# Quy Trình Tối Ưu Truy Vấn và Code trong Repository NestJS với TypeORM

Mục tiêu của chúng ta là làm cho các truy vấn nhanh nhất có thể, sử dụng tài nguyên hiệu quả và duy trì code sạch sẽ, d<PERSON> bảo trì theo đúng chuẩn mực đã đặt ra.

## Bước 1: Thiết Kế và Hiểu Sâu về Model Dữ Liệu (Entity) và Chỉ Mục (Indexes)

Trước khi viết bất kỳ dòng code truy vấn nào, việc hiểu rõ cấu trúc dữ liệu và cách nó được lưu trữ là cực kỳ quan trọng.

* **Nên làm:**
    * **Định nghĩa Entity rõ ràng:** <PERSON><PERSON><PERSON> bảo các `Entity` của bạn phản ánh chính xác cấu trúc bảng trong cơ sở dữ liệu. <PERSON><PERSON><PERSON> mối quan hệ (`<PERSON><PERSON><PERSON><PERSON><PERSON>`, `<PERSON>ToMany`, `ManyToOne`, `ManyToMany`) phải được định nghĩa chính xác.
    * **Sử dụng `lazy loading` và `eager loading` một cách có chủ đích:**
        * `eager: true` (trong định nghĩa quan hệ) sẽ tự động tải quan hệ mỗi khi entity chính được tải. Chỉ nên dùng khi bạn *luôn luôn* cần dữ liệu quan hệ đó.
        * Mặc định là `lazy loading`, dữ liệu quan hệ chỉ được tải khi bạn truy cập tường minh vào thuộc tính đó.
        * Trong Repository, chúng ta thường ưu tiên kiểm soát việc tải quan hệ bằng `leftJoinAndSelect` hơn là phụ thuộc vào `eager: true` toàn cục.
    * **Tận dụng chỉ mục (Database Indexes):**
        * Xác định các cột thường xuyên được sử dụng trong mệnh đề `WHERE`, `JOIN`, `ORDER BY`.
        * Đảm bảo các cột này đã được đánh chỉ mục trong cơ sở dữ liệu. Bạn có thể định nghĩa chỉ mục ngay trong Entity của TypeORM bằng decorator `@Index()`.
        ```typescript
        import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

        @Entity('products')
        @Index(['name', 'category']) // Chỉ mục kết hợp
        export class Product {
          @PrimaryGeneratedColumn('uuid')
          id: string;

          @Index() // Chỉ mục đơn
          @Column()
          name: string;

          @Column()
          category: string;

          @Column({ type: 'decimal' })
          price: number;
        }
        ```
* **Không nên làm:**
    * Bỏ qua việc đánh chỉ mục cho các cột hay được truy vấn.
    * Sử dụng `eager: true` một cách bừa bãi cho tất cả các quan hệ, điều này có thể dẫn đến việc tải thừa dữ liệu không cần thiết.

## Bước 2: Xây Dựng Truy Vấn Hiệu Quả với `SelectQueryBuilder`

Đây là trái tim của việc tối ưu trong Repository, theo đúng quy tắc bạn đặt ra.

* **Nên làm:**
    1.  **Luôn bắt đầu với `createBaseQuery()`:**
        * Điều này đảm bảo tính nhất quán và dễ dàng thêm các điều kiện chung (ví dụ: `isActive = true`) cho tất cả các truy vấn của một entity.
        ```typescript
        // trong xxx.repository.ts
        private createBaseQuery(): SelectQueryBuilder<AgentSystem> {
          return this.createQueryBuilder('agentSystem'); // 'agentSystem' là alias cho bảng chính
        }
        ```
    2.  **Chỉ chọn các cột cần thiết (`.select()` và `.addSelect()`):**
        * Thay vì lấy tất cả các cột của entity (`SELECT *`), hãy chỉ định rõ ràng những cột bạn cần. Điều này giảm lượng dữ liệu truyền tải giữa DB và ứng dụng, đặc biệt quan trọng với các bảng có nhiều cột hoặc cột TEXT/BLOB lớn.
        ```typescript
        async findActiveAgentNames(): Promise<{ id: string; name: string }[]> {
          this.logger.log('Lấy ID và tên các agent system đang hoạt động');
          const qb = this.createBaseQuery()
            .select(['agentSystem.id', 'agentSystem.name']) // Chỉ chọn cột id và name của agentSystem
            .where('agentSystem.isActive = :isActive', { isActive: true });

          return qb.getMany(); // Trả về mảng các object chỉ chứa id và name
        }
        ```
    3.  **Sử dụng `JOIN` một cách thông minh:**
        * **`leftJoinAndSelect(property, alias, condition?, parameters?)`:** Khi bạn cần lấy dữ liệu từ entity chính VÀ entity liên quan. TypeORM sẽ tự động map dữ liệu liên quan vào thuộc tính tương ứng. Rất hữu ích để tránh N+1 query.
        * **`innerJoinAndSelect(property, alias, condition?, parameters?)`:** Tương tự `leftJoinAndSelect` nhưng chỉ trả về kết quả nếu có sự khớp ở cả hai bảng (tương đương `INNER JOIN`).
        * **`leftJoin(property, alias, condition?, parameters?)` / `innerJoin(property, alias, condition?, parameters?)`:** Khi bạn cần `JOIN` để lọc (trong `WHERE`) hoặc sắp xếp (`ORDER BY`) dựa trên cột của bảng liên quan, nhưng KHÔNG cần lấy dữ liệu từ bảng đó.
        ```typescript
        async findAgentSystemWithActivePrimaryContact(agentSystemId: string): Promise<AgentSystem | null> {
          this.logger.log(`Tìm agent system ${agentSystemId} với liên hệ chính đang hoạt động`);
          const qb = this.createBaseQuery()
            .leftJoinAndSelect('agentSystem.primaryContact', 'contact') // Lấy cả dữ liệu của primaryContact
            .where('agentSystem.id = :agentSystemId', { agentSystemId })
            .andWhere('contact.isActive = :isActive', { isActive: true }); // Lọc dựa trên bảng contact

          return qb.getOne();
        }
        ```
    4.  **Điều kiện `WHERE` tối ưu:**
        * Đặt các điều kiện có khả năng lọc cao nhất (loại bỏ nhiều hàng nhất) lên trước nếu DB của bạn có cơ chế tối ưu thứ tự điều kiện.
        * Sử dụng tham số hóa (`:paramName`) để tránh SQL Injection và cho phép DB cache query plan. TypeORM làm điều này tự động khi bạn truyền object vào `.where()`, `.andWhere()`.
    5.  **Giải quyết vấn đề N+1 Query:**
        * **Vấn đề:** Khi bạn lấy một danh sách các entity (ví dụ: 100 `AgentSystem`), sau đó lặp qua từng `AgentSystem` để lấy `primaryContact` của nó. Điều này sẽ tạo ra 1 query đầu tiên + 100 query tiếp theo (N+1 query).
        * **Giải pháp:** Sử dụng `leftJoinAndSelect` để tải tất cả `primaryContact` cần thiết trong một query duy nhất.
        ```typescript
        // TRÁNH N+1:
        async findAllAgentSystemsWithPrimaryContacts(): Promise<AgentSystem[]> {
          this.logger.log('Lấy tất cả agent system cùng với liên hệ chính của chúng');
          const qb = this.createBaseQuery()
            .leftJoinAndSelect('agentSystem.primaryContact', 'contact') // Quan trọng!
            .where('contact.deleted_at IS NULL'); // Ví dụ thêm điều kiện cho bảng join

          return qb.getMany();
        }
        ```

* **Không nên làm:**
    * Không sử dụng `.select()` khi chỉ cần vài cột từ bảng có hàng chục cột.
    * Lấy entity cha, sau đó trong service lặp qua từng entity cha để gọi repository lấy entity con (gây N+1).
    * Viết các điều kiện `JOIN` phức tạp hoặc không cần thiết khi có thể giải quyết bằng quan hệ đã định nghĩa trong Entity và `leftJoinAndSelect('entity.relationProperty', 'alias')`.
    * Không xử lý trường hợp `null` hoặc `undefined` cho các tham số đầu vào trước khi đưa vào query builder, có thể gây lỗi hoặc kết quả không mong muốn.

## Bước 3: Tối ưu cho các Thao tác CUD (Create, Update, Delete)

Mặc dù quy tắc tập trung vào `SelectQueryBuilder`, các thao tác CUD cũng cần được tối ưu và thực hiện thông qua QueryBuilder của TypeORM thay vì raw SQL.

* **Nên làm:**
    1.  **Sử dụng `save()` cho Create và Update đơn lẻ (có kiểm tra):**
        * Phương thức `this.save(entityOrEntities)` là tiện lợi, nó sẽ tự động kiểm tra entity đã tồn tại (qua ID) để quyết định `INSERT` hay `UPDATE`. Tuy nhiên, nó sẽ tải entity trước khi update (nếu entity đã tồn tại và bạn không cung cấp đầy đủ) để thực hiện partial update.
    2.  **Sử dụng `createQueryBuilder().update()` cho cập nhật hàng loạt hiệu quả:**
        * Khi bạn cần cập nhật nhiều hàng dựa trên một điều kiện mà không cần tải chúng về ứng dụng.
        ```typescript
        async deactivateAgentsBySystemId(systemId: string): Promise<void> {
          this.logger.log(`Vô hiệu hóa tất cả agents thuộc về system ID: ${systemId}`);
          await this.createQueryBuilder('agent') // Sử dụng alias cho bảng cần update nếu khác với entity của repository
            .update(Agent) // Chỉ định Entity để TypeORM biết metadata
            .set({ isActive: false, updatedAt: new Date() })
            .where('systemId = :systemId', { systemId })
            .execute();
          // Lưu ý: .execute() không trả về các entity đã được cập nhật.
          // Nếu cần, bạn phải query lại.
        }
        ```
    3.  **Sử dụng `createQueryBuilder().delete()` cho xóa hàng loạt hiệu quả:**
        * Tương tự như update, xóa trực tiếp trong DB dựa trên điều kiện.
        ```typescript
        async deleteInactiveUsers(lastLoginThreshold: Date): Promise<void> {
          this.logger.log(`Xóa người dùng không hoạt động trước ngày: ${lastLoginThreshold}`);
          await this.createQueryBuilder('user')
            .delete()
            .from(User) // Chỉ định Entity
            .where('user.lastLoginAt < :lastLoginThreshold', { lastLoginThreshold })
            .andWhere('user.isProtected = :isProtected', {isProtected: false}) // Ví dụ thêm điều kiện an toàn
            .execute();
        }
        ```
    4.  **Sử dụng `insert()` cho chèn hàng loạt (Bulk Insert):**
        * Hiệu quả hơn việc gọi `save()` nhiều lần trong vòng lặp.
        ```typescript
        async bulkInsertProducts(productsDto: CreateProductDto[]): Promise<void> {
            this.logger.log(`Chèn hàng loạt ${productsDto.length} sản phẩm.`);
            // Giả sử productsDto đã được validate và sẵn sàng để chèn
            // productsDto có thể cần được map thành partial entities
            const productsToInsert = productsDto.map(dto => ({
                name: dto.name,
                category: dto.category,
                price: dto.price,
                // ... các trường khác, không cần id nếu là auto-generated
            }));

            await this.createQueryBuilder()
                .insert()
                .into(Product) // Entity
                .values(productsToInsert)
                .execute();
        }
        ```

* **Không nên làm:**
    * Sử dụng `this.save()` trong một vòng lặp lớn để cập nhật hoặc chèn nhiều bản ghi. Mỗi lần gọi `save` là một chuyến đi riêng đến DB.
    * Thực hiện "soft delete" (cập nhật cột `deletedAt`) bằng cách tải entity về, sửa trường `deletedAt` rồi `save()`, đặc biệt là cho nhiều bản ghi. Dùng `createQueryBuilder().update()` sẽ hiệu quả hơn.
    * **Tuyệt đối không dùng Raw SQL:** Như quy tắc `this.dataSource.query('DELETE ...')` là cấm. Luôn dùng các phương thức của `QueryBuilder`.

## Bước 4: Phân Trang (Pagination)

Khi truy vấn có khả năng trả về nhiều bản ghi, phân trang là bắt buộc.

* **Nên làm:**
    * Sử dụng `skip()` và `take()` (hoặc `limit()` và `offset()` tùy theo phương ngữ SQL, TypeORM sẽ tự điều chỉnh).
    ```typescript
    async findAllProductsPaginated(page: number, limit: number): Promise<[Product[], number]> {
      this.logger.log(`Lấy danh sách sản phẩm, trang ${page}, giới hạn ${limit}`);
      const qb = this.createBaseQuery()
        .orderBy('product.createdAt', 'DESC') // Luôn có ORDER BY khi phân trang để đảm bảo thứ tự nhất quán
        .skip((page - 1) * limit)
        .take(limit);

      return qb.getManyAndCount(); // Trả về cả danh sách và tổng số lượng bản ghi (trước khi phân trang)
    }
    ```
* **Không nên làm:**
    * Lấy toàn bộ hàng nghìn bản ghi về rồi mới xử lý phân trang ở tầng service hoặc client. Điều này cực kỳ tốn bộ nhớ và băng thông.

## Bước 5: Xử Lý `null`, `undefined` và Logging

Như quy tắc đã nêu, việc này quan trọng cho sự ổn định và dễ gỡ lỗi.

* **Nên làm:**
    * Kiểm tra các tham số đầu vào của phương thức repository. Nếu một tham số quan trọng là `null` hoặc `undefined` và nó sẽ gây lỗi query hoặc logic không mong muốn, hãy trả về `null` hoặc `[]` sớm.
    * Sử dụng `this.logger.log()` hoặc `this.logger.debug()` để ghi lại các thông tin hữu ích: tham số đầu vào, các bước chính trong query builder, số lượng kết quả.
    ```typescript
    async findProductById(id: string | null | undefined): Promise<Product | null> {
      if (!id) {
        this.logger.warn('ID sản phẩm không được cung cấp, không thể tìm kiếm.');
        return null;
      }
      this.logger.log(`Tìm sản phẩm với ID: ${id}`);
      const qb = this.createBaseQuery().where('product.id = :id', { id });
      return qb.getOne();
    }
    ```

## Bước 6: Luôn Sử Dụng Kiểu Trả Về Tường Minh

* **Nên làm:**
    * Định nghĩa rõ ràng kiểu trả về của Promise, ví dụ `Promise<User | null>`, `Promise<User[]>`, `Promise<{ id: string, name: string }[]>` thay vì `Promise<any>` hoặc `Promise<unknown>`. Điều này giúp TypeScript kiểm tra kiểu và tăng tính dễ đọc, dễ bảo trì.
* **Không nên làm:**
    * `async findSomething(): Promise<any> { ... }`

## Bước 7: Đánh giá và Đo Lường (Profiling)

Ngay cả khi bạn tuân theo tất cả các quy tắc, đôi khi một truy vấn vẫn có thể chậm.

* **Nên làm:**
    * Sử dụng công cụ của cơ sở dữ liệu (ví dụ `EXPLAIN ANALYZE` trong PostgreSQL) để xem kế hoạch thực thi của truy vấn được TypeORM tạo ra. Bạn có thể lấy SQL được tạo ra bằng cách:
        ```typescript
        const [sql, parameters] = qb.getQueryAndParameters();
        this.logger.debug(`SQL thực thi: ${sql}`);
        this.logger.debug(`Parameters: ${JSON.stringify(parameters)}`);
        ```
        Sau đó chạy SQL này trực tiếp trên DB với `EXPLAIN ANALYZE`.
    * Tìm kiếm các "узкие места" (bottlenecks) như full table scans, nested loop joins không hiệu quả, thiếu index.
    * Dựa vào kết quả phân tích, điều chỉnh lại `QueryBuilder` (ví dụ: thêm `.select()`, thay đổi cách `JOIN`, đảm bảo index được sử dụng).

## Ví dụ Tổng Hợp Repository Tối Ưu

```typescript
// File: user.repository.ts
import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { User } from './entities/user.entity'; // Giả sử có entity User
import { Profile } from '../profile/entities/profile.entity'; // Giả sử có entity Profile liên quan

interface UserWithProfileName {
  id: string;
  email: string;
  profileName: string | null; // Tên từ profile có thể null
}

@Injectable()
export class UserRepository extends Repository<User> {
  private readonly logger = new Logger(UserRepository.name);

  constructor(private dataSource: DataSource) {
    super(User, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho User.
   * @returns SelectQueryBuilder<User> để sử dụng trong các phương thức khác.
   */
  private createBaseQuery(): SelectQueryBuilder<User> {
    return this.createQueryBuilder('user') // 'user' là alias cho bảng User
      .where('user.deletedAt IS NULL'); // Ví dụ: luôn loại trừ các user đã bị soft delete
  }

  /**
   * Tìm người dùng bằng ID, kèm theo thông tin profile cơ bản.
   * Chỉ chọn các cột cần thiết để tối ưu.
   * @param id ID của người dùng
   * @returns Promise<User | null>
   */
  async findUserWithProfileById(id: string): Promise<User | null> {
    if (!id) {
      this.logger.warn('ID người dùng không được cung cấp.');
      return null;
    }
    this.logger.log(`Tìm người dùng với ID: ${id} kèm profile.`);

    const qb = this.createBaseQuery()
      .leftJoinAndSelect('user.profile', 'profile') // Lấy cả object profile
      .select([
        'user.id',
        'user.email',
        'user.createdAt',
        'profile.id', // Chỉ lấy các trường cần thiết từ profile
        'profile.firstName',
        'profile.lastName',
      ])
      .where('user.id = :id', { id });

    return qb.getOne();
  }

  /**
   * Lấy danh sách email và tên từ profile của người dùng đang hoạt động, có phân trang.
   * @param page Trang hiện tại
   * @param limit Số lượng mục trên mỗi trang
   * @returns Promise<[UserWithProfileName[], number]> Mảng người dùng và tổng số lượng
   */
  async findActiveUserEmailsWithProfileNames(
    page: number,
    limit: number,
  ): Promise<[UserWithProfileName[], number]> {
    this.logger.log(`Lấy email người dùng hoạt động, trang ${page}, giới hạn ${limit}`);

    const offset = (page > 0 ? page - 1 : 0) * limit;

    // Chú ý: .select() sẽ ghi đè các select trước đó từ createBaseQuery nếu không cẩn thận.
    // Nếu createBaseQuery có .select() riêng, bạn có thể cần .addSelect() ở đây.
    // Trong ví dụ này, createBaseQuery chỉ có .where() nên .select() là an toàn.
    const qb = this.createBaseQuery()
      .innerJoin('user.profile', 'profile') // Inner join vì chúng ta cần profileName
      .select([
        'user.id AS id', // Alias để khớp với interface
        'user.email AS email',
        'profile.firstName AS "profileName"', // Ví dụ tên profile là firstName
      ])
      .where('user.isActive = :isActive', { isActive: true })
      .orderBy('user.createdAt', 'DESC')
      .offset(offset)
      .limit(limit);

    // Vì .getRawMany() trả về dữ liệu thô, cần đảm bảo alias khớp với interface
    const rawResults = await qb.getRawMany<UserWithProfileName>();
    const totalCount = await qb.getCount(); // getCount() sẽ bỏ qua offset/limit để đếm tổng

    // Hoặc có thể dùng qb.getManyAndCount() nếu không cần custom select quá nhiều và muốn TypeORM tự map
    // nhưng khi dùng .select với alias cho raw query thì getRawMany/getRawOne là phù hợp.

    return [rawResults, totalCount];
  }

  /**
   * Cập nhật trạng thái cho nhiều người dùng dựa trên danh sách ID.
   * @param userIds Mảng ID của người dùng
   * @param isActive Trạng thái mới
   * @returns Promise<void>
   */
  async updateUserStatusByIds(userIds: string[], isActive: boolean): Promise<void> {
    if (!userIds || userIds.length === 0) {
      this.logger.warn('Không có ID người dùng nào được cung cấp để cập nhật trạng thái.');
      return;
    }
    this.logger.log(`Cập nhật trạng thái (isActive: ${isActive}) cho ${userIds.length} người dùng.`);

    await this.createQueryBuilder() // Không cần alias 'user' ở đây vì update() và from() sẽ xác định entity
      .update(User)
      .set({ isActive, updatedAt: new Date() })
      .whereInIds(userIds) // Tiện lợi cho việc cập nhật bằng mảng ID
      .execute();
  }
}