# Custom Field Swagger Examples

## Tổng quan

Tài liệu này cung cấp các mẫu đầu vào (examples) cho Swagger API documentation của Custom Field system.

## Request Examples cho từng DataType

### 1. TEXT Field

#### Request Body:
```json
{
  "fieldKey": "full_name",
  "displayName": "Họ và tên",
  "dataType": "text",
  "description": "Họ và tên đầy đủ của khách hàng",
  "tags": ["personal", "required"],
  "config": {
    "placeholder": "Nhập họ và tên đầy đủ...",
    "defaultValue": "",
    "pattern": "^[a-zA-ZÀ-ỹ\\s]+$",
    "minLength": 2,
    "maxLength": 100
  }
}
```

#### Success Response:
```json
{
  "success": true,
  "message": "Tạo trường tùy chỉnh thành công",
  "data": {
    "id": 1,
    "fieldKey": "full_name",
    "userId": 123,
    "displayName": "Họ và tên",
    "dataType": "text",
    "description": "Họ và tên đầy đủ của khách hàng",
    "tags": ["personal", "required"],
    "config": {
      "placeholder": "Nhập họ và tên đầy đủ...",
      "defaultValue": "",
      "pattern": "^[a-zA-ZÀ-ỹ\\s]+$",
      "minLength": 2,
      "maxLength": 100
    }
  }
}
```

### 2. NUMBER Field

#### Request Body:
```json
{
  "fieldKey": "age",
  "displayName": "Tuổi",
  "dataType": "number",
  "description": "Tuổi của khách hàng",
  "config": {
    "placeholder": "Nhập tuổi...",
    "defaultValue": 18,
    "minValue": 0,
    "maxValue": 120
  }
}
```

### 3. BOOLEAN Field

#### Request Body:
```json
{
  "fieldKey": "is_married",
  "displayName": "Đã kết hôn",
  "dataType": "boolean",
  "config": {
    "placeholder": "Tình trạng hôn nhân",
    "defaultValue": false
  }
}
```

### 4. DATE Field

#### Request Body:
```json
{
  "fieldKey": "birth_date",
  "displayName": "Ngày sinh",
  "dataType": "date",
  "config": {
    "placeholder": "Chọn ngày sinh...",
    "defaultValue": "2000-01-01"
  }
}
```

### 5. SELECT Field

#### Request Body:
```json
{
  "fieldKey": "gender",
  "displayName": "Giới tính",
  "dataType": "select",
  "config": {
    "placeholder": "Chọn giới tính...",
    "options": [
      { "title": "Nam", "value": "male" },
      { "title": "Nữ", "value": "female" },
      { "title": "Khác", "value": "other" }
    ],
    "defaultValue": "male"
  }
}
```

### 6. OBJECT Field

#### Request Body:
```json
{
  "fieldKey": "address",
  "displayName": "Địa chỉ",
  "dataType": "object",
  "config": {
    "placeholder": "Nhập thông tin địa chỉ...",
    "defaultValue": {
      "street": "",
      "ward": "",
      "district": "",
      "city": "",
      "country": "Vietnam"
    }
  }
}
```

## Error Examples

### 1. Invalid TEXT Config

#### Request Body:
```json
{
  "fieldKey": "invalid_text",
  "displayName": "Invalid Text",
  "dataType": "text",
  "config": {
    "minLength": -1,
    "maxLength": 0
  }
}
```

#### Error Response:
```json
{
  "success": false,
  "message": "Config không hợp lệ cho dataType đã chọn",
  "errors": [
    "Độ dài tối thiểu không được nhỏ hơn 0",
    "Độ dài tối đa không được nhỏ hơn 1"
  ],
  "dataType": "text",
  "receivedConfig": {
    "minLength": -1,
    "maxLength": 0
  }
}
```

### 2. Invalid SELECT Config

#### Request Body:
```json
{
  "fieldKey": "invalid_select",
  "displayName": "Invalid Select",
  "dataType": "select",
  "config": {
    "options": [],
    "defaultValue": "invalid_value"
  }
}
```

#### Error Response:
```json
{
  "success": false,
  "message": "Config không hợp lệ cho dataType đã chọn",
  "errors": [
    "Phải có ít nhất 1 tùy chọn",
    "Giá trị mặc định phải là một trong các value trong options"
  ],
  "dataType": "select",
  "receivedConfig": {
    "options": [],
    "defaultValue": "invalid_value"
  }
}
```

## Real-world Examples

### Email Field
```json
{
  "fieldKey": "email",
  "displayName": "Email",
  "dataType": "text",
  "description": "Địa chỉ email của khách hàng",
  "tags": ["contact", "required"],
  "config": {
    "placeholder": "Nhập địa chỉ email...",
    "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
    "maxLength": 255
  }
}
```

### Phone Field
```json
{
  "fieldKey": "phone",
  "displayName": "Số điện thoại",
  "dataType": "text",
  "description": "Số điện thoại liên hệ",
  "tags": ["contact"],
  "config": {
    "placeholder": "Nhập số điện thoại (VD: 0912345678)",
    "pattern": "^(0|\\+84)[0-9]{9,10}$",
    "minLength": 10,
    "maxLength": 12
  }
}
```

### Education Level Field
```json
{
  "fieldKey": "education",
  "displayName": "Trình độ học vấn",
  "dataType": "select",
  "description": "Trình độ học vấn cao nhất",
  "tags": ["education"],
  "config": {
    "placeholder": "Chọn trình độ học vấn...",
    "options": [
      { "title": "Tiểu học", "value": "primary" },
      { "title": "Trung học cơ sở", "value": "secondary" },
      { "title": "Trung học phổ thông", "value": "high_school" },
      { "title": "Cao đẳng", "value": "college" },
      { "title": "Đại học", "value": "university" },
      { "title": "Thạc sĩ", "value": "master" },
      { "title": "Tiến sĩ", "value": "phd" }
    ]
  }
}
```

### Company Info Field
```json
{
  "fieldKey": "company_info",
  "displayName": "Thông tin công ty",
  "dataType": "object",
  "description": "Thông tin chi tiết về công ty",
  "tags": ["business"],
  "config": {
    "placeholder": "Nhập thông tin công ty...",
    "defaultValue": {
      "name": "",
      "taxCode": "",
      "address": "",
      "phone": "",
      "email": "",
      "website": ""
    }
  }
}
```

## Update Examples

### 1. Update TEXT Field

#### Request Body:
```json
{
  "displayName": "Họ và tên đầy đủ",
  "description": "Họ và tên đầy đủ của khách hàng (đã cập nhật)",
  "config": {
    "placeholder": "Nhập họ và tên đầy đủ...",
    "pattern": "^[a-zA-ZÀ-ỹ\\s]+$",
    "minLength": 3,
    "maxLength": 150
  }
}
```

#### Success Response:
```json
{
  "success": true,
  "message": "Cập nhật trường tùy chỉnh thành công",
  "data": {
    "id": 1,
    "fieldKey": "full_name",
    "userId": 123,
    "displayName": "Họ và tên đầy đủ",
    "dataType": "text",
    "description": "Họ và tên đầy đủ của khách hàng (đã cập nhật)",
    "tags": ["personal", "required", "updated"],
    "config": {
      "placeholder": "Nhập họ và tên đầy đủ...",
      "pattern": "^[a-zA-ZÀ-ỹ\\s]+$",
      "minLength": 3,
      "maxLength": 150
    },
    "updatedAt": 1703123456
  }
}
```

### 2. Update SELECT Field - Add Options

#### Request Body:
```json
{
  "displayName": "Giới tính",
  "config": {
    "placeholder": "Chọn giới tính...",
    "options": [
      { "title": "Nam", "value": "male" },
      { "title": "Nữ", "value": "female" },
      { "title": "Khác", "value": "other" },
      { "title": "Không muốn tiết lộ", "value": "prefer_not_to_say" }
    ],
    "defaultValue": "prefer_not_to_say"
  }
}
```

### 3. Change DataType from TEXT to SELECT

#### Request Body:
```json
{
  "dataType": "select",
  "config": {
    "placeholder": "Chọn trình độ học vấn...",
    "options": [
      { "title": "Tiểu học", "value": "primary" },
      { "title": "Trung học cơ sở", "value": "secondary" },
      { "title": "Trung học phổ thông", "value": "high_school" },
      { "title": "Cao đẳng", "value": "college" },
      { "title": "Đại học", "value": "university" }
    ]
  }
}
```

### 4. Admin Update Examples

#### Update Customer Type SELECT:
```json
{
  "displayName": "Loại khách hàng",
  "description": "Phân loại khách hàng theo tính chất (đã cập nhật)",
  "config": {
    "placeholder": "Chọn loại khách hàng...",
    "options": [
      { "title": "Cá nhân", "value": "individual" },
      { "title": "Doanh nghiệp nhỏ", "value": "small_business" },
      { "title": "Doanh nghiệp vừa", "value": "medium_business" },
      { "title": "Doanh nghiệp lớn", "value": "large_business" },
      { "title": "Đối tác chiến lược", "value": "strategic_partner" },
      { "title": "Nhà cung cấp", "value": "supplier" }
    ],
    "defaultValue": "individual"
  }
}
```

#### Update Company Revenue NUMBER:
```json
{
  "displayName": "Doanh thu công ty (VND)",
  "description": "Doanh thu hàng năm của công ty",
  "config": {
    "placeholder": "Nhập doanh thu hàng năm...",
    "defaultValue": 1000000000,
    "minValue": 0,
    "maxValue": 1000000000000
  }
}
```

## Config Examples Endpoint Response

### GET /config-examples Response:
```json
{
  "success": true,
  "message": "Lấy config examples thành công",
  "data": {
    "text": {
      "placeholder": "Nhập họ tên...",
      "defaultValue": "",
      "pattern": "^[a-zA-ZÀ-ỹ\\s]+$",
      "minLength": 2,
      "maxLength": 100
    },
    "number": {
      "placeholder": "Nhập tuổi...",
      "defaultValue": 18,
      "minValue": 0,
      "maxValue": 120
    },
    "boolean": {
      "placeholder": "Đồng ý điều khoản",
      "defaultValue": false
    },
    "date": {
      "placeholder": "Chọn ngày sinh...",
      "defaultValue": "2000-01-01"
    },
    "select": {
      "placeholder": "Chọn giới tính...",
      "options": [
        { "title": "Nam", "value": "male" },
        { "title": "Nữ", "value": "female" },
        { "title": "Khác", "value": "other" }
      ],
      "defaultValue": "male"
    },
    "object": {
      "placeholder": "Nhập thông tin địa chỉ...",
      "defaultValue": {
        "street": "",
        "city": "",
        "country": "Vietnam"
      }
    }
  }
}
```
