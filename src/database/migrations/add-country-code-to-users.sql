-- Migration: Add country_code field to users table
-- Date: 2024-12-19
-- Description: Thêm trường country_code vào bảng users để lưu mã quốc gia của số điện thoại

-- Thêm cột country_code vào bảng users
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS country_code VARCHAR(10) DEFAULT '+84';

-- Thêm comment cho cột
COMMENT ON COLUMN users.country_code 
IS 'Mã quốc gia của số điện thoại người dùng (ví dụ: +84, +1, +86)';

-- C<PERSON><PERSON> nhật dữ liệu hiện có với giá trị mặc định
UPDATE users 
SET country_code = '+84' 
WHERE country_code IS NULL;

-- <PERSON><PERSON><PERSON> tra kết quả
SELECT 
    column_name, 
    data_type, 
    column_default, 
    is_nullable,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name = 'country_code';
