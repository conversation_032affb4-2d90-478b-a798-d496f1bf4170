import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { TaskExecutionStatus } from '../enums/task-execution-status.enum';

/**
 * Entity đại diện cho bảng user_task_executions trong cơ sở dữ liệu
 * B<PERSON>ng lưu lịch sử thực thi của nhiệm vụ, bao gồm log chi tiết từng bước dưới dạng JSON
 */
@Entity('user_task_executions')
export class UserTaskExecution {
  /**
   * Khóa chính của bảng user_task_executions, sử dụng UUID
   */
  @PrimaryGeneratedColumn('uuid', { name: 'task_execution_id' })
  taskExecutionId: string;

  /**
   * ID của nhiệm vụ, tham chiếu đến bảng user_tasks
   */
  @Column({ name: 'task_id', type: 'uuid', nullable: false })
  taskId: string;

  /**
   * Thời gian bắt đầu phiên thực thi (Unix epoch)
   */
  @Column({ name: 'start_time', type: 'bigint', nullable: false })
  startTime: number;

  /**
   * Thời gian kết thúc phiên thực thi (Unix epoch)
   */
  @Column({ name: 'end_time', type: 'bigint', nullable: true })
  endTime: number;

  /**
   * Trạng thái tổng thể của phiên thực thi (success, fail, ...)
   */
  @Column({
    name: 'overall_status',
    type: 'enum',
    enum: TaskExecutionStatus,
    default: TaskExecutionStatus.RUNNING,
    nullable: false,
  })
  overallStatus: TaskExecutionStatus;

  /**
   * Log chi tiết của từng bước trong phiên thực thi, lưu dưới dạng mảng JSON
   */
  @Column({ name: 'execution_details', type: 'jsonb', nullable: true })
  executionDetails: Record<string, any>;

  /**
   * Thời điểm ghi log phiên thực thi (Unix epoch)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  createdAt: number;
}
