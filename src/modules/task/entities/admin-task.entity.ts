import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng admin_tasks trong cơ sở dữ liệu
 * Bảng lưu thông tin định nghĩa của nhiệm vụ
 */
@Entity('admin_tasks')
export class AdminTask {
  /**
   * Khóa chính của bảng admin_tasks, sử dụng UUID
   */
  @PrimaryGeneratedColumn('uuid', { name: 'task_id' })
  taskId: string;

  /**
   * ID của agent (tham chiếu đến bảng agents)
   */
  @Column({ name: 'agent_id', type: 'uuid', nullable: false })
  agentId: string;

  /**
   * Tên của nhiệm vụ
   */
  @Column({ name: 'task_name', type: 'varchar', length: 255, nullable: false })
  taskName: string;

  /**
   * <PERSON><PERSON> tả chi tiết nhiệm vụ
   */
  @Column({ name: 'task_description', type: 'text', nullable: true })
  taskDescription: string;

  /**
   * Trạng thái hoạt động của nhiệm vụ
   */
  @Column({ name: 'active', type: 'boolean', default: true })
  active: boolean;

  /**
   * ID của nhân viên tạo nhiệm vụ
   */
  @Column({ name: 'created_by', type: 'int', nullable: true })
  createdBy: number;

  /**
   * ID của nhân viên cập nhật nhiệm vụ
   */
  @Column({ name: 'updated_by', type: 'int', nullable: true })
  updatedBy: number;

  /**
   * ID của nhân viên xóa nhiệm vụ
   */
  @Column({ name: 'deleted_by', type: 'int', nullable: true })
  deletedBy: number;

  /**
   * Thời điểm tạo nhiệm vụ (Unix epoch)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật nhiệm vụ (Unix epoch)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint',
  })
  updatedAt: number;

  /**
   * Thời điểm xóa nhiệm vụ (Unix epoch) - soft delete
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number;
}
