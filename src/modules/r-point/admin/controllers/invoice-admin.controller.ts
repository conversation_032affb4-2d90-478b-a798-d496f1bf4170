import { Controller, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { InvoiceUserService } from '@modules/r-point/user/services';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';

@ApiTags('R-Point - Admin Invoices')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/r-point/invoices')
export class InvoiceAdminController {
  constructor(private readonly invoiceUserService: InvoiceUserService) {}
}
