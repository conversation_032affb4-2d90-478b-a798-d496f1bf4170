import { ApiProperty } from '@nestjs/swagger';
import { TransactionStatus } from '@modules/r-point/enums';

/**
 * DTO cho việc trả về thông tin giao dịch mua R-Point
 */
export class PurchasePointResponseDto {
  @ApiProperty({
    description: 'ID của giao dịch',
    example: 123456
  })
  id: number;

  @ApiProperty({
    description: 'ID của người dùng',
    example: 1
  })
  userId: number;

  @ApiProperty({
    description: 'Số tiền giao dịch',
    example: 100000
  })
  amount: number;

  @ApiProperty({
    description: 'Số lượng R-Point mua',
    example: 100
  })
  pointsAmount: number;

  @ApiProperty({
    description: 'Tên gói R-Point',
    example: 'Gói 100k'
  })
  pointName: string;

  @ApiProperty({
    description: 'ID của gói R-Point',
    example: 1
  })
  pointId: number;

  @ApiProperty({
    description: 'Trạng thái giao dịch',
    enum: TransactionStatus,
    example: TransactionStatus.PENDING
  })
  status: TransactionStatus;

  @ApiProperty({
    description: 'URL QR code thanh toán',
    example: 'https://qr.sepay.vn/img?bank=VCB&acc=**********&template=compact&amount=100000.00&des=REDAI123456SEPAY'
  })
  qrCodeUrl: string;

  @ApiProperty({
    description: 'Thời gian tạo giao dịch',
    example: *************
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật giao dịch',
    example: *************
  })
  updatedAt: number;

  @ApiProperty({
    description: 'Mô tả giao dịch',
    example: 'REDAI123456SEPAY'
  })
  description?: string;

  @ApiProperty({
    description: 'Số tiền gốc trước khi áp dụng mã giảm giá',
    example: 120000
  })
  originalAmount?: number;
}
