import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '@shared/services/redis.service';
import { TransactionStatus } from '@modules/r-point/enums';
import { PurchasePointResponseDto } from '../dto';

/**
 * Service xử lý lưu trữ và kiểm tra trạng thái đơn hàng trong Redis
 */
@Injectable()
export class TransactionRedisService {
  private readonly logger = new Logger(TransactionRedisService.name);
  private readonly transactionPrefix = 'r-point:transaction:';
  private readonly expiryTimeInSeconds = 30 * 60; // 30 phút

  constructor(private readonly redisService: RedisService) {}

  /**
   * Lưu thông tin giao dịch vào Redis với thời gian sống 30 phút
   * @param transactionId ID của giao dịch
   * @param transactionData Dữ liệu giao dịch
   */
  async saveTransactionStatus(
    transactionId: number,
    transactionData: PurchasePointResponseDto,
  ): Promise<void> {
    try {
      const key = `${this.transactionPrefix}${transactionId}`;
      await this.redisService.setWithExpiry(
        key,
        JSON.stringify(transactionData),
        this.expiryTimeInSeconds,
      );
      this.logger.debug(
        `Đã lưu trạng thái giao dịch ${transactionId} vào Redis`,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi lưu trạng thái giao dịch ${transactionId} vào Redis: ${error.message}`,
        error.stack,
      );
      // Không throw lỗi để không ảnh hưởng đến luồng chính
    }
  }

  /**
   * Cập nhật trạng thái giao dịch trong Redis
   * @param transactionId ID của giao dịch
   * @param status Trạng thái mới
   */
  async updateTransactionStatus(
    transactionId: number,
    status: TransactionStatus,
  ): Promise<void> {
    try {
      const key = `${this.transactionPrefix}${transactionId}`;
      const transactionDataStr = await this.redisService.get(key);

      if (transactionDataStr) {
        const transactionData = JSON.parse(transactionDataStr) as PurchasePointResponseDto;
        transactionData.status = status;
        transactionData.updatedAt = Math.floor(Date.now() / 1000);

        await this.redisService.setWithExpiry(
          key,
          JSON.stringify(transactionData),
          this.expiryTimeInSeconds,
        );
        this.logger.debug(
          `Đã cập nhật trạng thái giao dịch ${transactionId} thành ${status} trong Redis`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật trạng thái giao dịch ${transactionId} trong Redis: ${error.message}`,
        error.stack,
      );
      // Không throw lỗi để không ảnh hưởng đến luồng chính
    }
  }

  /**
   * Kiểm tra trạng thái giao dịch từ Redis
   * @param transactionId ID của giao dịch
   * @returns Thông tin giao dịch hoặc null nếu không tìm thấy
   */
  async getTransactionStatus(
    transactionId: number,
  ): Promise<PurchasePointResponseDto | null> {
    try {
      const key = `${this.transactionPrefix}${transactionId}`;
      const transactionDataStr = await this.redisService.get(key);

      if (transactionDataStr) {
        return JSON.parse(transactionDataStr) as PurchasePointResponseDto;
      }

      return null;
    } catch (error) {
      this.logger.error(
        `Lỗi khi kiểm tra trạng thái giao dịch ${transactionId} từ Redis: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * Xóa thông tin giao dịch khỏi Redis
   * @param transactionId ID của giao dịch
   */
  async deleteTransactionStatus(transactionId: number): Promise<void> {
    try {
      const key = `${this.transactionPrefix}${transactionId}`;
      await this.redisService.del(key);
      this.logger.debug(
        `Đã xóa thông tin giao dịch ${transactionId} khỏi Redis`,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa thông tin giao dịch ${transactionId} khỏi Redis: ${error.message}`,
        error.stack,
      );
      // Không throw lỗi để không ảnh hưởng đến luồng chính
    }
  }
}
