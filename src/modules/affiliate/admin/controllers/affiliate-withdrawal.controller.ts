import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { AffiliateWithdrawalService } from '@modules/affiliate/admin/services';
import { AffiliateWithdrawalDto, AffiliateWithdrawalQueryDto, AffiliateWithdrawalStatisticsDto } from '../dto';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { SWAGGER_API_TAGS } from '@/common/swagger';

@Controller('admin/affiliate/withdrawals')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.ADMIN_AFFILIATE_WITHDRAWAL)
@ApiExtraModels(ApiResponseDto, AffiliateWithdrawalDto)
export class AffiliateWithdrawalController {
  constructor(
    private readonly affiliateWithdrawalService: AffiliateWithdrawalService,
  ) {}

  /**
   * Lấy danh sách yêu cầu rút tiền
   * @param queryDto Tham số truy vấn
   * @returns Danh sách yêu cầu rút tiền với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách yêu cầu rút tiền' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách yêu cầu rút tiền thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'object',
              properties: {
                items: {
                  type: 'array',
                  items: { $ref: getSchemaPath(AffiliateWithdrawalDto) },
                },
                meta: {
                  type: 'object',
                  properties: {
                    totalItems: { type: 'number' },
                    itemCount: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    currentPage: { type: 'number' },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi lấy danh sách yêu cầu rút tiền',
  })
  async getWithdrawals(
    @Query() queryDto: AffiliateWithdrawalQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AffiliateWithdrawalDto>>> {
    const withdrawals =
      await this.affiliateWithdrawalService.getWithdrawals(queryDto);
    return ApiResponseDto.success(
      withdrawals,
      'Lấy danh sách yêu cầu rút tiền thành công',
    );
  }

  /**
   * Lấy thông tin chi tiết yêu cầu rút tiền
   * @param id ID của yêu cầu rút tiền
   * @returns Thông tin chi tiết yêu cầu rút tiền
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết yêu cầu rút tiền' })
  @ApiParam({
    name: 'id',
    description: 'ID của yêu cầu rút tiền',
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin chi tiết yêu cầu rút tiền thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(AffiliateWithdrawalDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy yêu cầu rút tiền',
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi lấy thông tin chi tiết yêu cầu rút tiền',
  })
  async getWithdrawalById(
    @Param('id') id: number,
  ): Promise<ApiResponseDto<AffiliateWithdrawalDto>> {
    const withdrawal = await this.affiliateWithdrawalService.getWithdrawalById(id);
    return ApiResponseDto.success(
      withdrawal,
      'Lấy thông tin chi tiết yêu cầu rút tiền thành công',
    );
  }

  /**
   * Phê duyệt yêu cầu rút tiền
   * @param id ID của yêu cầu rút tiền
   * @param employee Thông tin nhân viên
   * @returns Thông tin yêu cầu rút tiền đã phê duyệt
   */
  @Patch(':id/approve')
  @ApiOperation({ summary: 'Phê duyệt yêu cầu rút tiền' })
  @ApiParam({
    name: 'id',
    description: 'ID của yêu cầu rút tiền',
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Phê duyệt yêu cầu rút tiền thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(AffiliateWithdrawalDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy yêu cầu rút tiền',
  })
  @ApiResponse({
    status: 400,
    description: 'Chỉ có thể phê duyệt yêu cầu đang ở trạng thái chờ duyệt',
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi phê duyệt yêu cầu rút tiền',
  })
  async approveWithdrawal(
    @Param('id') id: number,
    @CurrentEmployee() employee: JwtPayload,
  ): Promise<ApiResponseDto<AffiliateWithdrawalDto>> {
    const withdrawal = await this.affiliateWithdrawalService.approveWithdrawal(
      id,
      employee.id,
    );
    return ApiResponseDto.success(
      withdrawal,
      'Phê duyệt yêu cầu rút tiền thành công',
    );
  }

  /**
   * Từ chối yêu cầu rút tiền
   * @param id ID của yêu cầu rút tiền
   * @param employee Thông tin nhân viên
   * @param body Thông tin từ chối
   * @returns Thông tin yêu cầu rút tiền đã từ chối
   */
  @Patch(':id/reject')
  @ApiOperation({ summary: 'Từ chối yêu cầu rút tiền' })
  @ApiParam({
    name: 'id',
    description: 'ID của yêu cầu rút tiền',
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Từ chối yêu cầu rút tiền thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(AffiliateWithdrawalDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy yêu cầu rút tiền',
  })
  @ApiResponse({
    status: 400,
    description: 'Chỉ có thể từ chối yêu cầu đang ở trạng thái chờ duyệt',
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi từ chối yêu cầu rút tiền',
  })
  async rejectWithdrawal(
    @Param('id') id: number,
    @CurrentEmployee() employee: JwtPayload,
    @Body() body: { rejectReason: string },
  ): Promise<ApiResponseDto<AffiliateWithdrawalDto>> {
    const withdrawal = await this.affiliateWithdrawalService.rejectWithdrawal(
      id,
      employee.id,
      body.rejectReason,
    );
    return ApiResponseDto.success(
      withdrawal,
      'Từ chối yêu cầu rút tiền thành công',
    );
  }

  /**
   * Lấy thống kê về yêu cầu rút tiền
   */
  @Get('statistics')
  @ApiOperation({ summary: 'Lấy thống kê về yêu cầu rút tiền' })
  @ApiQuery({
    name: 'begin',
    description: 'Thời gian bắt đầu thống kê (Unix timestamp)',
    required: false,
    type: 'number',
    example: 1672531200
  })
  @ApiQuery({
    name: 'end',
    description: 'Thời gian kết thúc thống kê (Unix timestamp)',
    required: false,
    type: 'number',
    example: 1675209600
  })
  @ApiQuery({
    name: 'interval',
    description: 'Khoảng thời gian cho xu hướng (day, month, year)',
    required: false,
    type: 'string',
    example: 'month'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê về yêu cầu rút tiền thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(AffiliateWithdrawalStatisticsDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi lấy thống kê về yêu cầu rút tiền',
  })
  async getStatistics(
    @Query('begin') begin?: number,
    @Query('end') end?: number,
    @Query('interval') interval?: 'day' | 'month' | 'year',
  ): Promise<ApiResponseDto<AffiliateWithdrawalStatisticsDto>> {
    const statistics = await this.affiliateWithdrawalService.getStatistics(
      begin,
      end,
      interval,
    );
    return ApiResponseDto.success(
      statistics,
      'Lấy thống kê về yêu cầu rút tiền thành công',
    );
  }
}
