import { Controller, Get, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { ApiResponseDto } from '@/common/response';
import { AffiliateOverviewService } from '@modules/affiliate/admin/services';
import { AffiliateOverviewDto } from '@modules/affiliate/admin/dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';

@Controller('admin/affiliate/overview')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.ADMIN_AFFILIATE_OVERVIEW)
@ApiExtraModels(ApiResponseDto, AffiliateOverviewDto)
export class AffiliateOverviewController {
  constructor(
    private readonly affiliateOverviewService: AffiliateOverviewService,
  ) {}

  /**
   * <PERSON><PERSON>y thông tin tổng quan về affiliate
   * @returns Thông tin tổng quan
   */
  @Get()
  @ApiOperation({ 
    summary: 'Lấy thông tin tổng quan về affiliate',
    description: 'API này trả về thông tin tổng quan về affiliate, bao gồm: tổng số Publisher (tài khoản affiliate), tổng số cấp bậc (Rank), tổng số đơn hàng, và tổng số lần chuyển đổi điểm' 
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin tổng quan thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(AffiliateOverviewDto) },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi lấy thông tin thống kê affiliate',
  })
  async getOverview(): Promise<ApiResponseDto<AffiliateOverviewDto>> {
    const overview = await this.affiliateOverviewService.getOverview();
    return ApiResponseDto.success(
      overview,
      'Lấy thông tin tổng quan thành công',
    );
  }
}
