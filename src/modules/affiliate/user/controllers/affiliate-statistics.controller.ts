import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JWTPayload } from '@modules/auth/interfaces';
import { AffiliateStatisticsService } from '../services/affiliate-statistics.service';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { AffiliateStatisticsQueryDto, AffiliateStatisticsDto, AffiliateUserOverviewDto } from '../dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';

@Controller('user/affiliate/statistics')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags(SWAGGER_API_TAGS.USER_AFFILIATE_STATISTICS)
@ApiExtraModels(ApiResponseDto, AffiliateStatisticsDto, AffiliateUserOverviewDto)
export class AffiliateStatisticsController {
  constructor(
    private readonly affiliateStatisticsService: AffiliateStatisticsService,
  ) {}

  /**
   * Lấy thông tin thống kê tài khoản affiliate
   * @param user Thông tin người dùng hiện tại
   * @param queryDto Tham số truy vấn
   * @returns Thông tin thống kê tài khoản affiliate
   */
  @Get()
  @ApiOperation({ summary: 'Lấy thông tin thống kê tài khoản affiliate' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê thành công',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Lấy thống kê thành công' },
        result: {
          type: 'object',
          properties: {
            totalClicks: { type: 'number', example: 120 },
            totalOrders: { type: 'number', example: 15 },
            totalEarnings: { type: 'number', example: 1500000 },
            totalCommission: { type: 'number', example: 750000 },
            conversionRate: { type: 'number', example: 12.5 },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy tài khoản affiliate',
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi khi lấy thông tin thống kê',
  })
  async getStatistics(
    @CurrentUser() user: JWTPayload,
    @Query() queryDto: AffiliateStatisticsQueryDto,
  ): Promise<ApiResponseDto<AffiliateStatisticsDto>> {
    const statistics = await this.affiliateStatisticsService.getStatistics(
      Number(user.id),
      queryDto,
    );
    return ApiResponseDto.success(
      statistics,
      'Lấy thông tin thống kê thành công',
    );
  }

  /**
   * Lấy thông tin tổng quan về affiliate
   * @returns Thông tin tổng quan
   */
  @Get('overview')
  @ApiOperation({ 
    summary: 'Lấy thông tin tổng quan về affiliate',
    description: 'API này trả về thông tin tổng quan về affiliate, bao gồm: tổng số Publisher (tài khoản affiliate), tổng số cấp bậc (Rank), tổng số đơn hàng, và tổng số lần chuyển đổi điểm'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin tổng quan thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: { $ref: getSchemaPath(AffiliateUserOverviewDto) },
          },
        },
      ],
    },
  })
  async getOverview(): Promise<ApiResponseDto<AffiliateUserOverviewDto>> {
    const overview = await this.affiliateStatisticsService.getOverview();
    return ApiResponseDto.success(overview, 'Lấy thông tin tổng quan thành công');
  }
}
