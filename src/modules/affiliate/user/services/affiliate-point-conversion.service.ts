import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { AffiliateAccountRepository } from '@modules/affiliate/repositories/affiliate-account.repository';
import { AffiliatePointConversionHistoryRepository } from '@modules/affiliate/repositories/affiliate-point-conversion-history.repository';
import {
  AffiliatePointConversionQueryDto,
  AffiliatePointConversionDto,
  ConvertToPointsRequestDto,
  ConvertToPointsResponseDto
} from '../dto';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';
import { Transactional } from 'typeorm-transactional';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { DataSource } from 'typeorm';
import { User } from '@modules/user/entities/user.entity';
import { PointConversionStatus } from '@modules/affiliate/enums';

@Injectable()
export class AffiliatePointConversionService {
  private readonly logger = new Logger(AffiliatePointConversionService.name);

  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly affiliatePointConversionHistoryRepository: AffiliatePointConversionHistoryRepository,
    private readonly userRepository: UserRepository,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Lấy danh sách lịch sử chuyển đổi điểm
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách lịch sử chuyển đổi điểm với phân trang
   */
  @Transactional()
  async getPointConversions(
    userId: number,
    queryDto: AffiliatePointConversionQueryDto,
  ): Promise<PaginatedResult<AffiliatePointConversionDto>> {
    try {
      // Lấy tài khoản affiliate của người dùng
      const affiliateAccount =
        await this.affiliateAccountRepository.findByUserId(userId);

      if (!affiliateAccount) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản affiliate',
        );
      }

      // Lấy danh sách lịch sử chuyển đổi điểm với phân trang
      const { items, meta } =
        await this.affiliatePointConversionHistoryRepository.findWithPagination(
          affiliateAccount.id,
          queryDto,
        );

      // Xử lý dữ liệu trả về
      const conversionDtos = items.map((conversion) => ({
        id: conversion.id,
        pointsConverted: conversion.pointsConverted,
        conversionRate: conversion.conversionRate,
        amount: conversion.amount,
        createdAt: conversion.createdAt,
        status: conversion.status,
      }));

      return {
        items: conversionDtos,
        meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting point conversions: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy danh sách lịch sử chuyển đổi điểm',
      );
    }
  }

  /**
   * Lấy chi tiết lịch sử chuyển đổi điểm
   * @param userId ID của người dùng
   * @param conversionId ID của bản ghi chuyển đổi
   * @returns Thông tin chi tiết lịch sử chuyển đổi điểm
   */
  @Transactional()
  async getPointConversionById(
    userId: number,
    conversionId: number,
  ): Promise<AffiliatePointConversionDto> {
    try {
      // Lấy tài khoản affiliate của người dùng
      const affiliateAccount =
        await this.affiliateAccountRepository.findByUserId(userId);

      if (!affiliateAccount) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản affiliate',
        );
      }

      // Lấy thông tin chi tiết lịch sử chuyển đổi điểm
      const conversion =
        await this.affiliatePointConversionHistoryRepository.findById(
          conversionId,
        );

      if (!conversion) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.POINT_CONVERSION_NOT_FOUND,
          'Không tìm thấy lịch sử chuyển đổi điểm',
        );
      }

      // Kiểm tra xem lịch sử chuyển đổi có thuộc về tài khoản affiliate này không
      if (conversion.affiliateAccountId !== affiliateAccount.id) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Bạn không có quyền truy cập lịch sử chuyển đổi điểm này',
        );
      }

      // Xử lý dữ liệu trả về
      return {
        id: conversion.id,
        pointsConverted: conversion.pointsConverted,
        conversionRate: conversion.conversionRate,
        amount: conversion.amount,
        createdAt: conversion.createdAt,
        status: conversion.status,
      };
    } catch (error) {
      this.logger.error(
        `Error getting point conversion: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy thông tin chi tiết lịch sử chuyển đổi điểm',
      );
    }
  }

  /**
   * Chuyển đổi tiền hoa hồng sang điểm
   * @param userId ID của người dùng
   * @param dto Thông tin yêu cầu chuyển đổi
   * @returns Kết quả chuyển đổi
   */
  @Transactional()
  async convertToPoints(
    userId: number,
    dto: ConvertToPointsRequestDto,
  ): Promise<ConvertToPointsResponseDto> {
    try {
      // Lấy tài khoản affiliate của người dùng
      const affiliateAccount =
        await this.affiliateAccountRepository.findByUserId(userId);

      if (!affiliateAccount) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản affiliate',
        );
      }

      // Kiểm tra số dư có đủ không (sử dụng availableBalance thay vì walletBalance)
      if (affiliateAccount.availableBalance < dto.amount) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.INSUFFICIENT_BALANCE,
          `Số dư không đủ để chuyển đổi. Số dư hiện tại: ${affiliateAccount.availableBalance.toLocaleString('vi-VN')} VND`,
        );
      }

      // Kiểm tra số tiền chuyển đổi có hợp lệ không
      if (dto.amount < 1000) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.POINT_CONVERSION_AMOUNT_TOO_SMALL,
          'Số tiền chuyển đổi phải lớn hơn hoặc bằng 1,000 VND',
        );
      }

      // Lấy thông tin người dùng
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy thông tin người dùng',
        );
      }

      // Tỷ lệ chuyển đổi (1 VND = 1 Point)
      const conversionRate = 1;
      const pointsConverted = Math.floor(dto.amount * conversionRate);

      // Trừ tiền từ ví affiliate (sử dụng availableBalance thay vì walletBalance)
      await this.affiliateAccountRepository.update(affiliateAccount.id, {
        availableBalance: affiliateAccount.availableBalance - dto.amount,
        updatedAt: Math.floor(Date.now() / 1000),
      });

      // Cộng điểm vào tài khoản người dùng
      user.pointsBalance = user.pointsBalance + pointsConverted;
      // Sử dụng phương thức findById để cập nhật
      await this.userRepository.findById(user.id); // Chỉ để đảm bảo user tồn tại
      // Cập nhật trực tiếp vào database
      await this.dataSource.createQueryBuilder()
        .update(User)
        .set({ pointsBalance: user.pointsBalance })
        .where("id = :id", { id: user.id })
        .execute();

      // Lưu lịch sử chuyển đổi
      const conversion = await this.affiliatePointConversionHistoryRepository.createPointConversion({
        affiliateAccountId: affiliateAccount.id,
        amount: dto.amount,
        pointsConverted,
        conversionRate,
        status: PointConversionStatus.SUCCESS,
        createdAt: Math.floor(Date.now() / 1000),
      });

      // Lấy thông tin tài khoản affiliate và người dùng sau khi cập nhật
      const updatedAffiliateAccount = await this.affiliateAccountRepository.findById(affiliateAccount.id);
      const updatedUser = await this.userRepository.findById(userId);

      if (!updatedAffiliateAccount || !updatedUser) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không thể lấy thông tin tài khoản sau khi cập nhật',
        );
      }

      // Xử lý dữ liệu trả về
      return {
        id: conversion.id,
        amount: conversion.amount,
        pointsConverted: conversion.pointsConverted,
        conversionRate: conversion.conversionRate,
        currentWalletBalance: updatedAffiliateAccount.availableBalance, // Sử dụng availableBalance thay vì walletBalance
        currentPointsBalance: updatedUser.pointsBalance,
        status: conversion.status,
        createdAt: conversion.createdAt,
      };
    } catch (error) {
      this.logger.error(
        `Error converting to points: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.POINT_CONVERSION_PROCESSING_FAILED,
        'Lỗi khi chuyển đổi tiền hoa hồng sang điểm',
      );
    }
  }
}
