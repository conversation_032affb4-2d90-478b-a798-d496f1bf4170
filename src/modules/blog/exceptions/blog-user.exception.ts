import { ErrorCode } from '@/common';
import { HttpStatus } from '@nestjs/common';

export const BLOG_ERROR_CODE = {
  // ===== BLOG ERRORS (20001-20099) =====
  BLOG_NOT_FOUND: new ErrorCode(
    20001,
    'Không tìm thấy bài viết hoặc bài viết không khả dụng',
    HttpStatus.NOT_FOUND,
  ),

  BLOG_ACCESS_DENIED: new ErrorCode(
    20002,
    'Bạn không có quyền truy cập bài viết này',
    HttpStatus.FORBIDDEN,
  ),

  BLOG_INVALID_STATUS: new ErrorCode(
    20003,
    'Trạng thái bài viết không hợp lệ cho thao tác này',
    HttpStatus.BAD_REQUEST,
  ),

  BLOG_ALREADY_SUBMITTED: new ErrorCode(
    20004,
    'Bài viết đã được gửi để kiểm duyệt',
    HttpStatus.BAD_REQUEST,
  ),

  BLOG_NOT_DRAFT: new ErrorCode(
    20005,
    'Chỉ bài viết ở trạng thái nháp mới có thể gửi kiểm duyệt',
    HttpStatus.BAD_REQUEST,
  ),

  BLOG_NOT_PENDING: new ErrorCode(
    20006,
    'Chỉ bài viết đang chờ kiểm duyệt mới có thể hủy gửi',
    HttpStatus.BAD_REQUEST,
  ),

  BLOG_CANNOT_DELETE_APPROVED: new ErrorCode(
    20007,
    'Không thể xóa bài viết đã được phê duyệt',
    HttpStatus.FORBIDDEN,
  ),

  BLOG_MEDIA_TYPE_INVALID: new ErrorCode(
    20008,
    'Loại media không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  BLOG_MEDIA_CONTENT_TYPE_INVALID: new ErrorCode(
    20009,
    'Loại nội dung media không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  BLOG_CREATE_FAILED: new ErrorCode(
    20010,
    'Tạo bài viết thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  BLOG_MEDIA_UPDATE_FAILED: new ErrorCode(
    20011,
    'Cập nhật media cho bài viết thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  BLOG_SUBMIT_FAILED: new ErrorCode(
    20012,
    'Gửi bài viết để kiểm duyệt thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  BLOG_CANCEL_SUBMIT_FAILED: new ErrorCode(
    20013,
    'Hủy gửi bài viết kiểm duyệt thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  BLOG_DELETE_FAILED: new ErrorCode(
    20014,
    'Xóa bài viết thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== BLOG COMMENT ERRORS (20100-20199) =====
  BLOG_COMMENT_NOT_FOUND: new ErrorCode(
    20100,
    'Không tìm thấy bình luận',
    HttpStatus.NOT_FOUND,
  ),

  BLOG_COMMENT_ACCESS_DENIED: new ErrorCode(
    20101,
    'Bạn không có quyền thao tác với bình luận này',
    HttpStatus.FORBIDDEN,
  ),

  BLOG_COMMENT_PARENT_NOT_FOUND: new ErrorCode(
    20102,
    'Không tìm thấy bình luận cha',
    HttpStatus.NOT_FOUND,
  ),

  BLOG_COMMENT_CANNOT_REPLY_TO_REPLY: new ErrorCode(
    20103,
    'Không thể trả lời một bình luận đã là trả lời',
    HttpStatus.BAD_REQUEST,
  ),

  BLOG_COMMENT_CREATE_FAILED: new ErrorCode(
    20104,
    'Tạo bình luận thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  BLOG_COMMENT_DELETE_FAILED: new ErrorCode(
    20105,
    'Xóa bình luận thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  BLOG_COMMENTS_FETCH_FAILED: new ErrorCode(
    20106,
    'Lấy danh sách bình luận thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // ===== BLOG PURCHASE ERRORS (20200-20299) =====
  BLOG_ALREADY_PURCHASED: new ErrorCode(
    20200,
    'Bạn đã mua bài viết này rồi',
    HttpStatus.BAD_REQUEST,
  ),

  BLOG_INSUFFICIENT_POINTS: new ErrorCode(
    20201,
    'Số dư điểm không đủ để mua bài viết',
    HttpStatus.BAD_REQUEST,
  ),

  BLOG_CANNOT_PURCHASE_OWN: new ErrorCode(
    20202,
    'Bạn không thể mua bài viết của chính mình',
    HttpStatus.BAD_REQUEST,
  ),

  BLOG_PURCHASE_FAILED: new ErrorCode(
    20203,
    'Giao dịch mua bài viết thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  BLOG_PURCHASE_STATUS_CHECK_FAILED: new ErrorCode(
    20204,
    'Kiểm tra trạng thái mua bài viết thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  BLOG_PURCHASED_LIST_FETCH_FAILED: new ErrorCode(
    20205,
    'Lấy danh sách bài viết đã mua thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  BLOG_PURCHASE_NOT_FOUND: new ErrorCode(
    20206,
    'Không tìm thấy giao dịch mua bài viết',
    HttpStatus.NOT_FOUND,
  ),

  BLOG_PURCHASE_DETAIL_FETCH_FAILED: new ErrorCode(
    20207,
    'Lấy chi tiết giao dịch mua bài viết thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
};
