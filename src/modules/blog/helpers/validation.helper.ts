import { Injectable } from '@nestjs/common';
import { AppException } from '@/common';
import { BLOG_ERROR_CODE } from '@modules/blog/exceptions';
import { Blog, BlogPurchase } from '../entities';
import { BlogStatusEnum } from '../enums';

/**
 * Helper xử lý validation cho module Blog
 */
@Injectable()
export class ValidationHelper {
  /**
   * Kiểm tra bài viết có tồn tại và khả dụng không
   * @param blog Bài viết cần kiểm tra
   * @throws AppException nếu bài viết không tồn tại hoặc không khả dụng
   */
  validateBlogExists(blog: Blog | null | undefined): void {
    if (!blog) {
      throw new AppException(BLOG_ERROR_CODE.BLOG_NOT_FOUND);
    }
    
    if (!blog.enable) {
      throw new AppException(BLOG_ERROR_CODE.BLOG_ACCESS_DENIED);
    }
    
    if (blog.status !== BlogStatusEnum.APPROVED) {
      throw new AppException(BLOG_ERROR_CODE.BLOG_INVALID_STATUS);
    }
  }
  
  /**
   * Kiểm tra người dùng có tồn tại và đang hoạt động không
   * @param user Thông tin người dùng hoặc null
   * @throws AppException nếu người dùng không tồn tại hoặc không hoạt động
   */
  validateUserExists(user: any | null | undefined): void {
    if (!user) {
      throw new AppException(
        BLOG_ERROR_CODE.BLOG_ACCESS_DENIED,
        'Người dùng không tồn tại hoặc không hoạt động',
      );
    }
  }
  
  /**
   * Kiểm tra người dùng chưa mua bài viết
   * @param hasPurchased Đã mua hay chưa
   * @throws AppException nếu người dùng đã mua bài viết
   */
  validateNotAlreadyPurchased(hasPurchased: boolean): void {
    if (hasPurchased) {
      throw new AppException(BLOG_ERROR_CODE.BLOG_ALREADY_PURCHASED);
    }
  }
  
  /**
   * Kiểm tra người dùng có đủ điểm để mua bài viết
   * @param userPoints Số điểm của người dùng
   * @param blogPoints Số điểm của bài viết
   * @throws AppException nếu người dùng không đủ điểm
   */
  validateSufficientPoints(userPoints: number, blogPoints: number): void {
    if (userPoints < blogPoints) {
      throw new AppException(BLOG_ERROR_CODE.BLOG_INSUFFICIENT_POINTS);
    }
  }
  
  /**
   * Kiểm tra người dùng không mua bài viết của chính mình
   * @param blogUserId ID của tác giả bài viết
   * @param userId ID của người dùng đang mua
   * @throws AppException nếu người dùng đang cố mua bài viết của chính mình
   */
  validateNotOwnBlog(blogUserId: number, userId: number): void {
    if (blogUserId === userId) {
      throw new AppException(BLOG_ERROR_CODE.BLOG_CANNOT_PURCHASE_OWN);
    }
  }
  
  /**
   * Kiểm tra giao dịch mua bài viết có tồn tại không
   * @param purchase Giao dịch mua bài viết cần kiểm tra
   * @throws AppException nếu giao dịch không tồn tại
   */
  validatePurchaseExists(purchase: BlogPurchase | null | undefined): void {
    if (!purchase) {
      throw new AppException(BLOG_ERROR_CODE.BLOG_PURCHASE_NOT_FOUND);
    }
  }
  
  /**
   * Kiểm tra giá trị điểm hợp lệ
   * @param userPointsBalance Số điểm của người dùng
   * @param blogPoint Số điểm của bài viết
   * @throws AppException nếu giá trị điểm không hợp lệ
   */
  validatePointValues(userPointsBalance: any, blogPoint: any): { userPoints: number, blogPoints: number } {
    let userPoints = 0;
    let blogPoints = 0;
    
    try {
      userPoints = userPointsBalance ? Number(userPointsBalance) : 0;
      blogPoints = blogPoint ? Number(blogPoint) : 0;
      
      if (isNaN(userPoints) || isNaN(blogPoints)) {
        throw new Error('Giá trị điểm không hợp lệ');
      }
      
      return { userPoints, blogPoints };
    } catch (err) {
      throw new AppException(
        BLOG_ERROR_CODE.BLOG_PURCHASE_FAILED,
        'Giá trị điểm không hợp lệ',
      );
    }
  }
}
