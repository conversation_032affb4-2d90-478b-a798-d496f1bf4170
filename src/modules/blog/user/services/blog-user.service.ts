import { User } from '@/modules/user/entities/user.entity';
import { Employee } from '@/modules/employee/entities/employee.entity';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Brackets } from 'typeorm';
import { Blog, BlogPurchase } from '../../entities';
import {
  AuthorDto,
  BlogResponseDto,
  CreateBlogDto,
  GetBlogsUnifiedDto,
  MediaTypeEnum,
  PaginatedBlogResponseDto,
  UpdateBlogMediaDto,
} from '../../dto';
import { AuthorTypeEnum, BlogStatusEnum, BlogOwnershipEnum } from '../../enums';
import { BlogRepository } from '../../repositories';
import { S3Service } from '@/shared/services/s3.service';
import {
  CategoryFolderEnum,
  FileSizeEnum,
  generateS3Key,
  TimeIntervalEnum,
} from '@/shared/utils';
import {
  ImageType,
  ImageTypeEnum,
} from '@/shared/utils/file/image-media_type.util';
import { SqlHelper } from '@common/helpers/sql.helper';
import { PaginatedResult } from '@/common/response';
import { AppException } from '@/common';
import { BLOG_ERROR_CODE } from '@modules/blog/exceptions';
import { ContentMediaType, ContentMediaTypeEnum } from '@/shared/utils/file/content-media-type.util';

@Injectable()
export class BlogUserService {
  constructor(
    @InjectRepository(Blog)
    private readonly blogRepository: Repository<Blog>,
    @InjectRepository(BlogPurchase)
    private readonly blogPurchaseRepository: Repository<BlogPurchase>,
    private readonly customBlogRepository: BlogRepository,
    private readonly s3Service: S3Service,
    private readonly sqlHelper: SqlHelper,
  ) {}

  /**
   * Format a CDN URL by adding the CDN_URL prefix if needed
   * @param url The URL or path to format
   * @returns Properly formatted CDN URL
   */
  private formatCdnUrl(url: string | null | undefined): string {
    if (!url) return '';

    // If the URL already starts with http, return it as is
    if (url.startsWith('http')) return url;

    // Add the CDN_URL prefix with a slash between domain and path
    const cdnUrl = process.env.CDN_URL || 'https://cdn.redai.vn';
    return `${cdnUrl}/${url}`;
  }

  /**
   * Helper method to convert PaginatedResult<BlogResponseDto> to PaginatedBlogResponseDto
   * @private
   */
  private async convertToPaginatedBlogResponseDto(
    paginatedResult: PaginatedResult<BlogResponseDto>,
  ): Promise<PaginatedBlogResponseDto> {
    // Transform the data to include author information
    const transformedItems = await Promise.all(
      paginatedResult.items.map(async (item) => {
        // Get author information
        let author = {
          id:
            item.authorType === AuthorTypeEnum.USER
              ? (item.userId ?? null)
              : (item.employeeId ?? null),
          name: item.user?.fullName || 'System',
          type: item.authorType || AuthorTypeEnum.SYSTEM,
          avatar:
            item.user?.avatar || 'https://cdn.example.com/avatars/default.jpg',
        };

        // If user information is not available in the item, fetch it
        if (item.userId && !item.user) {
          const users = await this.sqlHelper.select(
            'users',
            ['id', 'full_name as fullName', 'avatar'],
            [{ condition: 'id = :id', params: { id: item.userId } }],
          );

          if (users && users.length > 0) {
            author.name = users[0].fullName || 'User';
            author.avatar =
              users[0].avatar || 'https://cdn.example.com/avatars/default.jpg';
          }
        }

        // Create a new object with the author field properly set
        return {
          ...item,
          author, // Ensure author is included
          employeeModerator: item.employeeModerator || null,
        };
      }),
    );

    const result = new PaginatedBlogResponseDto();
    result.content = transformedItems;
    result.totalItems = paginatedResult.meta.totalItems;
    result.itemCount = paginatedResult.meta.itemCount;
    result.itemsPerPage = paginatedResult.meta.itemsPerPage;
    result.totalPages = paginatedResult.meta.totalPages;
    result.currentPage = paginatedResult.meta.currentPage;
    return result;
  }

  /**
   * Lấy danh sách bài viết với các tùy chọn lọc nâng cao
   * @param userId ID của người dùng hiện tại
   * @param dto Tham số truy vấn với các tùy chọn lọc
   * @returns Danh sách bài viết có phân trang
   */
  async getBlogs(userId: number | null, dto: GetBlogsUnifiedDto): Promise<PaginatedBlogResponseDto> {
    try {
      // Tạo query builder cơ bản
      const qb = this.blogRepository.createQueryBuilder('blog');

      // Chọn các trường cần thiết
      qb.select([
        'blog.id',
        'blog.title',
        'blog.content',
        'blog.point',
        'blog.viewCount',
        'blog.thumbnailUrl',
        'blog.tags',
        'blog.createdAt',
        'blog.updatedAt',
        'blog.userId',
        'blog.employeeId',
        'blog.authorType',
        'blog.status',
        'blog.enable',
        'blog.like',
        'blog.employeeModerator',
      ]);

      // Điều kiện cơ bản: blog phải được kích hoạt
      qb.where('blog.enable = :enable', { enable: true });

      // Xử lý điều kiện status
      if (userId && dto.ownership_type) {
        // Khi lọc theo ownership_type=CREATED, không lọc theo status để lấy tất cả trạng thái
        if (dto.ownership_type === BlogOwnershipEnum.CREATED) {
          // Không áp dụng điều kiện status để lấy tất cả blog của người dùng với mọi trạng thái
          console.log('Lấy tất cả blog của người dùng với mọi trạng thái');
        } else {
          // Đối với các loại ownership_type khác (PURCHASED, NOT_OWNED), vẫn lọc theo APPROVED
          qb.andWhere('blog.status = :status', { status: BlogStatusEnum.APPROVED });
        }
      } else if (dto.status) {
        // Nếu có chỉ định status và không lọc theo ownership_type
        qb.andWhere('blog.status = :status', { status: dto.status });
      } else {
        // Nếu không chỉ định status và không lọc theo ownership_type
        qb.andWhere('blog.status = :status', { status: BlogStatusEnum.APPROVED });
      }

      // Lọc theo authorType nếu có
      if (dto.authorType) {
        qb.andWhere('blog.authorType = :authorType', { authorType: dto.authorType });
      }

      // Đã bỏ lọc theo tags

      // Tìm kiếm theo tiêu đề và nội dung
      if (dto.search) {
        qb.andWhere(new Brackets(qb => {
          qb.where('blog.title ILIKE :search', { search: `%${dto.search}%` })
            .orWhere('blog.content ILIKE :search', { search: `%${dto.search}%` });
        }));
      }

      // Lọc theo loại sở hữu nếu có userId
      if (userId && dto.ownership_type) {
        switch (dto.ownership_type) {
          case BlogOwnershipEnum.CREATED:
            // Lấy bài viết do người dùng tạo
            qb.andWhere('blog.userId = :userId', { userId });
            break;

          case BlogOwnershipEnum.PURCHASED:
            // Lấy bài viết người dùng đã mua
            qb.innerJoin(
              'blog_purchases',
              'purchase',
              'blog.id = purchase.blog_id AND purchase.user_id = :userId',
              { userId }
            );
            break;

          case BlogOwnershipEnum.NOT_OWNED:
            // Lấy bài viết người dùng chưa sở hữu (chưa tạo và chưa mua)

            // Lọc bỏ các bài viết do người dùng tạo
            // Sử dụng OR để bao gồm cả các bài viết có userId là NULL
            qb.andWhere(new Brackets(qb => {
              qb.where('blog.userId != :userId', { userId })
                .orWhere('blog.userId IS NULL');
            }));

            // Kiểm tra xem người dùng đã mua bài viết nào chưa
            const purchasedBlogs = await this.sqlHelper.select(
              'blog_purchases',
              ['blog_id as blogId'],
              [{ condition: 'user_id = :userId', params: { userId } }]
            );

            // Nếu người dùng đã mua bài viết, loại bỏ các bài viết đó
            if (purchasedBlogs && purchasedBlogs.length > 0) {
              // Lọc các giá trị hợp lệ và chuyển đổi sang số
              const validIds = purchasedBlogs
                .map(p => {
                  const id = Number(p.blogId);
                  return isNaN(id) ? null : id;
                })
                .filter(id => id !== null);

              // Chỉ thêm điều kiện nếu có ít nhất một ID hợp lệ
              if (validIds.length > 0) {
                qb.andWhere('blog.id NOT IN (:...ids)', { ids: validIds });
              }
            }
            break;
        }
      }

      // In ra câu truy vấn SQL cuối cùng để debug
      console.log('Final SQL Query:', qb.getSql());
      console.log('Final SQL Parameters:', qb.getParameters());

      // Join với bảng user để lấy thông tin chi tiết của tác giả (nếu là user)
      qb.leftJoin(User, 'user', 'blog.userId = user.id')
        .addSelect([
          'user.id',
          'user.fullName',
          'user.avatar',
        ]);

      // Join với bảng employee để lấy thông tin chi tiết của tác giả (nếu là employee)
      qb.leftJoin(Employee, 'employee', 'blog.employeeId = employee.id')
        .addSelect([
          'employee.id',
          'employee.fullName',
          'employee.avatar',
        ]);

      // Sắp xếp
      qb.orderBy(`blog.${dto.sortBy || 'createdAt'}`, dto.sortDirection || 'DESC');

      // Phân trang
      const page = dto.page || 1;
      const limit = dto.limit || 10;
      const offset = (page - 1) * limit;
      qb.skip(offset).take(limit);

      // Thực thi truy vấn
      const [items, totalItems] = await qb.getManyAndCount();

      // Chuyển đổi kết quả sang DTO
      const blogResponseDtos = await Promise.all(items.map(async (item) => {
        const dto = new BlogResponseDto();
        dto.id = item.id;
        dto.title = item.title;

        // Kiểm tra xem người dùng có quyền xem nội dung không
        let isPurchased = false;
        if (userId) {
          // Nếu người dùng là tác giả của bài viết, coi như đã mua
          if (item.userId === userId) {
            isPurchased = true;
          } else {
            // Kiểm tra người dùng đã mua bài viết chưa
            const hasPurchased = await this.sqlHelper.exists('blog_purchases', [
              { condition: 'user_id = :userId', params: { userId } },
              { condition: 'blog_id = :blogId', params: { blogId: item.id } },
            ]);
            isPurchased = hasPurchased;
          }
        }

        // Chỉ hiển thị nội dung nếu người dùng đã mua hoặc là tác giả
        dto.content = isPurchased ? this.formatCdnUrl(item.content) : null;
        dto.isPurchased = isPurchased;

        dto.point = item.point;
        dto.viewCount = item.viewCount;
        dto.thumbnailUrl = this.formatCdnUrl(item.thumbnailUrl);
        dto.tags = item.tags;
        dto.createdAt = item.createdAt;
        dto.updatedAt = item.updatedAt;
        dto.userId = item.userId;
        dto.employeeId = item.employeeId;
        dto.authorType = item.authorType;
        dto.status = item.status;
        dto.enable = item.enable;
        dto.like = item.like;
        dto.employeeModerator = item.employeeModerator;

        // Thông tin tác giả
        let authorName = 'Unknown';
        let authorAvatar = 'https://cdn.example.com/avatars/default.jpg';

        // Lấy thông tin chi tiết của tác giả dựa vào authorType
        if (item.authorType === AuthorTypeEnum.USER && item.userId) {
          // Lấy thông tin user từ bảng users
          const users = await this.sqlHelper.select(
            'users',
            ['id', 'full_name as fullName', 'avatar'],
            [{ condition: 'id = :id', params: { id: item.userId } }]
          );

          if (users && users.length > 0) {
            authorName = String(users[0].fullName || 'User');
            authorAvatar = String(users[0].avatar || 'https://cdn.example.com/avatars/default.jpg');
          }
        } else if (item.authorType === AuthorTypeEnum.SYSTEM && item.employeeId) {
          // Lấy thông tin employee từ bảng employees
          const employees = await this.sqlHelper.select(
            'employees',
            ['id', 'full_name as fullName', 'avatar'],
            [{ condition: 'id = :id', params: { id: item.employeeId } }]
          );

          if (employees && employees.length > 0) {
            authorName = String(employees[0].fullName || 'Employee');
            authorAvatar = String(employees[0].avatar || 'https://cdn.example.com/avatars/default.jpg');
          }
        }

        dto.author = {
          id: item.authorType === AuthorTypeEnum.USER ? item.userId : item.employeeId,
          name: authorName,
          type: item.authorType,
          avatar: this.formatCdnUrl(authorAvatar),
        };

        return dto;
      }));

      // Tạo kết quả phân trang
      const result = new PaginatedBlogResponseDto();
      result.content = blogResponseDtos;
      result.totalItems = totalItems;
      result.itemCount = blogResponseDtos.length;
      result.itemsPerPage = limit;
      result.totalPages = Math.ceil(totalItems / limit);
      result.currentPage = page;

      return result;
    } catch (error) {
      console.error('Error in getBlogs:', error);

      // Nếu là lỗi đã được xử lý, tiếp tục ném lỗi
      if (error instanceof AppException) {
        throw error;
      }

      // Trả về kết quả trống thay vì ném lỗi 404
      if (error.name === 'QueryFailedError') {
        const emptyResult = new PaginatedBlogResponseDto();
        emptyResult.content = [];
        emptyResult.totalItems = 0;
        emptyResult.itemCount = 0;
        emptyResult.itemsPerPage = dto.limit || 10;
        emptyResult.totalPages = 0;
        emptyResult.currentPage = dto.page || 1;
        return emptyResult;
      }

      // Nếu là lỗi khác, ném lỗi chung
      throw new AppException(
        BLOG_ERROR_CODE.BLOG_NOT_FOUND,
        'Failed to fetch blogs. Please try again later.',
      );
    }
  }

  // Đã bỏ các phương thức debug không cần thiết

  /**
   * Lấy chi tiết bài viết theo ID
   * @param id ID của bài viết
   * @param userId ID của người dùng hiện tại (nếu đã đăng nhập)
   * @returns Thông tin chi tiết của bài viết
   */
  async findOne(id: number, userId?: number): Promise<BlogResponseDto> {
    try {
      console.log(`Getting blog detail for id=${id}, userId=${userId || 'guest'}`);

      // Sử dụng SqlHelper để lấy thông tin bài viết và tác giả
      const blogs = await this.sqlHelper.select(
        'blogs',
        [
          'blogs.id',
          'blogs.title',
          'blogs.content',
          'blogs.point',
          'blogs.view_count as viewCount',
          'blogs.thumbnail_url as thumbnailUrl',
          'blogs.tags',
          'blogs.created_at as createdAt',
          'blogs.updated_at as updatedAt',
          'blogs.user_id as userId',
          'blogs.employee_id as employeeId',
          'blogs.author_type as authorType',
          'blogs.employee_moderator as employeeModerator',
          'blogs.status',
          'blogs.enable',
          'blogs.like',
          // User author fields
          'users.id as "user.id"',
          'users.full_name as "user.fullName"',
          'users.avatar as "user.avatar"',
          // Employee author fields
          'employees.id as "employee.id"',
          'employees.full_name as "employee.fullName"',
          'employees.avatar as "employee.avatar"',
          // Moderator fields
          'moderators.id as "moderator.id"',
          'moderators.full_name as "moderator.fullName"',
          'moderators.avatar as "moderator.avatar"',
        ],
        [
          { condition: 'blogs.id = :id', params: { id } },
          { condition: 'blogs.enable = :enable', params: { enable: true } },
          // Removed the status condition to allow blogs with any status to be returned
        ],
        [
          {
            type: 'LEFT',
            table: 'users',
            alias: 'users',
            condition: 'blogs.user_id = users.id',
          },
          {
            type: 'LEFT',
            table: 'employees',
            alias: 'employees',
            condition: 'blogs.employee_id = employees.id',
          },
          {
            type: 'LEFT',
            table: 'employees',
            alias: 'moderators',
            condition: 'blogs.employee_moderator = moderators.id',
          },
        ],
        [],
        [],
        undefined,
        undefined,
        { raw: true },
      );

      if (!blogs || blogs.length === 0) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_NOT_FOUND);
      }

      const blog = blogs[0] as any;
      console.log('Blog data from database:', JSON.stringify(blog, null, 2));

      // Lấy thông tin tác giả
      let author: AuthorDto = {
        id: null,
        name: 'System',
        type: (blog.authortype as AuthorTypeEnum) || AuthorTypeEnum.SYSTEM,
        avatar: this.formatCdnUrl('avatars/default.jpg'),
      };

      console.log('Author type:', blog.authortype);
      console.log('User ID:', blog.userid);
      console.log('Employee ID:', blog.employeeid);

      // Nếu tác giả là người dùng
      if (blog.authortype === AuthorTypeEnum.USER && blog.userid) {
        // Sử dụng dữ liệu đã join từ bảng users
        if (blog['user.id']) {
          author.id = Number(blog.userid);
          author.name = String(blog['user.fullName'] || 'User');
          author.avatar = this.formatCdnUrl(String(blog['user.avatar'] || 'avatars/default.jpg'));
          author.type = AuthorTypeEnum.USER;
        }
      }
      // Nếu tác giả là nhân viên
      else if (blog.authortype === AuthorTypeEnum.SYSTEM && blog.employeeid) {
        // Sử dụng dữ liệu đã join từ bảng employees
        if (blog['employee.id']) {
          author.id = Number(blog.employeeid);
          author.name = String(blog['employee.fullName'] || 'Employee');
          author.avatar = this.formatCdnUrl(String(blog['employee.avatar'] || 'avatars/default.jpg'));
          author.type = AuthorTypeEnum.SYSTEM;
        }
      }

      // Lấy thông tin người kiểm duyệt (nếu có)
      let employeeModerator: any = null;
      if (blog.employeemoderator && blog['moderator.id']) {
        employeeModerator = {
          id: Number(blog.employeemoderator),
          name: String(blog['moderator.fullName'] || 'Moderator'),
          avatar: this.formatCdnUrl(String(blog['moderator.avatar'] || 'avatars/default.jpg')),
        };
      }

      // Kiểm tra trạng thái mua của người dùng hiện tại
      let isPurchased = false;
      if (userId) {
        // Nếu người dùng là tác giả của bài viết, coi như đã mua
        if (Number(blog.userid) === userId) {
          isPurchased = true;
        } else {
          // Kiểm tra người dùng đã mua bài viết chưa
          const hasPurchased = await this.sqlHelper.exists('blog_purchases', [
            { condition: 'user_id = :userId', params: { userId } },
            { condition: 'blog_id = :blogId', params: { blogId: id } },
          ]);
          isPurchased = hasPurchased;
        }
      }

      // Upload URLs have been removed from the response

      // Cập nhật số lượt xem
      await this.customBlogRepository.incrementViewCount(id);

      // Parse tags from JSON if needed
      let tags: string[] = [];
      if (blog.tags) {
        try {
          if (typeof blog.tags === 'string') {
            tags = JSON.parse(blog.tags);
          } else if (Array.isArray(blog.tags)) {
            tags = blog.tags;
          }
        } catch (e) {
          console.error('Error parsing tags:', e);
        }
      }

      // Make sure thumbnailUrl has the CDN prefix if needed
      let thumbnailUrl = String(blog.thumbnailurl || '');
      if (thumbnailUrl && !thumbnailUrl.startsWith('http')) {
        const cdnUrl = process.env.CDN_URL || 'https://cdn.redai.vn';
        thumbnailUrl = `${cdnUrl}/${thumbnailUrl}`;
      }

      // Make sure content has the CDN prefix if needed
      let content = String(blog.content || '');
      if (content && !content.startsWith('http')) {
        const cdnUrl = process.env.CDN_URL || 'https://cdn.redai.vn';
        content = `${cdnUrl}/${content}`;
      }

      // Tạo đối tượng kết quả với đầy đủ thông tin
      const result: Partial<BlogResponseDto> = {
        id: Number(blog.id),
        title: String(blog.title || ''),
        // Chỉ hiển thị nội dung nếu người dùng đã mua hoặc là tác giả
        content: isPurchased ? content : null,
        point: Number(blog.point || 0),
        viewCount: Number(blog.viewcount || 0),
        thumbnailUrl,
        tags,
        createdAt: Number(blog.createdat || 0),
        updatedAt: Number(blog.updatedat || 0),
        userId: blog.userid ? Number(blog.userid) : null,
        employeeId: blog.employeeid ? Number(blog.employeeid) : null,
        authorType: blog.authortype as AuthorTypeEnum,
        author,
        employeeModerator,
        status: blog.status as BlogStatusEnum,
        enable: Boolean(blog.enable),
        like: Number(blog.like || 0),
        isPurchased,
      };

      // Upload URLs have been removed from the response as requested

      // Format avatar URL
      if (result.author && result.author.avatar) {
        result.author.avatar = this.formatCdnUrl(result.author.avatar);
      }

      console.log('Final blog response:', JSON.stringify(result, null, 2));

      // Cast the result back to BlogResponseDto before returning
      return result as BlogResponseDto;
    } catch (error: unknown) {
      if (error instanceof AppException) {
        throw error;
      }
      console.error('Error in findOne:', error);
      throw new AppException(BLOG_ERROR_CODE.BLOG_NOT_FOUND);
    }
  }

  /**
   * Tạo bài viết mới
   */
  async create(userId: number, createBlogDto: CreateBlogDto) {
    try {
      const now = Date.now();

      // Sử dụng SqlHelper để tạo bài viết mới
      const savedBlog = await this.sqlHelper.insert(
        'blogs',
        {
          title: createBlogDto.title,
          description: createBlogDto.description, // Lưu description vào đúng field
          content: null, // Content sẽ được cập nhật sau khi upload file
          point: createBlogDto.point,
          tags: JSON.stringify(createBlogDto.tags),
          user_id: userId,
          author_type: AuthorTypeEnum.USER,
          status: createBlogDto.status || BlogStatusEnum.DRAFT,
          enable: true,
          created_at: now,
          updated_at: now,
          view_count: 0,
          like: 0,
        },
        ['id'],
      );

      if (!savedBlog || !savedBlog.id) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_CREATE_FAILED);
      }

      // Tạo URL tạm thời để upload content và thumbnail
      const contentKey = generateS3Key({
        baseFolder: 'blogs',
        prefix: `content-${savedBlog.id}`,
        categoryFolder: CategoryFolderEnum.DOCUMENT,
      });

      const thumbnailKey = generateS3Key({
        baseFolder: 'blogs',
        prefix: `thumbnail-${savedBlog.id}`,
        categoryFolder: CategoryFolderEnum.IMAGE,
      });

      // Xác định loại content media
      let contentMediaType: ContentMediaTypeEnum;
      try {
        contentMediaType = ContentMediaType.getMimeType(createBlogDto.contentMediaType);
      } catch {
        contentMediaType = ContentMediaTypeEnum.HTML; // Default to JPEG if not valid
      }

      // Xác định loại thumbnail media
      let thumbnailMediaType: ImageTypeEnum;
      try {
        thumbnailMediaType = ImageType.getType(
          createBlogDto.thumbnailMediaType,
        );
      } catch {
        thumbnailMediaType = ImageTypeEnum.JPEG; // Default to JPEG if not valid
      }

      // Tạo presigned URL cho content
      const contentUploadUrl = await this.s3Service.createPresignedWithID(
        contentKey,
        TimeIntervalEnum.ONE_HOUR,
        contentMediaType,
        FileSizeEnum.FIVE_MB,
      );

      // Tạo presigned URL cho thumbnail
      const thumbnailUploadUrl = await this.s3Service.createPresignedWithID(
        thumbnailKey,
        TimeIntervalEnum.ONE_HOUR,
        thumbnailMediaType,
        FileSizeEnum.TWO_MB,
      );

      // Cập nhật blog với content và thumbnail keys
      console.log('Updating blog with content and thumbnail keys:', {
        blogId: savedBlog.id,
        contentKey,
        thumbnailKey
      });

      try {
        // Execute a raw SQL query to update the blog with the keys
        // This approach gives us more control over the SQL being executed
        const updateQuery = `
          UPDATE blogs
          SET content = $1,
              thumbnail_url = $2,
              updated_at = $3
          WHERE id = $4
        `;

        await this.sqlHelper.executeRawQuery(
          updateQuery,
          [contentKey, thumbnailKey, now, Number(savedBlog.id)]
        );

        console.log('Blog updated successfully with keys');
      } catch (updateError) {
        console.error('Error updating blog with keys:', updateError);
        console.error('Error details:', updateError.message);
        console.error('Error stack:', updateError.stack);
        // Continue execution even if update fails
      }

      return {
        contentUploadUrl: contentUploadUrl,
        thumbnailUploadUrl: thumbnailUploadUrl,
        blogId: savedBlog.id, // Thêm blogId để client có thể sử dụng cho các API tiếp theo
      };
    } catch (error: unknown) {
      if (error instanceof AppException) {
        throw error;
      }
      console.error('Error creating blog:', error);
      throw new AppException(BLOG_ERROR_CODE.BLOG_CREATE_FAILED);
    }
  }

  /**
   * Cập nhật media cho bài viết
   */
  async updateMedia(
    id: number,
    userId: number,
    updateMediaDto: UpdateBlogMediaDto,
  ) {
    try {
      // Kiểm tra bài viết có tồn tại không và thuộc về user không
      const blogExists = await this.sqlHelper.exists('blogs', [
        { condition: 'id = :id', params: { id } },
        { condition: 'user_id = :userId', params: { userId } },
      ]);

      if (!blogExists) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_ACCESS_DENIED);
      }

      // Xác định loại media cần cập nhật
      const isContent = updateMediaDto.media_type === MediaTypeEnum.CONTENT;
      const mediaKey = isContent
        ? generateS3Key({
            baseFolder: 'blogs',
            prefix: `content-${id}`,
            categoryFolder: CategoryFolderEnum.DOCUMENT,
          })
        : generateS3Key({
            baseFolder: 'blogs',
            prefix: `thumbnail-${id}`,
            categoryFolder: CategoryFolderEnum.IMAGE,
          });

      // Xác định loại media content dựa vào loại media
      let mediaContentType: ContentMediaTypeEnum | ImageTypeEnum;
      try {
        if (isContent) {
          // Kiểm tra nếu media_content_type không phải là text/html
          if (updateMediaDto.media_content_type !== ContentMediaTypeEnum.HTML) {
            throw new AppException(
              BLOG_ERROR_CODE.BLOG_MEDIA_CONTENT_TYPE_INVALID,
              `Loại nội dung không hợp lệ cho content. Chỉ hỗ trợ 'text/html'.`
            );
          }
          // Sử dụng ContentMediaType cho nội dung
          mediaContentType = ContentMediaType.getMimeType(updateMediaDto.media_content_type);
        } else {
          // Kiểm tra nếu media_content_type không phải là loại hình ảnh
          if (!updateMediaDto.media_content_type.startsWith('image/')) {
            throw new AppException(
              BLOG_ERROR_CODE.BLOG_MEDIA_CONTENT_TYPE_INVALID,
              `Loại nội dung không hợp lệ cho thumbnail. Chỉ hỗ trợ các định dạng hình ảnh: 'image/jpeg', 'image/png', 'image/webp', 'image/gif'.`
            );
          }
          // Sử dụng ImageType cho thumbnail
          mediaContentType = ImageType.getType(updateMediaDto.media_content_type);
        }
      } catch (error) {
        if (error instanceof AppException) {
          throw error;
        }
        throw new AppException(
          BLOG_ERROR_CODE.BLOG_MEDIA_CONTENT_TYPE_INVALID,
          `Loại nội dung media không hợp lệ. ${isContent ? "Content chỉ hỗ trợ 'text/html'." : "Thumbnail chỉ hỗ trợ 'image/jpeg', 'image/png', 'image/webp', 'image/gif'."}`
        );
      }

      // Tạo presigned URL cho media
      const uploadUrl = await this.s3Service.createPresignedWithID(
        mediaKey,
        TimeIntervalEnum.ONE_HOUR,
        mediaContentType,
        isContent ? FileSizeEnum.FIVE_MB : FileSizeEnum.TWO_MB,
      );

      return {
        uploadUrl: uploadUrl,
      };
    } catch (error: unknown) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(BLOG_ERROR_CODE.BLOG_MEDIA_UPDATE_FAILED);
    }
  }

  /**
   * Gửi bài viết để kiểm duyệt
   * Chuyển trạng thái từ DRAFT sang PENDING
   */
  async submitForReview(id: number, userId: number): Promise<void> {
    try {
      // Kiểm tra bài viết có tồn tại không và thuộc về user không
      const blog = await this.sqlHelper.select(
        'blogs',
        ['id', 'status'],
        [
          { condition: 'id = :id', params: { id } },
          { condition: 'user_id = :userId', params: { userId } },
          { condition: 'enable = :enable', params: { enable: true } },
        ],
      );

      if (!blog || blog.length === 0) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_ACCESS_DENIED);
      }

      // Kiểm tra trạng thái hiện tại của bài viết
      if (blog[0].status !== BlogStatusEnum.DRAFT) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_NOT_DRAFT);
      }

      // Cập nhật trạng thái bài viết
      const now = Date.now();
      await this.sqlHelper.update(
        'blogs',
        [
          { column: 'status', value: BlogStatusEnum.PENDING },
          { column: 'updated_at', value: now },
        ],
        [{ condition: 'id = :id', params: { id } }],
      );
    } catch (error: unknown) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(BLOG_ERROR_CODE.BLOG_SUBMIT_FAILED);
    }
  }

  /**
   * Hủy gửi kiểm duyệt bài viết
   * Chuyển trạng thái từ PENDING về DRAFT
   */
  async cancelSubmit(id: number, userId: number): Promise<void> {
    try {
      // Kiểm tra bài viết có tồn tại không và thuộc về user không
      const blog = await this.sqlHelper.select(
        'blogs',
        ['id', 'status'],
        [
          { condition: 'id = :id', params: { id } },
          { condition: 'user_id = :userId', params: { userId } },
          { condition: 'enable = :enable', params: { enable: true } },
        ],
      );

      if (!blog || blog.length === 0) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_ACCESS_DENIED);
      }

      // Kiểm tra trạng thái hiện tại của bài viết
      if (blog[0].status !== BlogStatusEnum.PENDING) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_NOT_PENDING);
      }

      // Cập nhật trạng thái bài viết
      const now = Date.now();
      await this.sqlHelper.update(
        'blogs',
        [
          { column: 'status', value: BlogStatusEnum.DRAFT },
          { column: 'updated_at', value: now },
        ],
        [{ condition: 'id = :id', params: { id } }],
      );
    } catch (error: unknown) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(BLOG_ERROR_CODE.BLOG_CANCEL_SUBMIT_FAILED);
    }
  }

  /**
   * Xóa bài viết
   * Chỉ xóa mềm bằng cách đặt enable = false
   */
  async delete(id: number, userId: number): Promise<void> {
    try {
      // Kiểm tra bài viết có tồn tại không và thuộc về user không
      const blog = await this.sqlHelper.select(
        'blogs',
        ['id', 'status'],
        [
          { condition: 'id = :id', params: { id } },
          { condition: 'user_id = :userId', params: { userId } },
          { condition: 'enable = :enable', params: { enable: true } },
        ],
      );

      if (!blog || blog.length === 0) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_ACCESS_DENIED);
      }

      // Kiểm tra trạng thái hiện tại của bài viết
      if (blog[0].status === BlogStatusEnum.APPROVED) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_CANNOT_DELETE_APPROVED);
      }

      // Xóa mềm bài viết bằng cách đặt enable = false
      const now = Date.now();
      await this.sqlHelper.update(
        'blogs',
        [
          { column: 'enable', value: false },
          { column: 'updated_at', value: now },
        ],
        [{ condition: 'id = :id', params: { id } }],
      );
    } catch (error: unknown) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(BLOG_ERROR_CODE.BLOG_DELETE_FAILED);
    }
  }
}
