import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { BlogDto } from './blog.dto';

export class BlogPurchaseDto {
  @Expose()
  @ApiProperty({
    description: 'ID của giao dịch mua bài viết',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'ID của người mua',
    example: 10,
  })
  user_id: number;

  @Expose()
  @ApiProperty({
    description: 'ID của bài viết',
    example: 1,
  })
  blog_id: number;

  @Expose()
  @ApiProperty({
    description: 'Số point thời điểm mua',
    example: 100,
  })
  point: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian mua hàng (Unix timestamp)',
    example: 1632474086123,
  })
  purchased_at: number;

  @Expose()
  @Type(() => BlogDto)
  @ApiProperty({
    description: 'Thông tin bài viết',
    type: BlogDto,
  })
  blog?: BlogDto;
}

export class PurchaseStatusDto {
  @Expose()
  @ApiProperty({
    description: 'Đã mua hay chưa',
    example: true,
  })
  purchased: boolean;

  @Expose()
  @ApiProperty({
    description: 'Thời gian mua (Unix timestamp)',
    example: 1632474086123,
    nullable: true,
  })
  purchased_at: number | null;
}

export class PaginatedBlogPurchaseDto {
  @Expose()
  @ApiProperty({
    description: 'Danh sách bài viết đã mua',
    type: [BlogPurchaseDto],
  })
  content: BlogPurchaseDto[];

  @Expose()
  @ApiProperty({
    description: 'Tổng số bài viết đã mua',
    example: 25,
  })
  totalItems: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng bài viết trên trang hiện tại',
    example: 10,
  })
  itemCount: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng bài viết trên mỗi trang',
    example: 10,
  })
  itemsPerPage: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng số trang',
    example: 3,
  })
  totalPages: number;

  @Expose()
  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
  })
  currentPage: number;
}
