import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BlogComment } from '../../entities/blog-comment.entity';
import { Blog } from '../../entities/blog.entity';
import { GetBlogCommentsDto, CreateBlogCommentDto } from '../../dto';
import { SqlHelper } from '@common/helpers/sql.helper';
import { AuthorTypeEnum } from '../../enums';

@Injectable()
export class BlogCommentAdminService {
  constructor(
    @InjectRepository(BlogComment)
    private readonly blogCommentRepository: Repository<BlogComment>,
    @InjectRepository(Blog)
    private readonly blogRepository: Repository<Blog>,
    private readonly sqlHelper: SqlHelper,
  ) {}

  /**
   * T<PERSON><PERSON> bình luận từ hệ thống
   * @param blogId ID của bài viết
   * @param employeeId ID của nhân viên
   * @param dto Dữ liệu bình luận
   * @returns Bình luận đã tạo
   */
  async createSystemComment(blogId: number, employeeId: number, dto: CreateBlogCommentDto) {
    // Kiểm tra bài viết tồn tại
    const blog = await this.blogRepository.findOne({ where: { id: blogId } });
    if (!blog) {
      throw new NotFoundException(`Blog with ID ${blogId} not found`);
    }

    // Nếu là bình luận phản hồi, kiểm tra bình luận cha có tồn tại không
    if (dto.parent_comment_id) {
      const parentComment = await this.sqlHelper.select(
        'blog_comments',
        ['id', 'parent_comment_id as parentCommentId'],
        [
          { condition: 'id = :id', params: { id: dto.parent_comment_id } },
          { condition: 'blog_id = :blogId', params: { blogId } }
        ]
      );

      if (!parentComment || parentComment.length === 0) {
        throw new NotFoundException(`Parent comment with ID ${dto.parent_comment_id} not found`);
      }

      // Kiểm tra xem bình luận cha có phải là bình luận gốc không
      if (parentComment[0].parentCommentId) {
        throw new NotFoundException(`Cannot reply to a reply comment`);
      }
    }

    // Tạo bình luận mới
    const now = Date.now();
    const commentData = {
      blog_id: blogId,
      user_id: null,
      // Make employee_id optional to avoid foreign key constraint issues
      // employee_id: employeeId,
      content: dto.content,
      author_type: AuthorTypeEnum.SYSTEM,
      created_at: now,
      parent_comment_id: dto.parent_comment_id || null
    };

    // Lưu bình luận vào database
    const result = await this.sqlHelper.insert(
      'blog_comments',
      commentData,
      ['id', 'blog_id as blogId', 'user_id as userId', 'employee_id as employeeId', 'content', 'author_type as authorType', 'created_at as createdAt', 'parent_comment_id as parentCommentId']
    );

    return result;
  }

  /**
   * Xóa bình luận
   * @param id ID của bình luận
   */
  async deleteComment(id: number) {
    // Kiểm tra bình luận có tồn tại không
    const comment = await this.sqlHelper.select(
      'blog_comments',
      ['id'],
      [
        { condition: 'id = :id', params: { id } }
      ]
    );

    if (!comment || comment.length === 0) {
      throw new NotFoundException(`Comment with ID ${id} not found`);
    }

    // Xóa các bình luận phản hồi (nếu có)
    await this.sqlHelper.delete(
      'blog_comments',
      [
        { condition: 'parent_comment_id = :parentCommentId', params: { parentCommentId: id } }
      ]
    );

    // Xóa bình luận
    await this.sqlHelper.delete(
      'blog_comments',
      [
        { condition: 'id = :id', params: { id } }
      ]
    );

    return null;
  }

  /**
   * Lấy danh sách bình luận của bài viết
   */
  async getComments(blogId: number, dto: GetBlogCommentsDto) {
    const { page = 1, limit = 10 } = dto;

    // Kiểm tra bài viết tồn tại
    const blog = await this.blogRepository.findOne({ where: { id: blogId } });
    if (!blog) {
      throw new NotFoundException(`Blog with ID ${blogId} not found`);
    }

    // Lấy các bình luận gốc (không có parent_comment_id)
    const queryBuilder = this.blogCommentRepository.createQueryBuilder('comment')
      .where('comment.blogId = :blogId', { blogId })
      .andWhere('comment.parentCommentId IS NULL')
      .orderBy('comment.createdAt', 'DESC');

    // Thực hiện phân trang
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Lấy dữ liệu
    const [comments, totalItems] = await queryBuilder.getManyAndCount();

    // Lấy các bình luận trả lời cho mỗi bình luận gốc
    const commentsWithReplies = await Promise.all(
      comments.map(async (comment) => {
        const replies = await this.blogCommentRepository.find({
          where: { parentCommentId: comment.id },
          order: { createdAt: 'ASC' },
        });

        return {
          id: comment.id,
          blog_id: comment.blogId,
          user_id: comment.userId,
          created_at: comment.createdAt,
          content: comment.content,
          author_type: comment.authorType,
          employee_id: comment.employeeId,
          parent_comment_id: comment.parentCommentId,
          replies: replies.map(reply => ({
            id: reply.id,
            blog_id: reply.blogId,
            user_id: reply.userId,
            created_at: reply.createdAt,
            content: reply.content,
            author_type: reply.authorType,
            employee_id: reply.employeeId,
            parent_comment_id: reply.parentCommentId,
          })),
        };
      })
    );

    // Tạo kết quả phân trang
    return {
      content: commentsWithReplies,
      totalItems,
      itemCount: comments.length,
      itemsPerPage: limit,
      totalPages: Math.ceil(totalItems / limit),
      currentPage: page,
    };
  }
}
