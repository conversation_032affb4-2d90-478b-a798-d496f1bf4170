import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Blog, BlogComment, BlogPurchase } from '../entities';
import { BlogPurchaseAdminController } from './controllers';
import { BlogAdminController } from './controllers/blog-admin.controller';
import { BlogCommentAdminController } from './controllers/blog-comment-admin.controller';
import { BlogAdminService, BlogCommentAdminService, BlogPurchaseAdminService } from './services';
import { User } from '@/modules/user/entities/user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Blog, BlogComment, BlogPurchase, User])],
  controllers: [
    // Đăng ký BlogPurchaseAdminController trước để đảm bảo route /admin/blog/purchases được ưu tiên
    BlogPurchaseAdminController,
    BlogCommentAdminController,
    BlogAdminController,
  ],
  providers: [
    BlogAdminService,
    BlogCommentAdminService,
    BlogPurchaseAdminService,
  ],
  exports: [
    BlogAdminService,
    BlogCommentAdminService,
    BlogPurchaseAdminService,
  ],
})
export class BlogAdminModule { }
