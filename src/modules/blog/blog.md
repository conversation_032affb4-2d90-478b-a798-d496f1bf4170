# Phân tích và Đề xuất Giao diện Frontend cho Module Blog

## 1. Tổng quan

Module Blog quản lý các bài viết, bình luận và giao dịch mua bài viết trong hệ thống. Dựa trên phân tích backend, module n<PERSON>y bao gồm các chức năng chính:

- <PERSON><PERSON><PERSON>n lý bài viết (thêm, sửa, xóa, xem)
- Quản lý bình luận
- Quản lý giao dịch mua bài viết
- Phân loại bài viết theo tác giả (người dùng hoặc hệ thống)
- Quản lý trạng thái bài viết (nháp, chờ duy<PERSON>t, đã duyệt)

## 2. C<PERSON>u trúc dữ liệu chính

### Blog (<PERSON><PERSON><PERSON> viế<PERSON>)
- id: ID bài viết
- title: Ti<PERSON><PERSON> đề
- content: Nội dung
- point: <PERSON><PERSON> điểm để mua bài viết
- viewCount: <PERSON><PERSON> lượt xem
- thumbnailUrl: URL ảnh đại diện
- tags: Thẻ (JSON)
- createdAt: Thời gian tạo
- updatedAt: Thời gian cập nhật
- userId: ID người dùng tạo bài viết
- employeeId: ID nhân viên tạo bài viết
- employeeModerator: ID nhân viên kiểm duyệt
- authorType: Loại tác giả (USER, SYSTEM)
- status: Trạng thái (DRAFT, PENDING, APPROVED)
- enable: Trạng thái kích hoạt
- like: Số lượt thích

### BlogComment (Bình luận)
- id: ID bình luận
- blogId: ID bài viết
- userId: ID người dùng bình luận
- content: Nội dung bình luận
- createdAt: Thời gian tạo
- updatedAt: Thời gian cập nhật

### BlogPurchase (Giao dịch mua bài viết)
- id: ID giao dịch
- blogId: ID bài viết
- userId: ID người dùng mua
- point: Số điểm đã trả
- createdAt: Thời gian tạo
- updatedAt: Thời gian cập nhật

## 3. Đề xuất Giao diện Frontend

### 3.1. Giao diện Admin

#### 3.1.1. Trang Quản lý Bài viết
- **Thành phần**: Bảng dữ liệu (DataTable)
- **Chức năng**:
  - Tìm kiếm bài viết theo tiêu đề, nội dung, tags
  - Lọc theo trạng thái (DRAFT, PENDING, APPROVED)
  - Lọc theo loại tác giả (USER, SYSTEM)
  - Phân trang
  - Thêm bài viết mới
  - Sửa thông tin bài viết
  - Duyệt/từ chối bài viết
  - Vô hiệu hóa/kích hoạt bài viết
- **Cột hiển thị**:
  - Ảnh đại diện
  - Tiêu đề
  - Tác giả (Người dùng/Nhân viên)
  - Trạng thái
  - Số điểm
  - Số lượt xem
  - Số lượt thích
  - Ngày tạo
  - Hành động (Sửa, Xóa, Duyệt)

#### 3.1.2. Form Thêm/Sửa Bài viết
- **Thành phần**: Form với các trường
- **Trường dữ liệu**:
  - Tiêu đề (input text)
  - Nội dung (rich text editor)
  - Số điểm (input number)
  - Ảnh đại diện (upload file)
  - Tags (input tags)
  - Trạng thái (select: DRAFT, PENDING, APPROVED)
  - Kích hoạt (toggle/switch)

#### 3.1.3. Trang Chi tiết Bài viết
- **Thành phần**: Hiển thị chi tiết bài viết
- **Chức năng**:
  - Xem nội dung đầy đủ
  - Xem thông tin tác giả
  - Xem danh sách bình luận
  - Duyệt/từ chối bài viết
  - Sửa/xóa bài viết

#### 3.1.4. Trang Quản lý Bình luận
- **Thành phần**: Bảng dữ liệu (DataTable)
- **Chức năng**:
  - Tìm kiếm bình luận theo nội dung
  - Lọc theo bài viết
  - Phân trang
  - Xóa bình luận
- **Cột hiển thị**:
  - ID
  - Bài viết
  - Người dùng
  - Nội dung
  - Ngày tạo
  - Hành động (Xóa)

#### 3.1.5. Trang Quản lý Giao dịch Mua Bài viết
- **Thành phần**: Bảng dữ liệu (DataTable)
- **Chức năng**:
  - Tìm kiếm giao dịch theo bài viết, người dùng
  - Lọc theo khoảng thời gian
  - Phân trang
  - Xuất báo cáo
- **Cột hiển thị**:
  - ID
  - Bài viết
  - Người dùng
  - Số điểm
  - Ngày tạo

### 3.2. Giao diện Người dùng

#### 3.2.1. Trang Danh sách Bài viết
- **Thành phần**: Grid/List hiển thị bài viết
- **Chức năng**:
  - Tìm kiếm bài viết theo tiêu đề, nội dung, tags
  - Lọc theo danh mục
  - Phân trang
  - Hiển thị bài viết nổi bật
- **Hiển thị cho mỗi bài viết**:
  - Ảnh đại diện
  - Tiêu đề
  - Tóm tắt nội dung
  - Tác giả
  - Số lượt xem
  - Số lượt thích
  - Tags

#### 3.2.2. Trang Chi tiết Bài viết
- **Thành phần**: Hiển thị chi tiết bài viết
- **Chức năng**:
  - Xem nội dung đầy đủ
  - Thích bài viết
  - Bình luận
  - Mua bài viết (nếu cần)
  - Chia sẻ bài viết
- **Hiển thị**:
  - Ảnh đại diện
  - Tiêu đề
  - Nội dung đầy đủ
  - Thông tin tác giả
  - Ngày đăng
  - Số lượt xem
  - Số lượt thích
  - Tags
  - Danh sách bình luận
  - Bài viết liên quan

#### 3.2.3. Form Tạo/Sửa Bài viết (cho người dùng)
- **Thành phần**: Form với các trường
- **Trường dữ liệu**:
  - Tiêu đề (input text)
  - Nội dung (rich text editor)
  - Ảnh đại diện (upload file)
  - Tags (input tags)
  - Nút "Lưu nháp" và "Gửi duyệt"

#### 3.2.4. Trang Quản lý Bài viết Cá nhân
- **Thành phần**: Bảng/Grid hiển thị bài viết của người dùng
- **Chức năng**:
  - Xem danh sách bài viết đã tạo
  - Lọc theo trạng thái (DRAFT, PENDING, APPROVED)
  - Thêm bài viết mới
  - Sửa bài viết
  - Xóa bài viết
- **Hiển thị cho mỗi bài viết**:
  - Ảnh đại diện
  - Tiêu đề
  - Trạng thái
  - Số lượt xem
  - Số lượt thích
  - Ngày tạo
  - Hành động (Sửa, Xóa)

## 4. Đề xuất Cấu trúc Component

### 4.1. Components chung
- **Layout**: AdminLayout và UserLayout
- **AuthGuard**: Bảo vệ các route yêu cầu đăng nhập
- **DataTable**: Component bảng dữ liệu với phân trang, sắp xếp, tìm kiếm
- **RichTextEditor**: Editor văn bản phong phú
- **ImageUpload**: Component upload ảnh
- **TagInput**: Component nhập tags
- **Modal**: Component modal cho các form thêm/sửa
- **Toast/Notification**: Hiển thị thông báo thành công/lỗi

### 4.2. Components cho Admin
- **BlogList**: Danh sách bài viết
- **BlogForm**: Form thêm/sửa bài viết
- **BlogDetail**: Chi tiết bài viết
- **CommentList**: Danh sách bình luận
- **PurchaseList**: Danh sách giao dịch mua bài viết

### 4.3. Components cho Người dùng
- **BlogGrid**: Hiển thị bài viết dạng lưới
- **BlogCard**: Card hiển thị thông tin tóm tắt bài viết
- **BlogDetail**: Chi tiết bài viết
- **CommentSection**: Phần bình luận
- **BlogForm**: Form tạo/sửa bài viết
- **MyBlogs**: Quản lý bài viết cá nhân

### 4.4. Đề xuất Routing

#### Admin Routes
```
/admin
  /blogs                  # Danh sách bài viết
    /create               # Thêm bài viết mới
    /:id                  # Chi tiết bài viết
    /:id/edit             # Sửa thông tin bài viết
  /comments               # Danh sách bình luận
  /purchases              # Danh sách giao dịch mua bài viết
```

#### User Routes
```
/blogs                    # Danh sách bài viết
  /:id                    # Chi tiết bài viết
/my-blogs                 # Bài viết của tôi
  /create                 # Tạo bài viết mới
  /:id/edit               # Sửa bài viết
```

## 5. Đề xuất State Management

### 5.1. Redux/Context API Store
- **blogs**: Danh sách bài viết, loading state, error state
- **comments**: Danh sách bình luận, loading state, error state
- **purchases**: Danh sách giao dịch mua, loading state, error state
- **ui**: Trạng thái UI (filters, search, pagination)

### 5.2. Actions/Reducers
- **blogs**: fetchBlogs, createBlog, updateBlog, deleteBlog, approveBlog, likeBlog
- **comments**: fetchComments, createComment, deleteComment
- **purchases**: fetchPurchases, createPurchase

## 6. Đề xuất Công nghệ

- **Framework**: React
- **UI Library**: Material-UI hoặc Ant Design
- **State Management**: Redux Toolkit hoặc Context API
- **Form Handling**: Formik hoặc React Hook Form
- **Rich Text Editor**: CKEditor, TinyMCE hoặc Quill
- **Validation**: Yup
- **HTTP Client**: Axios
- **Routing**: React Router
- **Image Upload**: React Dropzone
- **Internationalization**: i18next

## 7. Mockups

Dưới đây là đề xuất mockup cho một số màn hình chính:

### 7.1. Trang Danh sách Bài viết (Admin)
```
+----------------------------------+
| [Sidebar] | Header               |
|           | [Thêm mới] [Tìm kiếm]|
|           |                      |
|           | Lọc: [Trạng thái] [Tác giả] |
|           |                      |
|           | +------------------+ |
|           | | Danh sách bài    | |
|           | | viết (DataTable) | |
|           | |                  | |
|           | |                  | |
|           | |                  | |
|           | |                  | |
|           | +------------------+ |
|           | [Pagination]         |
+----------------------------------+
```

### 7.2. Form Thêm/Sửa Bài viết (Admin)
```
+----------------------------------+
| [Sidebar] | Header               |
|           |                      |
|           | [Thêm bài viết mới]  |
|           |                      |
|           | +------------------+ |
|           | | Tiêu đề          | |
|           | +------------------+ |
|           |                      |
|           | +------------------+ |
|           | | Nội dung         | |
|           | | (Rich Text Editor)| |
|           | |                  | |
|           | |                  | |
|           | +------------------+ |
|           |                      |
|           | +------------------+ |
|           | | Số điểm          | |
|           | +------------------+ |
|           |                      |
|           | +------------------+ |
|           | | Ảnh đại diện     | |
|           | | [Upload]         | |
|           | +------------------+ |
|           |                      |
|           | +------------------+ |
|           | | Tags             | |
|           | +------------------+ |
|           |                      |
|           | +------------------+ |
|           | | Trạng thái [Select]|
|           | +------------------+ |
|           |                      |
|           | | Kích hoạt [On/Off]|
|           | +------------------+ |
|           |                      |
|           | [Lưu] [Hủy]         |
+----------------------------------+
```

### 7.3. Trang Danh sách Bài viết (Người dùng)
```
+----------------------------------+
| [Header]                         |
| [Banner/Search]                  |
|                                  |
| [Filters/Categories]             |
|                                  |
| +--------+ +--------+ +--------+ |
| | Blog   | | Blog   | | Blog   | |
| | Card   | | Card   | | Card   | |
| |        | |        | |        | |
| |        | |        | |        | |
| +--------+ +--------+ +--------+ |
|                                  |
| +--------+ +--------+ +--------+ |
| | Blog   | | Blog   | | Blog   | |
| | Card   | | Card   | | Card   | |
| |        | |        | |        | |
| |        | |        | |        | |
| +--------+ +--------+ +--------+ |
|                                  |
| [Pagination]                     |
|                                  |
| [Footer]                         |
+----------------------------------+
```

### 7.4. Trang Chi tiết Bài viết (Người dùng)
```
+----------------------------------+
| [Header]                         |
|                                  |
| [Breadcrumb]                     |
|                                  |
| [Tiêu đề bài viết]               |
| [Thông tin tác giả] [Ngày đăng]  |
|                                  |
| [Ảnh đại diện lớn]               |
|                                  |
| [Nội dung bài viết]              |
| ...                              |
| ...                              |
|                                  |
| [Tags] [Lượt xem] [Lượt thích]   |
|                                  |
| [Nút Thích] [Nút Chia sẻ]        |
|                                  |
| [Phần bình luận]                 |
| +----------------------------+   |
| | [Avatar] [Tên người dùng]  |   |
| | [Nội dung bình luận]       |   |
| +----------------------------+   |
| ...                              |
|                                  |
| [Form thêm bình luận]            |
|                                  |
| [Bài viết liên quan]             |
| +--------+ +--------+ +--------+ |
| | Blog   | | Blog   | | Blog   | |
| | Card   | | Card   | | Card   | |
| +--------+ +--------+ +--------+ |
|                                  |
| [Footer]                         |
+----------------------------------+
```

## 8. Kết luận

Dựa trên phân tích backend, module Blog cần một giao diện frontend đầy đủ để quản lý bài viết, bình luận và giao dịch mua bài viết. Giao diện đề xuất tập trung vào trải nghiệm người dùng tốt, dễ sử dụng và đáp ứng đầy đủ các chức năng của backend.

Việc triển khai nên được thực hiện theo từng giai đoạn, bắt đầu từ các chức năng cơ bản như hiển thị và quản lý bài viết, sau đó mở rộng đến bình luận và giao dịch mua bài viết.
