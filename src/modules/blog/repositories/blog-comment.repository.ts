import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { BlogComment } from '../entities/blog-comment.entity';

@Injectable()
export class BlogCommentRepository extends Repository<BlogComment> {
  constructor(private dataSource: DataSource) {
    super(BlogComment, dataSource.createEntityManager());
  }

  /**
   * <PERSON><PERSON><PERSON> tất cả bình luận gốc và phản hồi của một bài viết
   * Sử dụng SQL thuần với recursive CTE
   */
  async findCommentsWithReplies(blogId: number): Promise<any[]> {
    const query = `
      WITH RECURSIVE comment_tree AS (
        -- <PERSON><PERSON>y các bình luận gốc (không có parent)
        SELECT 
          c.id, 
          c.content, 
          c.created_at, 
          c.user_id, 
          c.employee_id,
          c.author_type,
          c.parent_comment_id,
          0 as level,
          ARRAY[c.id] as path
        FROM blog_comments c
        WHERE c.blog_id = $1 AND c.parent_comment_id IS NULL
        
        UNION ALL
        
        -- <PERSON><PERSON><PERSON> c<PERSON><PERSON> bình luậ<PERSON> con (có parent)
        SELECT 
          c.id, 
          c.content, 
          c.created_at, 
          c.user_id, 
          c.employee_id,
          c.author_type,
          c.parent_comment_id,
          ct.level + 1,
          ct.path || c.id
        FROM blog_comments c
        JOIN comment_tree ct ON c.parent_comment_id = ct.id
      )
      SELECT 
        ct.*,
        u.full_name as user_name,
        u.avatar as user_avatar
      FROM comment_tree ct
      LEFT JOIN users u ON ct.user_id = u.id
      ORDER BY path, level
    `;
    
    return this.query(query, [blogId]);
  }

  /**
   * Đếm số lượng bình luận theo bài viết
   * Sử dụng SQL thuần
   */
  async countCommentsByBlog(blogIds: number[]): Promise<any[]> {
    if (blogIds.length === 0) {
      return [];
    }
    
    const placeholders = blogIds.map((_, index) => `$${index + 1}`).join(',');
    
    const query = `
      SELECT 
        blog_id,
        COUNT(*) as comment_count
      FROM blog_comments
      WHERE blog_id IN (${placeholders})
      GROUP BY blog_id
    `;
    
    return this.query(query, blogIds);
  }

  /**
   * Lấy danh sách bình luận mới nhất
   * Sử dụng SQL thuần
   */
  async findLatestComments(limit: number = 10): Promise<any[]> {
    const query = `
      SELECT 
        c.*,
        b.title as blog_title,
        u.full_name as user_name,
        u.avatar as user_avatar
      FROM blog_comments c
      JOIN blogs b ON c.blog_id = b.id
      LEFT JOIN users u ON c.user_id = u.id
      WHERE b.enable = true AND b.status = 'APPROVED'
      ORDER BY c.created_at DESC
      LIMIT $1
    `;
    
    return this.query(query, [limit]);
  }
}
