import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, ArrayMinSize, ArrayUnique } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho yêu cầu xóa nhiều đơn hàng thanh toán
 */
export class BulkDeletePaymentDto {
  @ApiProperty({
    description: 'Danh sách ID đơn hàng cần xóa',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'Phải có ít nhất một đơn hàng để xóa' })
  @ArrayUnique({ message: 'Danh sách đơn hàng không được trùng lặp' })
  @IsNumber({}, { each: true, message: 'ID đơn hàng phải là số' })
  @Type(() => Number)
  @IsNotEmpty({ message: 'Danh sách đơn hàng không được để trống' })
  orderIds: number[];
}

/**
 * DTO cho response xóa nhiều đơn hàng
 */
export class BulkDeletePaymentResponseDto {
  @ApiProperty({
    description: 'Số lượng đơn hàng đã xóa thành công',
    example: 2
  })
  deletedCount: number;

  @ApiProperty({
    description: 'Số lượng đơn hàng không thể xóa',
    example: 1
  })
  failedCount: number;

  @ApiProperty({
    description: 'Danh sách ID đơn hàng đã xóa thành công',
    example: [1, 2],
    type: [Number]
  })
  deletedOrderIds: number[];

  @ApiProperty({
    description: 'Danh sách ID đơn hàng không thể xóa',
    example: [3],
    type: [Number]
  })
  failedOrderIds: number[];

  @ApiProperty({
    description: 'Thông báo kết quả',
    example: 'Đã xóa 2 đơn hàng thành công, 1 đơn hàng không thể xóa'
  })
  message: string;
}
