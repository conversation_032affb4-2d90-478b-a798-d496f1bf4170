import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsInt, IsNotEmpty, Min } from 'class-validator';

/**
 * DTO cho việc thêm sản phẩm vào giỏ hàng
 */
export class AddToCartDto {
  @ApiProperty({
    description: 'ID sản phẩm',
    example: 123,
  })
  @IsNotEmpty()
  @IsInt()
  @Type(() => Number)
  productId: number;

  @ApiProperty({
    description: 'Số lượng',
    example: 2,
    minimum: 1,
  })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  quantity: number;
}
