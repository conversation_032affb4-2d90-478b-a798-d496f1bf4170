import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  <PERSON>Length,
  Min,
  MinLength,
} from 'class-validator';
import { ProductCategory, ProductStatus } from '@modules/marketplace/enums';

/**
 * DTO cho việc tạo sản phẩm mới
 */
export class CreateProductDto {
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'AI Chatbot Template',
    maxLength: 500,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(500)
  name: string;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Mẫu chatbot AI hỗ trợ khách hàng tự động',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  /**
   * <PERSON>h sách ảnh sản phẩm (không hiển thị trong Swagger, chỉ dùng nội bộ)
   * @internal
   */
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  images?: string[];

  @ApiProperty({
    description: 'Giá niêm yết',
    example: 1200,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  listedPrice: number;

  @ApiProperty({
    description: 'Giá sau giảm',
    example: 1000,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  discountedPrice: number;

  @ApiProperty({
    description: 'Loại sản phẩm',
    enum: ProductCategory,
    example: ProductCategory.KNOWLEDGE_FILE,
  })
  @IsEnum(ProductCategory)
  category: ProductCategory;

  /**
   * Hướng dẫn sử dụng (không hiển thị trong Swagger, chỉ dùng nội bộ)
   * @internal
   */
  @IsOptional()
  @IsString()
  userManual?: string;

  /**
   * Thông tin chi tiết (không hiển thị trong Swagger, chỉ dùng nội bộ)
   * @internal
   */
  @IsOptional()
  @IsString()
  detail?: string;

  @ApiProperty({
    description: 'ID nguồn sản phẩm',
    example: '34f5c7ef-649a-46e2-a399-34fc7c197032',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  sourceId?: string;

  @ApiProperty({
    description: 'Danh sách loại media cho hình ảnh sản phẩm',
    type: [String],
    example: ['image/jpeg'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  imagesMediaTypes?: string[];

  @ApiProperty({
    description: 'Loại media cho hướng dẫn sử dụng',
    example: 'text/html',
    required: false,
  })
  @IsOptional()
  @IsString()
  userManualMediaType?: string;

  @ApiProperty({
    description: 'Loại media cho thông tin chi tiết',
    example: 'text/html',
    required: false,
  })
  @IsOptional()
  @IsString()
  detailMediaType?: string;

  /**
   * Trạng thái sản phẩm (không hiển thị trong Swagger, mặc định là DRAFT)
   * @internal
   */
  @IsOptional()
  @IsEnum(ProductStatus)
  status?: ProductStatus;
}

/**
 * Phản hồi khi tạo sản phẩm mới
 */
export class CreateProductResponseDto {
  @ApiProperty({
    description: 'ID sản phẩm',
    example: '27',
  })
  id: string;

  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'AI Chatbot Template',
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Mẫu chatbot AI hỗ trợ khách hàng tự động',
  })
  description: string;

  @ApiProperty({
    description: 'Giá niêm yết',
    example: '1200',
  })
  listedPrice: string;

  @ApiProperty({
    description: 'Giá sau giảm',
    example: '1000',
  })
  discountedPrice: string;

  @ApiProperty({
    description: 'Loại sản phẩm',
    example: 'KNOWLEDGE_FILE',
  })
  category: string;

  @ApiProperty({
    description: 'ID nguồn sản phẩm',
    example: '34f5c7ef-649a-46e2-a399-34fc7c197032',
    required: false,
  })
  sourceId?: string;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: '1746348513355',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Thời gian cập nhật (timestamp)',
    example: '1746348513355',
  })
  updatedAt: string;

  @ApiProperty({
    description: 'Thông tin người bán',
    example: {
      id: 123,
      name: 'Nguyễn Văn A',
      avatar: 'avatars/user-123/avatar-1234567890.jpg',
      email: '<EMAIL>',
      phoneNumber: '0987654321',
      type: 'user',
    },
  })
  seller: {
    id?: number;
    name: string;
    avatar: string;
    email?: string;
    phoneNumber?: string;
    type: string;
  };

  @ApiProperty({
    description: 'Trạng thái sản phẩm',
    example: 'DRAFT',
  })
  status: string;

  @ApiProperty({
    description: 'URLs để upload tệp',
    example: {
      productId: '27',
      imagesUploadUrls: [
        {
          url: 'https://cdn.redai.vn/marketplace/IMAGE/2025/05/product-image...',
          key: 'marketplace/IMAGE/2025/05/product-image...',
          index: 0,
        },
      ],
      userManualUploadUrl: {
        url: 'https://cdn.redai.vn/marketplace/DOCUMENT/2025/05/product-manual...',
        key: 'marketplace/DOCUMENT/2025/05/product-manual...',
        expiresAt: 1746350313351,
      },
      detailUploadUrl: {
        url: 'https://cdn.redai.vn/marketplace/DOCUMENT/2025/05/product-detail...',
        key: 'marketplace/DOCUMENT/2025/05/product-detail...',
        expiresAt: 1746350313354,
      },
    },
  })
  uploadUrls: {
    productId: string;
    imagesUploadUrls: Array<{
      url: string;
      key: string;
      index: number;
    }>;
    userManualUploadUrl?: {
      url: string;
      key: string;
      expiresAt: number;
    };
    detailUploadUrl?: {
      url: string;
      key: string;
      expiresAt: number;
    };
  };
}