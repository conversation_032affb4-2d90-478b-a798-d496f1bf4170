import { ApiProperty } from '@nestjs/swagger';
import { ProductDetailResponseDto } from './product-detail-response.dto';

/**
 * DTO cho thông tin URL upload ảnh sản phẩm
 */
export class ImageUploadUrlDto {
  @ApiProperty({
    description: 'URL để upload ảnh',
    example: 'https://example.com/upload/image1.jpg',
  })
  url: string;

  @ApiProperty({
    description: 'Key của ảnh',
    example: 'marketplace/IMAGE/2023/01/01/product-image-0-123456789-1',
  })
  key: string;

  @ApiProperty({
    description: 'Chỉ số của ảnh',
    example: 0,
  })
  index: number;
}

/**
 * DTO cho thông tin URL upload của sản phẩm
 */
export class ProductUploadUrlsDto {
  @ApiProperty({
    description: 'ID của sản phẩm',
    example: '123',
  })
  productId: string;

  @ApiProperty({
    description: 'Danh sách URL upload ảnh sản phẩm',
    type: [ImageUploadUrlDto],
  })
  imagesUploadUrls: ImageUploadUrlDto[];

  @ApiProperty({
    description: 'URL upload hướng dẫn sử dụng',
    example: 'https://example.com/upload/manual.pdf',
    required: false,
    nullable: true,
  })
  userManualUploadUrl?: string;

  @ApiProperty({
    description: 'URL upload thông tin chi tiết',
    example: 'https://example.com/upload/detail.pdf',
    required: false,
    nullable: true,
  })
  detailUploadUrl?: string;
}

/**
 * DTO cho response khi tạo sản phẩm mới
 */
export class CreateProductResponseDto {
  @ApiProperty({
    description: 'Thông tin sản phẩm đã tạo',
    type: ProductDetailResponseDto,
  })
  product: ProductDetailResponseDto;

  @ApiProperty({
    description: 'Thông tin URL upload tài liệu sản phẩm',
    type: ProductUploadUrlsDto,
  })
  uploadUrls: ProductUploadUrlsDto;
}
