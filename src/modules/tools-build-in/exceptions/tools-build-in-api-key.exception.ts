import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi cho API Key trong module Tools Build-In
 * Range: 30200-30299
 */
export const TOOLS_BUILD_IN_API_KEY_ERROR_CODES = {
  /**
   * API Key không được cung cấp
   */
  API_KEY_MISSING: new ErrorCode(
    30201,
    'API Key không được cung cấp',
    HttpStatus.UNAUTHORIZED,
  ),

  /**
   * API Key không hợp lệ
   */
  API_KEY_INVALID: new ErrorCode(
    30202,
    'API Key không hợp lệ',
    HttpStatus.UNAUTHORIZED,
  ),

  /**
   * API Key đã hết hạn
   */
  API_KEY_EXPIRED: new ErrorCode(
    30203,
    'API Key đã hết hạn',
    HttpStatus.UNAUTHORIZED,
  ),

  /**
   * API Key không có quyền truy cập
   */
  API_KEY_FORBIDDEN: new ErrorCode(
    30204,
    'API Key không có quyền truy cập',
    HttpStatus.FORBIDDEN,
  ),

  /**
   * Agent không tồn tại
   */
  AGENT_NOT_FOUND: new ErrorCode(
    30205,
    'Agent không tồn tại',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Agent không hoạt động
   */
  AGENT_INACTIVE: new ErrorCode(
    30206,
    'Agent không hoạt động',
    HttpStatus.FORBIDDEN,
  ),
};
