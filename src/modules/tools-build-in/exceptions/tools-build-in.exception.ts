import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

export const TOOLS_BUILD_IN_ERROR_CODES = {
  // Facebook error codes
  FACEBOOK_API_ERROR: new ErrorCode(
    30001,
    'Lỗi khi gọi Facebook API',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  FACEBOOK_AUTH_ERROR: new ErrorCode(
    30002,
    'Lỗi xác thực Facebook',
    HttpStatus.UNAUTHORIZED,
  ),
  FACEBOOK_PERMISSION_ERROR: new ErrorCode(
    30003,
    'Không có quyền truy cập Facebook',
    HttpStatus.FORBIDDEN,
  ),

  // Website error codes
  WEBSITE_FETCH_ERROR: new ErrorCode(
    30101,
    'Lỗi khi lấy thông tin website',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  WEBSITE_ANALYSIS_ERROR: new ErrorCode(
    30102,
    'Lỗi khi phân tích website',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  WEBSITE_SEO_ERROR: new ErrorCode(
    30103,
    'Lỗi khi phân tích SEO website',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  INVALID_URL: new ErrorCode(
    30104,
    'URL không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  // Address error codes
  ADDRESS_FETCH_ERROR: new ErrorCode(
    30201,
    'Lỗi khi lấy thông tin địa chỉ',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  ADDRESS_SEARCH_ERROR: new ErrorCode(
    30202,
    'Lỗi khi tìm kiếm địa chỉ',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  SHIPPING_CALCULATION_ERROR: new ErrorCode(
    30203,
    'Lỗi khi tính phí vận chuyển',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Product error codes
  PRODUCT_NOT_FOUND: new ErrorCode(
    30301,
    'Không tìm thấy sản phẩm',
    HttpStatus.NOT_FOUND,
  ),
  PRODUCT_FETCH_ERROR: new ErrorCode(
    30302,
    'Lỗi khi lấy thông tin sản phẩm',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  PRODUCT_SEARCH_ERROR: new ErrorCode(
    30303,
    'Lỗi khi tìm kiếm sản phẩm',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  PRODUCT_UPLOAD_ERROR: new ErrorCode(
    30304,
    'Lỗi khi tải lên hình ảnh sản phẩm',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  PRODUCT_TEMPLATE_ERROR: new ErrorCode(
    30305,
    'Lỗi khi tạo mẫu sản phẩm',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  PRODUCT_ACCESS_DENIED: new ErrorCode(
    30306,
    'Không có quyền truy cập sản phẩm',
    HttpStatus.FORBIDDEN,
  ),
  INVALID_S3_KEY: new ErrorCode(
    30307,
    'Key S3 không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  // Conversion error codes
  CUSTOMER_NOT_FOUND: new ErrorCode(
    30401,
    'Không tìm thấy khách hàng',
    HttpStatus.NOT_FOUND,
  ),
  CUSTOMER_FETCH_ERROR: new ErrorCode(
    30402,
    'Lỗi khi lấy thông tin khách hàng',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERSION_NOT_FOUND: new ErrorCode(
    30403,
    'Không tìm thấy chuyển đổi',
    HttpStatus.NOT_FOUND,
  ),
  CONVERSION_FETCH_ERROR: new ErrorCode(
    30404,
    'Lỗi khi lấy thông tin chuyển đổi',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CONVERSION_SEARCH_ERROR: new ErrorCode(
    30405,
    'Lỗi khi tìm kiếm chuyển đổi',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  CUSTOMER_UPLOAD_ERROR: new ErrorCode(
    30406,
    'Lỗi khi tải lên avatar khách hàng',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Send error codes
  EMAIL_SEND_ERROR: new ErrorCode(
    30501,
    'Lỗi khi gửi email',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  SMS_SEND_ERROR: new ErrorCode(
    30502,
    'Lỗi khi gửi SMS',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  PUSH_NOTIFICATION_ERROR: new ErrorCode(
    30503,
    'Lỗi khi gửi thông báo đẩy',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  EMAIL_HISTORY_ERROR: new ErrorCode(
    30504,
    'Lỗi khi lấy lịch sử email',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  SMS_HISTORY_ERROR: new ErrorCode(
    30505,
    'Lỗi khi lấy lịch sử SMS',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Agent Tools error codes
  AGENT_NOT_FOUND: new ErrorCode(
    30601,
    'Không tìm thấy agent',
    HttpStatus.NOT_FOUND,
  ),
  AGENT_TYPE_NOT_FOUND: new ErrorCode(
    30602,
    'Không tìm thấy loại agent',
    HttpStatus.NOT_FOUND,
  ),
  AGENT_TOOLS_FETCH_FAILED: new ErrorCode(
    30603,
    'Lỗi khi lấy danh sách tool của agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
};
