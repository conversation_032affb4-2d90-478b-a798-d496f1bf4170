import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserKeyRepository } from '../../repositories';
import { AiProvider, UserKey } from '../../entities';
import { AppException, ErrorCode } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import {
  AiProviderResponseDto,
  CreateUserKeyDto,
  TestUserKeyDto,
  UpdateUserKeyDto,
  UserKeyQueryDto,
  UserKeyResponseDto
} from '../dto';
import axios from 'axios';

@Injectable()
export class UserKeyUserService {
  private readonly logger = new Logger(UserKeyUserService.name);

  constructor(
    private readonly userKeyRepository: UserKeyRepository,
    @InjectRepository(AiProvider)
    private readonly aiProviderRepository: Repository<AiProvider>
  ) {}

  /**
   * <PERSON><PERSON><PERSON> danh sách API key của người dùng
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách API key đã phân trang
   */
  async findUserKeys(userId: number, queryDto: UserKeyQueryDto): Promise<PaginatedResult<UserKeyResponseDto>> {
    try {
      const { page = 1, limit = 10, providerKey } = queryDto;

      // Tạo query builder
      const queryBuilder = this.userKeyRepository
        .createQueryBuilder('userKey')
        .leftJoinAndSelect('ai_providers', 'provider', 'userKey.providerId = provider.id')
        .where('userKey.userId = :userId', { userId });

      // Thêm điều kiện tìm kiếm theo providerKey nếu có
      if (providerKey) {
        queryBuilder.andWhere('provider.providerKey LIKE :providerKey', { providerKey: `%${providerKey}%` });
      }

      // Đếm tổng số bản ghi
      const totalItems = await queryBuilder.getCount();

      // Lấy dữ liệu phân trang
      const items = await queryBuilder
        .skip((page - 1) * limit)
        .take(limit)
        .orderBy('userKey.id', 'DESC')
        .getRawMany();

      // Chuyển đổi dữ liệu thành DTO
      const userKeys = items.map(item => this.mapToUserKeyResponseDto(item));

      return {
        items: userKeys,
        meta: {
          totalItems,
          itemCount: userKeys.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page
        }
      };
    } catch (error) {
      this.logger.error(`Error finding user keys: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách API key'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết API key của người dùng
   * @param userId ID của người dùng
   * @param id ID của API key
   * @returns Thông tin chi tiết API key
   */
  async findUserKeyById(userId: number, id: number): Promise<UserKeyResponseDto> {
    try {
      const userKey = await this.userKeyRepository
        .createQueryBuilder('userKey')
        .leftJoinAndSelect('ai_providers', 'provider', 'userKey.providerId = provider.id')
        .where('userKey.id = :id', { id })
        .andWhere('userKey.userId = :userId', { userId })
        .getRawOne();

      if (!userKey) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy API key với ID ${id}`
        );
      }

      return this.mapToUserKeyResponseDto(userKey);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error finding user key by ID: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy thông tin API key'
      );
    }
  }

  /**
   * Tạo API key mới cho người dùng
   * @param userId ID của người dùng
   * @param createUserKeyDto Thông tin API key cần tạo
   * @returns API key đã tạo
   */
  async createUserKey(userId: number, createUserKeyDto: CreateUserKeyDto): Promise<UserKeyResponseDto> {
    try {
      // Kiểm tra nhà cung cấp tồn tại
      const provider = await this.aiProviderRepository.findOne({
        where: { id: createUserKeyDto.providerId }
      });

      if (!provider) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy nhà cung cấp AI với ID ${createUserKeyDto.providerId}`
        );
      }

      // Kiểm tra xem người dùng đã có API key cho nhà cung cấp này chưa
      const existingKey = await this.userKeyRepository.findOne({
        where: {
          userId,
          providerId: createUserKeyDto.providerId
        }
      });

      if (existingKey) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          `Bạn đã có API key cho nhà cung cấp ${provider.name}. Vui lòng cập nhật key hiện có thay vì tạo mới.`
        );
      }

      // Tạo API key mới
      const newUserKey = this.userKeyRepository.create({
        userId,
        providerId: createUserKeyDto.providerId,
        credentials: createUserKeyDto.credentials,
        settings: createUserKeyDto.settings || { is_active: true, is_default: false }
      });

      // Lưu vào database
      const savedUserKey = await this.userKeyRepository.save(newUserKey);

      // Trả về kết quả
      return {
        id: savedUserKey.id,
        userId: savedUserKey.userId,
        providerId: savedUserKey.providerId,
        provider: {
          id: provider.id,
          providerKey: provider.providerKey,
          name: provider.name,
          icon: provider.icon
        },
        credentials: this.maskCredentials(savedUserKey.credentials),
        settings: savedUserKey.settings
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error creating user key: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi tạo API key mới'
      );
    }
  }

  /**
   * Cập nhật API key của người dùng
   * @param userId ID của người dùng
   * @param id ID của API key
   * @param updateUserKeyDto Thông tin cần cập nhật
   * @returns API key đã cập nhật
   */
  async updateUserKey(userId: number, id: number, updateUserKeyDto: UpdateUserKeyDto): Promise<UserKeyResponseDto> {
    try {
      // Kiểm tra API key tồn tại
      const userKey = await this.userKeyRepository.findOne({
        where: { id, userId }
      });

      if (!userKey) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy API key với ID ${id}`
        );
      }

      // Cập nhật thông tin
      if (updateUserKeyDto.credentials) {
        userKey.credentials = updateUserKeyDto.credentials;
      }

      if (updateUserKeyDto.settings) {
        userKey.settings = updateUserKeyDto.settings;
      }

      // Lưu vào database
      await this.userKeyRepository.save(userKey);

      // Lấy thông tin nhà cung cấp
      const provider = await this.aiProviderRepository.findOne({
        where: { id: userKey.providerId }
      });

      if (!provider) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy nhà cung cấp AI với ID ${userKey.providerId}`
        );
      }

      // Trả về kết quả
      return {
        id: userKey.id,
        userId: userKey.userId,
        providerId: userKey.providerId,
        provider: {
          id: provider.id,
          providerKey: provider.providerKey,
          name: provider.name,
          icon: provider.icon
        },
        credentials: this.maskCredentials(userKey.credentials),
        settings: userKey.settings
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error updating user key: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi cập nhật API key'
      );
    }
  }

  /**
   * Xóa API key của người dùng
   * @param userId ID của người dùng
   * @param id ID của API key
   */
  async deleteUserKey(userId: number, id: number): Promise<void> {
    try {
      // Kiểm tra API key tồn tại
      const userKey = await this.userKeyRepository.findOne({
        where: { id, userId }
      });

      if (!userKey) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy API key với ID ${id}`
        );
      }

      // Xóa API key
      await this.userKeyRepository.remove(userKey);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error deleting user key: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi xóa API key'
      );
    }
  }

  /**
   * Lấy danh sách nhà cung cấp AI
   * @returns Danh sách nhà cung cấp AI
   */
  async findAiProviders(): Promise<AiProviderResponseDto[]> {
    try {
      const providers = await this.aiProviderRepository.find({
        order: { name: 'ASC' }
      });

      return providers.map(provider => ({
        id: provider.id,
        providerKey: provider.providerKey,
        name: provider.name,
        icon: provider.icon,
        baseUrl: provider.baseUrl
      }));
    } catch (error) {
      this.logger.error(`Error finding AI providers: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách nhà cung cấp AI'
      );
    }
  }

  /**
   * Kiểm tra API key
   * @param testUserKeyDto Thông tin API key cần kiểm tra
   * @returns Kết quả kiểm tra
   */
  async testUserKey(testUserKeyDto: TestUserKeyDto): Promise<{ success: boolean; message: string }> {
    try {
      const { providerKey, credentials, model } = testUserKeyDto;

      // Lấy thông tin nhà cung cấp
      const provider = await this.aiProviderRepository.findOne({
        where: { providerKey }
      });

      if (!provider) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy nhà cung cấp AI với key ${providerKey}`
        );
      }

      // Kiểm tra API key tùy theo nhà cung cấp
      switch (providerKey) {
        case 'openai':
          return await this.testOpenAiKey(credentials, model);
        case 'anthropic':
          return await this.testAnthropicKey(credentials, model);
        case 'gemini':
          return await this.testGeminiKey(credentials, model);
        case 'deepseek':
          return await this.testDeepseekKey(credentials, model);
        default:
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            `Không hỗ trợ kiểm tra API key cho nhà cung cấp ${providerKey}`
          );
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error testing user key: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi kiểm tra API key'
      );
    }
  }

  /**
   * Kiểm tra API key của OpenAI
   * @param credentials Thông tin xác thực
   * @param model Mô hình để kiểm tra
   * @returns Kết quả kiểm tra
   */
  private async testOpenAiKey(credentials: Record<string, any>, model?: string): Promise<{ success: boolean; message: string }> {
    try {
      const apiKey = credentials.api_key;
      const organizationId = credentials.organization_id;

      if (!apiKey) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'API key không được để trống'
        );
      }

      const headers: Record<string, string> = {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      };

      if (organizationId) {
        headers['OpenAI-Organization'] = organizationId;
      }

      // Gọi API để kiểm tra
      await axios.post(
        'https://api.openai.com/v1/chat/completions',
        {
          model: model || 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: 'Hello' }],
          max_tokens: 5
        },
        { headers }
      );

      return {
        success: true,
        message: 'API key hợp lệ'
      };
    } catch (error) {
      if (error.response) {
        const status = error.response.status;
        if (status === 401) {
          return {
            success: false,
            message: 'API key không hợp lệ hoặc đã hết hạn'
          };
        } else if (status === 429) {
          return {
            success: false,
            message: 'Đã vượt quá giới hạn tốc độ hoặc hạn mức'
          };
        } else {
          return {
            success: false,
            message: `Lỗi từ OpenAI: ${error.response.data.error?.message || 'Không xác định'}`
          };
        }
      }

      return {
        success: false,
        message: `Lỗi khi kiểm tra API key: ${error.message}`
      };
    }
  }

  /**
   * Kiểm tra API key của Anthropic
   * @param credentials Thông tin xác thực
   * @param model Mô hình để kiểm tra
   * @returns Kết quả kiểm tra
   */
  private async testAnthropicKey(credentials: Record<string, any>, model?: string): Promise<{ success: boolean; message: string }> {
    try {
      const apiKey = credentials.api_key;

      if (!apiKey) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'API key không được để trống'
        );
      }

      // Gọi API để kiểm tra
      await axios.post(
        'https://api.anthropic.com/v1/messages',
        {
          model: model || 'claude-3-opus-20240229',
          max_tokens: 5,
          messages: [{ role: 'user', content: 'Hello' }]
        },
        {
          headers: {
            'x-api-key': apiKey,
            'anthropic-version': '2023-06-01',
            'Content-Type': 'application/json'
          }
        }
      );

      return {
        success: true,
        message: 'API key hợp lệ'
      };
    } catch (error) {
      if (error.response) {
        const status = error.response.status;
        if (status === 401) {
          return {
            success: false,
            message: 'API key không hợp lệ hoặc đã hết hạn'
          };
        } else if (status === 429) {
          return {
            success: false,
            message: 'Đã vượt quá giới hạn tốc độ hoặc hạn mức'
          };
        } else {
          return {
            success: false,
            message: `Lỗi từ Anthropic: ${error.response.data.error?.message || 'Không xác định'}`
          };
        }
      }

      return {
        success: false,
        message: `Lỗi khi kiểm tra API key: ${error.message}`
      };
    }
  }

  /**
   * Kiểm tra API key của Gemini
   * @param credentials Thông tin xác thực
   * @param model Mô hình để kiểm tra
   * @returns Kết quả kiểm tra
   */
  private async testGeminiKey(credentials: Record<string, any>, model?: string): Promise<{ success: boolean; message: string }> {
    try {
      const apiKey = credentials.api_key;

      if (!apiKey) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'API key không được để trống'
        );
      }

      // Gọi API để kiểm tra
      await axios.post(
        `https://generativelanguage.googleapis.com/v1/models/${model || 'gemini-pro'}:generateContent?key=${apiKey}`,
        {
          contents: [{ parts: [{ text: 'Hello' }] }],
          generationConfig: { maxOutputTokens: 5 }
        },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      return {
        success: true,
        message: 'API key hợp lệ'
      };
    } catch (error) {
      if (error.response) {
        const status = error.response.status;
        if (status === 400 && error.response.data.error?.status === 'INVALID_ARGUMENT') {
          return {
            success: false,
            message: 'API key không hợp lệ'
          };
        } else if (status === 403) {
          return {
            success: false,
            message: 'API key không có quyền truy cập'
          };
        } else if (status === 429) {
          return {
            success: false,
            message: 'Đã vượt quá giới hạn tốc độ hoặc hạn mức'
          };
        } else {
          return {
            success: false,
            message: `Lỗi từ Gemini: ${error.response.data.error?.message || 'Không xác định'}`
          };
        }
      }

      return {
        success: false,
        message: `Lỗi khi kiểm tra API key: ${error.message}`
      };
    }
  }

  /**
   * Kiểm tra API key của Deepseek
   * @param credentials Thông tin xác thực
   * @param model Mô hình để kiểm tra
   * @returns Kết quả kiểm tra
   */
  private async testDeepseekKey(credentials: Record<string, any>, model?: string): Promise<{ success: boolean; message: string }> {
    try {
      const apiKey = credentials.api_key;

      if (!apiKey) {
        throw new AppException(
          ErrorCode.VALIDATION_ERROR,
          'API key không được để trống'
        );
      }

      // Gọi API để kiểm tra
      await axios.post(
        'https://api.deepseek.com/v1/chat/completions',
        {
          model: model || 'deepseek-chat',
          messages: [{ role: 'user', content: 'Hello' }],
          max_tokens: 5
        },
        {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      return {
        success: true,
        message: 'API key hợp lệ'
      };
    } catch (error) {
      if (error.response) {
        const status = error.response.status;
        if (status === 401) {
          return {
            success: false,
            message: 'API key không hợp lệ hoặc đã hết hạn'
          };
        } else if (status === 429) {
          return {
            success: false,
            message: 'Đã vượt quá giới hạn tốc độ hoặc hạn mức'
          };
        } else {
          return {
            success: false,
            message: `Lỗi từ Deepseek: ${error.response.data.error?.message || 'Không xác định'}`
          };
        }
      }

      return {
        success: false,
        message: `Lỗi khi kiểm tra API key: ${error.message}`
      };
    }
  }

  /**
   * Che giấu thông tin xác thực
   * @param credentials Thông tin xác thực
   * @returns Thông tin xác thực đã che giấu
   */
  private maskCredentials(credentials: Record<string, any>): Record<string, any> {
    const maskedCredentials: Record<string, any> = {};

    for (const key in credentials) {
      if (credentials[key]) {
        if (key.includes('key') || key.includes('secret') || key.includes('password') || key.includes('token')) {
          const value = credentials[key];
          if (typeof value === 'string' && value.length > 8) {
            maskedCredentials[key] = '********' + value.slice(-4);
          } else if (typeof value === 'string' && value.length > 0) {
            maskedCredentials[key] = '********';
          } else {
            maskedCredentials[key] = value;
          }
        } else {
          maskedCredentials[key] = credentials[key];
        }
      } else {
        maskedCredentials[key] = credentials[key];
      }
    }

    return maskedCredentials;
  }

  /**
   * Chuyển đổi dữ liệu thành DTO
   * @param rawData Dữ liệu thô
   * @returns DTO
   */
  private mapToUserKeyResponseDto(rawData: any): UserKeyResponseDto {
    return {
      id: rawData.userKey_id,
      userId: rawData.userKey_userId,
      providerId: rawData.userKey_providerId,
      provider: {
        id: rawData.provider_id,
        providerKey: rawData.provider_providerKey,
        name: rawData.provider_name,
        icon: rawData.provider_icon
      },
      credentials: this.maskCredentials(rawData.userKey_credentials),
      settings: rawData.userKey_settings
    };
  }
}
