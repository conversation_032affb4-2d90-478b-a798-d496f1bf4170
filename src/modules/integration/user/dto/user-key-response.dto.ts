import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho việc trả về thông tin API key của người dùng
 */
export class UserKeyResponseDto {
  @ApiProperty({
    description: 'ID của API key',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'ID của người dùng',
    example: 1
  })
  userId: number;

  @ApiProperty({
    description: 'ID của nhà cung cấp AI',
    example: 1
  })
  providerId: number;

  @ApiProperty({
    description: 'Thông tin về nhà cung cấp AI',
    example: {
      id: 1,
      providerKey: 'openai',
      name: 'OpenAI',
      icon: 'https://example.com/openai-icon.png'
    }
  })
  provider: {
    id: number;
    providerKey: string;
    name: string;
    icon?: string;
  };

  @ApiProperty({
    description: 'Thông tin xác thực API (đã che giấu)',
    example: {
      api_key: '********abcd',
      organization_id: 'org-******'
    }
  })
  credentials: Record<string, any>;

  @ApiProperty({
    description: 'Cài đặt người dùng',
    example: {
      is_active: true,
      is_default: true
    }
  })
  settings: Record<string, any>;
}
