import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IntegrationTypeEnum } from '@modules/integration/enums';

/**
 * DTO response cho thông tin loại tích hợp của người dùng
 */
export class IntegrationTypeResponseDto {
  /**
   * <PERSON><PERSON><PERSON> tích hợp
   * @example "SMS_TWILIO"
   */
  @ApiProperty({
    description: 'Loại tích hợp',
    enum: IntegrationTypeEnum,
    example: IntegrationTypeEnum.SMS
  })
  @Expose()
  type: IntegrationTypeEnum;

  /**
   * Số lượng tích hợp của loại này
   * @example 3
   */
  @ApiProperty({
    description: 'Số lượng tích hợp của loại này',
    type: 'number',
    example: 3
  })
  @Expose()
  count: number;

  /**
   * Thời điểm tạo tích hợp gần nhất của loại này
   * @example "2025-06-14T03:50:44.773Z"
   */
  @ApiProperty({
    description: 'Thời điểm tạo tích hợp gần nhất của loại này',
    type: 'string',
    format: 'date-time',
    example: '2025-06-14T03:50:44.773Z'
  })
  @Expose()
  latestCreatedAt: Date;
}
