import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsBoolean, Length } from 'class-validator';

/**
 * DTO cho việc cập nhật tài khoản WhatsApp Business
 */
export class UpdateWhatsAppAccountDto {
  @ApiProperty({
    description: 'Tên hiển thị của tài khoản',
    example: 'RedAI Customer Support',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Length(3, 255)
  displayName?: string;

  @ApiProperty({
    description: 'Access token để gọi API WhatsApp',
    example: 'EAABZCqZAZCZCZC...',
    required: false,
  })
  @IsOptional()
  @IsString()
  accessToken?: string;

  @ApiProperty({
    description: 'Trạng thái hoạt động của tài khoản',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
