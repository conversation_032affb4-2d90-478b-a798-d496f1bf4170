import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsObject, IsEnum, Length, Matches } from 'class-validator';

/**
 * Enum cho loại mẫu tin nhắn WhatsApp
 */
export enum WhatsAppTemplateCategory {
  MARKETING = 'MARKETING',
  UTILITY = 'UTILITY',
  AUTHENTICATION = 'AUTHENTICATION',
}

/**
 * DTO cho việc tạo mới mẫu tin nhắn WhatsApp
 */
export class CreateWhatsAppTemplateDto {
  @ApiProperty({
    description: 'Tên mẫu tin nhắn (chỉ chữ cái, số và gạch dưới)',
    example: 'welcome_message',
  })
  @IsNotEmpty()
  @IsString()
  @Length(1, 255)
  @Matches(/^[a-z0-9_]+$/, {
    message: 'Tên mẫu tin nhắn chỉ được chứa chữ cái thường, số và gạch dưới',
  })
  name: string;

  @ApiProperty({
    description: '<PERSON>ôn ngữ của mẫu tin nhắn (mã ISO)',
    example: 'vi',
  })
  @IsNotEmpty()
  @IsString()
  @Length(2, 10)
  language: string;

  @ApiProperty({
    description: 'Loại mẫu tin nhắn',
    enum: WhatsAppTemplateCategory,
    example: WhatsAppTemplateCategory.UTILITY,
  })
  @IsNotEmpty()
  @IsEnum(WhatsAppTemplateCategory)
  category: WhatsAppTemplateCategory;

  @ApiProperty({
    description: 'Các thành phần của mẫu tin nhắn (header, body, footer, buttons)',
    example: {
      header: {
        type: 'text',
        text: 'Chào mừng đến với RedAI',
      },
      body: {
        type: 'text',
        text: 'Xin chào {{1}}, cảm ơn bạn đã liên hệ với chúng tôi.',
      },
      footer: {
        type: 'text',
        text: 'RedAI - Trợ lý AI thông minh',
      },
      buttons: [
        {
          type: 'url',
          text: 'Tìm hiểu thêm',
          url: 'https://redai.vn',
        },
      ],
    },
  })
  @IsNotEmpty()
  @IsObject()
  components: Record<string, any>;
}
