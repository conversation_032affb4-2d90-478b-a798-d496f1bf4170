import { Controller, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { FacebookPageAdminService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { JwtEmployeeGuard } from '@/modules/auth/guards';

@ApiTags(SWAGGER_API_TAGS.INTEGRATION_ADMIN)
@Controller('admin/integration/facebook-page')
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtEmployeeGuard)
export class FacebookPageAdminController {
  constructor(
    private readonly facebookPageAdminService: FacebookPageAdminService,
  ) {}
}
