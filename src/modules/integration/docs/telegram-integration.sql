-- Tạo bảng telegram_bots
CREATE TABLE telegram_bots (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    bot_name VARCHAR(255) NOT NULL,
    bot_username VARCHAR(255) NOT NULL,
    bot_token VARCHAR(255) NOT NULL,
    agent_id UUID REFERENCES agents(id),
    is_active BOOLEAN DEFAULT TRUE,
    webhook_url VARCHAR(255),
    created_at BIGINT DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT),
    updated_at BIGINT DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT)
);

COMMENT ON TABLE telegram_bots IS 'Lưu trữ thông tin về các bot Telegram được tạo bởi người dùng';
COMMENT ON COLUMN telegram_bots.user_id IS 'ID người dùng sở hữu bot';
COMMENT ON COLUMN telegram_bots.bot_name IS 'Tên bot do người dùng đặt';
COMMENT ON COLUMN telegram_bots.bot_username IS 'Username của bot trên Telegram (không bao gồm @)';
COMMENT ON COLUMN telegram_bots.bot_token IS 'Token API của bot từ BotFather';
COMMENT ON COLUMN telegram_bots.agent_id IS 'ID agent được kết nối với bot Telegram';
COMMENT ON COLUMN telegram_bots.is_active IS 'Trạng thái hoạt động của bot';
COMMENT ON COLUMN telegram_bots.webhook_url IS 'Webhook URL đã đăng ký với Telegram';
COMMENT ON COLUMN telegram_bots.created_at IS 'Thời điểm tạo (Unix timestamp)';
COMMENT ON COLUMN telegram_bots.updated_at IS 'Thời điểm cập nhật (Unix timestamp)';

-- Tạo bảng telegram_chats
CREATE TABLE telegram_chats (
    id SERIAL PRIMARY KEY,
    telegram_bot_id INTEGER NOT NULL REFERENCES telegram_bots(id),
    chat_id BIGINT NOT NULL,
    chat_type VARCHAR(20) NOT NULL,
    chat_title VARCHAR(255),
    username VARCHAR(255),
    full_name VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at BIGINT DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT),
    updated_at BIGINT DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT),
    UNIQUE(telegram_bot_id, chat_id)
);

COMMENT ON TABLE telegram_chats IS 'Lưu trữ thông tin về các cuộc trò chuyện Telegram mà bot tham gia';
COMMENT ON COLUMN telegram_chats.telegram_bot_id IS 'ID của bot Telegram';
COMMENT ON COLUMN telegram_chats.chat_id IS 'ID của cuộc trò chuyện trên Telegram';
COMMENT ON COLUMN telegram_chats.chat_type IS 'Loại cuộc trò chuyện (private, group, supergroup, channel)';
COMMENT ON COLUMN telegram_chats.chat_title IS 'Tên của cuộc trò chuyện';
COMMENT ON COLUMN telegram_chats.username IS 'Username của người dùng hoặc nhóm (nếu có)';
COMMENT ON COLUMN telegram_chats.full_name IS 'Tên đầy đủ của người dùng (nếu là chat private)';
COMMENT ON COLUMN telegram_chats.is_active IS 'Trạng thái hoạt động của cuộc trò chuyện';
COMMENT ON COLUMN telegram_chats.created_at IS 'Thời điểm tạo (Unix timestamp)';
COMMENT ON COLUMN telegram_chats.updated_at IS 'Thời điểm cập nhật (Unix timestamp)';

-- Tạo bảng telegram_messages
CREATE TABLE telegram_messages (
    id SERIAL PRIMARY KEY,
    telegram_chat_id INTEGER NOT NULL REFERENCES telegram_chats(id),
    message_id BIGINT NOT NULL,
    sender_id BIGINT,
    sender_name VARCHAR(255),
    text TEXT,
    message_type VARCHAR(20) DEFAULT 'text',
    metadata JSONB DEFAULT '{}',
    direction VARCHAR(10) NOT NULL,
    sent_at BIGINT NOT NULL,
    UNIQUE(telegram_chat_id, message_id)
);

COMMENT ON TABLE telegram_messages IS 'Lưu trữ lịch sử tin nhắn trao đổi qua Telegram';
COMMENT ON COLUMN telegram_messages.telegram_chat_id IS 'ID của cuộc trò chuyện Telegram';
COMMENT ON COLUMN telegram_messages.message_id IS 'ID tin nhắn trên Telegram';
COMMENT ON COLUMN telegram_messages.sender_id IS 'ID người dùng gửi tin nhắn trên Telegram';
COMMENT ON COLUMN telegram_messages.sender_name IS 'Tên người gửi';
COMMENT ON COLUMN telegram_messages.text IS 'Nội dung tin nhắn';
COMMENT ON COLUMN telegram_messages.message_type IS 'Loại tin nhắn (text, photo, video, document, etc.)';
COMMENT ON COLUMN telegram_messages.metadata IS 'Metadata của tin nhắn (JSON)';
COMMENT ON COLUMN telegram_messages.direction IS 'Hướng tin nhắn (incoming/outgoing)';
COMMENT ON COLUMN telegram_messages.sent_at IS 'Thời điểm gửi tin nhắn (Unix timestamp)';

-- Tạo bảng telegram_webhook_logs
CREATE TABLE telegram_webhook_logs (
    id SERIAL PRIMARY KEY,
    telegram_bot_id INTEGER REFERENCES telegram_bots(id),
    payload JSONB NOT NULL,
    status VARCHAR(20) NOT NULL,
    error_message TEXT,
    created_at BIGINT DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT)
);

COMMENT ON TABLE telegram_webhook_logs IS 'Lưu trữ log các webhook từ Telegram để debug và theo dõi';
COMMENT ON COLUMN telegram_webhook_logs.telegram_bot_id IS 'ID của bot Telegram';
COMMENT ON COLUMN telegram_webhook_logs.payload IS 'Dữ liệu webhook nhận được (JSON)';
COMMENT ON COLUMN telegram_webhook_logs.status IS 'Trạng thái xử lý (success/error)';
COMMENT ON COLUMN telegram_webhook_logs.error_message IS 'Thông báo lỗi (nếu có)';
COMMENT ON COLUMN telegram_webhook_logs.created_at IS 'Thời điểm nhận webhook (Unix timestamp)';

-- Tạo bảng telegram_commands
CREATE TABLE telegram_commands (
    id SERIAL PRIMARY KEY,
    telegram_bot_id INTEGER NOT NULL REFERENCES telegram_bots(id),
    command VARCHAR(50) NOT NULL,
    description VARCHAR(255) NOT NULL,
    response TEXT,
    action JSONB,
    "order" INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at BIGINT DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT),
    updated_at BIGINT DEFAULT (EXTRACT(EPOCH FROM NOW())::BIGINT),
    UNIQUE(telegram_bot_id, command)
);

COMMENT ON TABLE telegram_commands IS 'Lưu trữ các lệnh tùy chỉnh cho bot Telegram';
COMMENT ON COLUMN telegram_commands.telegram_bot_id IS 'ID của bot Telegram';
COMMENT ON COLUMN telegram_commands.command IS 'Tên lệnh (không bao gồm /)';
COMMENT ON COLUMN telegram_commands.description IS 'Mô tả lệnh';
COMMENT ON COLUMN telegram_commands.response IS 'Phản hồi khi lệnh được gọi';
COMMENT ON COLUMN telegram_commands.action IS 'Hành động tùy chỉnh (JSON)';
COMMENT ON COLUMN telegram_commands.order IS 'Thứ tự hiển thị';
COMMENT ON COLUMN telegram_commands.is_active IS 'Trạng thái hoạt động';
COMMENT ON COLUMN telegram_commands.created_at IS 'Thời điểm tạo (Unix timestamp)';
COMMENT ON COLUMN telegram_commands.updated_at IS 'Thời điểm cập nhật (Unix timestamp)';

-- Tạo các chỉ mục để tối ưu hiệu suất truy vấn
CREATE INDEX idx_telegram_bots_user_id ON telegram_bots(user_id);
CREATE INDEX idx_telegram_bots_agent_id ON telegram_bots(agent_id);
CREATE INDEX idx_telegram_chats_bot_id ON telegram_chats(telegram_bot_id);
CREATE INDEX idx_telegram_chats_chat_id ON telegram_chats(chat_id);
CREATE INDEX idx_telegram_messages_chat_id ON telegram_messages(telegram_chat_id);
CREATE INDEX idx_telegram_messages_sent_at ON telegram_messages(sent_at);
CREATE INDEX idx_telegram_webhook_logs_bot_id ON telegram_webhook_logs(telegram_bot_id);
CREATE INDEX idx_telegram_webhook_logs_created_at ON telegram_webhook_logs(created_at);
CREATE INDEX idx_telegram_commands_bot_id ON telegram_commands(telegram_bot_id);
