import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng ai_provider_models trong cơ sở dữ liệu
 * Lưu thông tin về các mô hình AI của từng nhà cung cấp
 */
@Entity('ai_provider_models')
export class AiProviderModel {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID nhà cung cấp
   */
  @Column({ name: 'provider_id' })
  providerId: number;

  /**
   * ID kỹ thuật của model (ví dụ: gpt-4o)
   */
  @Column({ name: 'model_id', type: 'text' })
  modelId: string;

  /**
   * Tên hiển thị của model
   */
  @Column({ name: 'display_name', type: 'text' })
  displayName: string;

  /**
   * Loại model: chat, vision, embedding, etc.
   */
  @Column({ name: 'type', type: 'text' })
  type: string;

  /**
   * Mảng các khả năng hỗ trợ: image, text, etc.
   */
  @Column({ name: 'capabilities', type: 'text', array: true, default: '{}' })
  capabilities: string[];

  /**
   * Dung lượng context window (tokens)
   */
  @Column({ name: 'context_window', type: 'integer', nullable: true })
  contextWindow: number;

  /**
   * Số tokens tối đa có thể sinh ra
   */
  @Column({ name: 'max_tokens', type: 'integer', nullable: true })
  maxTokens: number;

  /**
   * Thời điểm cắt dữ liệu huấn luyện
   */
  @Column({ name: 'training_cutoff', type: 'text', nullable: true })
  trainingCutoff: string;

  /**
   * Giá / 1000 tokens đầu vào
   */
  @Column({ name: 'price_input', type: 'decimal', nullable: true })
  priceInput: number;

  /**
   * Giá / 1000 tokens đầu ra
   */
  @Column({ name: 'price_output', type: 'decimal', nullable: true })
  priceOutput: number;

  /**
   * Trạng thái: active, deprecated, etc.
   */
  @Column({ name: 'status', type: 'text', default: 'active' })
  status: string;

  /**
   * Thời điểm tạo (epoch time)
   */
  @Column({ name: 'created_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  createdAt: number;

  /**
   * Thời điểm cập nhật (epoch time)
   */
  @Column({ name: 'updated_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  updatedAt: number;
}
