import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { AdminEmailServerConfigurationEntity } from '../entities/admin_email_server_configurations.entity';

@Injectable()
export class AdminEmailServerConfigurationRepository extends Repository<AdminEmailServerConfigurationEntity> {
  constructor(private dataSource: DataSource) {
    super(AdminEmailServerConfigurationEntity, dataSource.createEntityManager());
  }
}
