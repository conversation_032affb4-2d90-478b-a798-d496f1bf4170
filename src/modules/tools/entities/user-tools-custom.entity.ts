import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { HttpMethodEnum, ToolStatusEnum } from '../constants';

/**
 * Entity đại diện cho bảng user_tools_custom trong cơ sở dữ liệu
 * Bảng lưu trữ các công cụ tùy chỉnh và phiên bản do người dùng tạo hoặc chỉnh sửa
 */
@Entity('user_tools_custom')
export class UserToolsCustom {
  /**
   * Mã định danh duy nhất của công cụ tùy chỉnh, sử dụng UUID
   */
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  /**
   * Mã người dùng tạo hoặc chỉnh sửa, tham chiếu đến users
   */
  @Column({ name: 'user_id', type: 'integer' })
  userId: number;

  /**
   * Thời điểm tạo, tính bằng mili giây
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật gần nhất, tính bằng mili giây
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;

  /**
   * Trạng thái của công cụ (APPROVED, DRAFT, DEPRECATED)
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: ToolStatusEnum,
    default: ToolStatusEnum.APPROVED,
  })
  status: ToolStatusEnum;

  /**
   * Tên duy nhất của công cụ tùy chỉnh
   */
  @Column({ name: 'tool_name', length: 64 })
  toolName: string;

  /**
   * Mô tả chi tiết về chức năng của công cụ
   */
  @Column({ name: 'tool_description', type: 'text', nullable: true })
  toolDescription: string | null;

  /**
   * Đường dẫn endpoint của công cụ (ví dụ: /users)
   */
  @Column({ name: 'endpoint', length: 255 })
  endpoint: string;

  /**
   * Phương thức HTTP (GET, POST, PUT, DELETE, PATCH)
   */
  @Column({
    name: 'method',
    length: 10,
    type: 'varchar',
    enum: HttpMethodEnum,
  })
  method: HttpMethodEnum;

  /**
   * Đối tượng JSONB lưu trữ schema phản hồi theo chuẩn Swagger
   */
  @Column({ name: 'response', type: 'jsonb', default: '{}', nullable: true })
  response: any;

  /**
   * ID của cấu hình OAuth
   */
  @Column({ name: 'oauth_id', type: 'uuid', nullable: true })
  oauthId: string | null;

  /**
   * ID của cấu hình API Key
   */
  @Column({ name: 'api_key_id', type: 'uuid', nullable: true })
  apiKeyId: string | null;

  /**
   * URL cơ sở của API
   */
  @Column({ name: 'base_url', length: 255 })
  baseUrl: string;

  /**
   * Đối tượng JSONB lưu trữ thông tin về tham số truy vấn
   */
  @Column({ name: 'query_param', type: 'jsonb' })
  queryParam: Record<string, unknown>;

  /**
   * Đối tượng JSONB lưu trữ thông tin về tham số đường dẫn
   */
  @Column({ name: 'path_param', type: 'jsonb' })
  pathParam: Record<string, unknown>;


  /**
   * Đối tượng JSONB lưu trữ thông tin về tham số body
   */
  @Column({ name: 'body', type: 'jsonb' })
  body: Record<string, unknown>;

    /**
   * Trạng thái hoạt động của tool
   */
  @Column({ name: 'active', type: 'boolean', default: true })
  active: boolean;
}
