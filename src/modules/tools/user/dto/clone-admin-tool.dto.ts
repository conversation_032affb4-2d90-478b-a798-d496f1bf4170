import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

/**
 * DTO cho việc sao chép tool từ admin
 */
export class CloneAdminToolDto {
  @ApiProperty({
    description: 'ID của tool admin cần sao chép',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsString()
  @IsUUID()
  @IsNotEmpty()
  adminToolId: string;

  @ApiProperty({
    description: 'Tên tùy chỉnh cho tool (nếu muốn đổi tên)',
    example: 'Công cụ tìm kiếm của tôi',
    required: false,
  })
  @IsString()
  @IsOptional()
  customName?: string;

  @ApiProperty({
    description: 'Mô tả tùy chỉnh cho tool (nếu muốn đổi mô tả)',
    example: 'Phiên bản tùy chỉnh của công cụ tìm kiếm',
    required: false,
  })
  @IsString()
  @IsOptional()
  customDescription?: string;

  @ApiProperty({
    description: '<PERSON><PERSON> tả cho phiên bản đầu tiên khi sao chép',
    example: 'Sao chép từ tool của admin',
    required: false,
  })
  @IsString()
  @IsOptional()
  changeDescription?: string;
}
