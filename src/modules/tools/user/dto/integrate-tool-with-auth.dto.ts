import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsObject, IsOptional, ValidateNested } from 'class-validator';
import { IntegrateOpenApiDto } from './integrate-openapi.dto';
import { ApiKeyAuthDto, AuthConfigDto, AuthTypeEnum, NoAuthDto, OAuthAuthDto } from './auth-config.dto';

/**
 * DTO cho việc tích hợp công cụ từ OpenAPI với cấu hình xác thực
 */
export class IntegrateToolWithAuthDto extends IntegrateOpenApiDto {
  /**
   * Cấu hình xác thực
   */
  @ApiProperty({
    description: 'Cấu hình xác thực',
    oneOf: [
      { $ref: '#/components/schemas/ApiKeyAuthDto' },
      { $ref: '#/components/schemas/OAuthAuthDto' },
      { $ref: '#/components/schemas/NoAuthDto' }
    ],
    example: {
      authType: AuthTypeEnum.API_KEY,
      schemeName: 'ApiKeyAuth',
      apiKey: 'api_key_123456',
      apiKeyLocation: 'header',
      paramName: 'X-API-KEY'
    }
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => AuthConfigDto, {
    discriminator: {
      property: 'authType',
      subTypes: [
        { value: ApiKeyAuthDto, name: AuthTypeEnum.API_KEY },
        { value: OAuthAuthDto, name: AuthTypeEnum.OAUTH },
        { value: NoAuthDto, name: AuthTypeEnum.NONE }
      ]
    }
  })
  authConfig?: ApiKeyAuthDto | OAuthAuthDto | NoAuthDto;
}

/**
 * DTO cho kết quả tích hợp công cụ với xác thực
 */
export class IntegrateToolWithAuthResponseDto {
  /**
   * Số lượng công cụ đã tạo
   */
  @ApiProperty({
    description: 'Số lượng công cụ đã tạo',
    example: 5
  })
  toolsCreated: number;

  /**
   * Số lượng tài nguyên đã tạo
   */
  @ApiProperty({
    description: 'Số lượng tài nguyên đã tạo',
    example: 3
  })
  resourcesCreated: number;

  /**
   * Thông tin về cấu hình xác thực đã tạo
   */
  @ApiProperty({
    description: 'Thông tin về cấu hình xác thực đã tạo',
    example: {
      apiKeyCreated: 1,
      oauthCreated: 0
    }
  })
  authConfig: {
    apiKeyCreated: number;
    oauthCreated: number;
  };
}
