import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho việc cập nhật user key LLM
 */
export class UpdateUserKeyLlmDto {
  /**
   * Tên định danh cho key
   */
  @ApiPropertyOptional({
    description: 'Tên định danh cho key',
    example: 'My OpenAI Key Updated',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  /**
   * API key cá nhân hóa (nếu muốn thay đổi)
   */
  @ApiPropertyOptional({
    description: 'API key cá nhân hóa (nếu muốn thay đổi)',
    example: 'sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
  })
  @IsOptional()
  @IsString()
  apiKey?: string;
}
