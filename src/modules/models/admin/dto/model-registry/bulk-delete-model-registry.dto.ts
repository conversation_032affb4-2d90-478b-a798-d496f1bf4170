import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsUUID, ArrayMinSize, ArrayMaxSize } from 'class-validator';

/**
 * DTO cho việc xóa nhiều model registry cùng lúc
 */
export class BulkDeleteModelRegistryDto {
  /**
   * Danh sách ID các model registry cần xóa
   */
  @ApiProperty({
    description: 'Danh sách ID các model registry cần xóa',
    type: [String],
    example: [
      '123e4567-e89b-12d3-a456-************',
      '123e4567-e89b-12d3-a456-************',
      '123e4567-e89b-12d3-a456-************'
    ],
    minItems: 1,
    maxItems: 50,
  })
  @IsArray()
  @IsNotEmpty()
  @ArrayMinSize(1, { message: '<PERSON><PERSON>i có ít nhất 1 ID để xóa' })
  @ArrayMaxSize(50, { message: 'Không thể xóa quá 50 model registry cùng lúc' })
  @IsUUID('4', { each: true, message: 'Mỗi ID phải là UUID hợp lệ' })
  ids: string[];
}

/**
 * DTO cho response của bulk delete operation
 */
export class BulkDeleteResponseDto {
  /**
   * Danh sách ID đã xóa thành công
   */
  @ApiProperty({
    description: 'Danh sách ID đã xóa thành công',
    type: [String],
    example: [
      '123e4567-e89b-12d3-a456-************',
      '123e4567-e89b-12d3-a456-************'
    ],
  })
  successIds: string[];

  /**
   * Danh sách ID xóa thất bại
   */
  @ApiProperty({
    description: 'Danh sách ID xóa thất bại',
    type: [String],
    example: [
      '123e4567-e89b-12d3-a456-************'
    ],
  })
  failedIds: string[];

  /**
   * Tổng số bản ghi đã xóa
   */
  @ApiProperty({
    description: 'Tổng số bản ghi đã xóa thành công',
    example: 2,
  })
  deletedCount: number;

  /**
   * Tổng số bản ghi xóa thất bại
   */
  @ApiProperty({
    description: 'Tổng số bản ghi xóa thất bại',
    example: 1,
  })
  failedCount: number;
}
