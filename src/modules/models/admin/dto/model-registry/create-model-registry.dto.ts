import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { 
  IsArray, 
  IsEnum, 
  IsNotEmpty, 
  IsNumber, 
  IsObject, 
  IsOptional, 
  IsString, 
  MaxLength, 
  Min, 
  ValidateNested 
} from 'class-validator';
import { ProviderEnum } from '../../../constants';
import { 
  InputModalityEnum, 
  OutputModalityEnum, 
  SamplingParameterEnum, 
  FeatureEnum 
} from '../../../constants/model-capabilities.enum';
import { ModelPricingInterface } from '../../../interfaces/pricing.interface';

/**
 * DTO cho pricing của model
 */
export class ModelPricingDto implements ModelPricingInterface {
  /**
   * Giá cho input (per token hoặc per request)
   */
  @ApiProperty({
    description: 'Giá cho input (per token hoặc per request)',
    example: 1,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  inputRate: number;

  /**
   * Giá cho output (per token hoặc per request)
   */
  @ApiProperty({
    description: 'Giá cho output (per token hoặc per request)',
    example: 2,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  outputRate: number;
}

/**
 * DTO cho việc tạo mới model registry
 */
export class CreateModelRegistryDto {
  /**
   * Nhà cung cấp model
   */
  @ApiPropertyOptional({
    description: 'Nhà cung cấp model',
    enum: ProviderEnum,
    example: ProviderEnum.OPENAI,
    default: ProviderEnum.OPENAI,
  })
  @IsOptional()
  @IsEnum(ProviderEnum)
  provider?: ProviderEnum = ProviderEnum.OPENAI;

  /**
   * Tên mẫu đại diện của model
   */
  @ApiProperty({
    description: 'Tên mẫu đại diện của model (phải unique)',
    example: 'gpt-4*',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  modelNamePattern: string;

  /**
   * Các loại dữ liệu đầu vào hỗ trợ (text, image, audio,...)
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu vào hỗ trợ',
    enum: InputModalityEnum,
    isArray: true,
    example: [InputModalityEnum.TEXT, InputModalityEnum.IMAGE],
    default: [InputModalityEnum.TEXT],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(InputModalityEnum, { each: true })
  inputModalities?: InputModalityEnum[] = [InputModalityEnum.TEXT];

  /**
   * Các loại dữ liệu đầu ra hỗ trợ
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu ra hỗ trợ',
    enum: OutputModalityEnum,
    isArray: true,
    example: [OutputModalityEnum.TEXT],
    default: [OutputModalityEnum.TEXT],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(OutputModalityEnum, { each: true })
  outputModalities?: OutputModalityEnum[] = [OutputModalityEnum.TEXT];

  /**
   * Các tham số sampling như temperature, top_p,...
   */
  @ApiPropertyOptional({
    description: 'Các tham số sampling hỗ trợ',
    enum: SamplingParameterEnum,
    isArray: true,
    example: [SamplingParameterEnum.TEMPERATURE, SamplingParameterEnum.TOP_P],
    default: [SamplingParameterEnum.TEMPERATURE],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(SamplingParameterEnum, { each: true })
  samplingParameters?: SamplingParameterEnum[] = [SamplingParameterEnum.TEMPERATURE];

  /**
   * Tập hợp feature đặc biệt (như tool-use, function-calling)
   */
  @ApiPropertyOptional({
    description: 'Các feature đặc biệt hỗ trợ',
    enum: FeatureEnum,
    isArray: true,
    example: [FeatureEnum.TOOL_CALL],
    default: [],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(FeatureEnum, { each: true })
  features?: FeatureEnum[] = [];

  /**
   * Giá cơ bản cho model (input/output rate)
   */
  @ApiPropertyOptional({
    description: 'Giá cơ bản cho model',
    type: ModelPricingDto,
    example: { inputRate: 1, outputRate: 2 },
    default: { inputRate: 1, outputRate: 1 },
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ModelPricingDto)
  basePricing?: ModelPricingDto = { inputRate: 1, outputRate: 1 };

  /**
   * Giá fine-tune cho model (input/output rate)
   */
  @ApiPropertyOptional({
    description: 'Giá fine-tune cho model',
    type: ModelPricingDto,
    example: { inputRate: 2, outputRate: 4 },
    default: { inputRate: 1, outputRate: 1 },
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ModelPricingDto)
  fineTunePricing?: ModelPricingDto = { inputRate: 1, outputRate: 1 };

  /**
   * Giá training cho model
   */
  @ApiPropertyOptional({
    description: 'Giá training cho model',
    example: 0,
    minimum: 0,
    default: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  trainingPricing?: number = 0;
}
