import { ApiProperty } from '@nestjs/swagger';
import { ModelDiscoveryInfo } from '../../../interfaces/model-discovery-info.interface';

/**
 * DTO cho response khi cập nhật system key LLM
 */
export class UpdateSystemKeyLlmResponseDto {
  @ApiProperty({
    description: 'ID của system key LLM được cập nhật',
    example: 'uuid-string'
  })
  id: string;

  @ApiProperty({
    description: 'Lỗi connection test nếu có',
    example: 'API key không hợp lệ',
    required: false
  })
  connectionError?: string;

  @ApiProperty({
    description: 'Thông tin model discovery (chỉ có khi cập nhật API key)',
    required: false,
    example: {
      totalModelsFound: 20,
      modelsMatched: 18,
      newModelsCreated: 5,
      existingModelsFound: 13,
      mappingsCreated: 18,
      discoveryTime: 1640995200000,
      success: true,
      message: 'Discovery thành công: 5 models mới, 13 models đã tồn tại',
      errors: []
    }
  })
  modelDiscovery?: ModelDiscoveryInfo;
}
