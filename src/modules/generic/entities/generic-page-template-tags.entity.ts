import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Primary<PERSON><PERSON>umn, ManyToOne, <PERSON>in<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { GenericPageTemplate } from './generic-page-templates.entity';

@Entity('generic_page_template_tags')
export class GenericPageTemplateTag {
  @PrimaryColumn({ type: 'varchar', length: 36 })
  template_id: string;

  @PrimaryColumn({ type: 'varchar', length: 50 })
  tag: string;
}