import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { GenericPageTemplate } from '../entities/generic-page-template.entity';
import { PaginatedResult } from '@/common/response';
import { AppException } from '@/common';
import { GENERIC_PAGE_ERROR_CODES } from '../exceptions/generic-page-error.code';

@Injectable()
export class GenericPageTemplateRepository extends Repository<GenericPageTemplate> {
  private readonly logger = new Logger(GenericPageTemplateRepository.name);

  constructor(private dataSource: DataSource) {
    super(GenericPageTemplate, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho GenericPageTemplate
   * @returns SelectQueryBuilder<GenericPageTemplate>
   */
  private createBaseQuery(): SelectQueryBuilder<GenericPageTemplate> {
    return this.createQueryBuilder('template');
  }

  /**
   * Tìm mẫu trang theo ID
   * @param id ID của mẫu trang
   * @returns Mẫu trang nếu tìm thấy
   * @throws AppException nếu không tìm thấy mẫu trang
   */
  async findById(id: string): Promise<GenericPageTemplate> {
    try {
      const template = await this.createBaseQuery()
        .where('template.id = :id', { id })
        .getOne();

      if (!template) {
        throw new AppException(
          GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_NOT_FOUND,
          `Không tìm thấy mẫu trang với ID ${id}`,
        );
      }

      return template;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error finding template by ID: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_NOT_FOUND,
        `Lỗi khi tìm mẫu trang với ID ${id}`,
      );
    }
  }

  /**
   * Tìm mẫu trang theo danh mục
   * @param category Danh mục của mẫu trang
   * @returns Danh sách mẫu trang thuộc danh mục
   */
  async findByCategory(category: string): Promise<GenericPageTemplate[]> {
    try {
      return await this.createBaseQuery()
        .where('template.category = :category', { category })
        .getMany();
    } catch (error) {
      this.logger.error(`Error finding templates by category: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_NOT_FOUND,
        `Lỗi khi tìm mẫu trang theo danh mục ${category}`,
      );
    }
  }

  /**
   * Tìm mẫu trang theo tag
   * @param tag Tag của mẫu trang
   * @returns Danh sách mẫu trang có tag
   */
  async findByTag(tag: string): Promise<GenericPageTemplate[]> {
    try {
      return await this.createBaseQuery()
        .innerJoin('generic_page_template_tags', 'tags', 'tags.template_id = template.id')
        .where('tags.tag = :tag', { tag })
        .getMany();
    } catch (error) {
      this.logger.error(`Error finding templates by tag: ${error.message}`, error.stack);
      throw new AppException(
        GENERIC_PAGE_ERROR_CODES.GENERIC_PAGE_TEMPLATE_NOT_FOUND,
        `Lỗi khi tìm mẫu trang theo tag ${tag}`,
      );
    }
  }
}
