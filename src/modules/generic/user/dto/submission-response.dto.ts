import { ApiProperty } from '@nestjs/swagger';
import { GenericPageSubmissionStatusEnum } from '../../constants/generic-page.enum';

/**
 * DTO cho phản hồi khi gửi form thành công
 */
export class SubmissionResponseDto {
  /**
   * ID của dữ liệu gửi
   * @example "c7d8e9f0-a1b2-4c3d-9e0f-1a2b3c4d5e6f"
   */
  @ApiProperty({
    description: 'ID của dữ liệu gửi',
    example: 'c7d8e9f0-a1b2-4c3d-9e0f-1a2b3c4d5e6f',
  })
  id: string;

  /**
   * ID của trang
   * @example "f47ac10b-58cc-4372-a567-0e02b2c3d479"
   */
  @ApiProperty({
    description: 'ID của trang',
    example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
  })
  pageId: string;

  /**
   * Trạng thái của dữ liệu gửi
   * @example "pending"
   */
  @ApiProperty({
    description: 'Trạng thái của dữ liệu gửi',
    enum: GenericPageSubmissionStatusEnum,
    example: GenericPageSubmissionStatusEnum.PENDING,
  })
  status: GenericPageSubmissionStatusEnum;

  /**
   * Thời điểm gửi dữ liệu
   * @example 1678439700000
   */
  @ApiProperty({
    description: 'Thời điểm gửi dữ liệu (Unix timestamp)',
    example: 1678439700000,
  })
  createdAt: number;

  /**
   * Thông báo
   * @example "Dữ liệu đã được gửi thành công"
   */
  @ApiProperty({
    description: 'Thông báo',
    example: 'Dữ liệu đã được gửi thành công',
  })
  message: string;
}
