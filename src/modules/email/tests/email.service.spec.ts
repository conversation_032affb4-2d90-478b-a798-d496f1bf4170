import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { EmailService } from '../services/email.service';
import { SendEmailDto } from '../interface/send-email.dto';
import { AppException } from '@/common';
import * as nodemailer from 'nodemailer';

// Mock nodemailer
jest.mock('nodemailer');
const mockNodemailer = nodemailer as jest.Mocked<typeof nodemailer>;

describe('EmailService', () => {
  let service: EmailService;
  let configService: jest.Mocked<ConfigService>;
  let mockTransporter: jest.Mocked<nodemailer.Transporter>;

  beforeEach(async () => {
    // Mock transporter
    mockTransporter = {
      sendMail: jest.fn(),
      verify: jest.fn(),
    } as any;

    // Mock nodemailer.createTransport
    mockNodemailer.createTransport.mockReturnValue(mockTransporter);

    // Mock ConfigService
    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<EmailService>(EmailService);
    configService = module.get(ConfigService);

    // Setup default config values
    configService.get.mockImplementation((key: string) => {
      switch (key) {
        case 'EMAIL_SMTP_HOST':
          return 'smtp.gmail.com';
        case 'EMAIL_SMTP_PORT':
          return 587;
        case 'EMAIL_SMTP_USER':
          return '<EMAIL>';
        case 'EMAIL_SMTP_PASS':
          return 'test-password';
        case 'EMAIL_FROM':
          return '<EMAIL>';
        default:
          return undefined;
      }
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create transporter with SMTP configuration', () => {
      expect(mockNodemailer.createTransport).toHaveBeenCalledWith({
        host: 'smtp.gmail.com',
        port: 587,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: 'test-password',
        },
      });
    });

    it('should throw error when SMTP credentials are missing', async () => {
      // Reset mocks
      jest.clearAllMocks();
      
      // Mock missing credentials
      configService.get.mockImplementation((key: string) => {
        switch (key) {
          case 'EMAIL_SMTP_HOST':
            return 'smtp.gmail.com';
          case 'EMAIL_SMTP_PORT':
            return 587;
          case 'EMAIL_SMTP_USER':
            return undefined; // Missing user
          case 'EMAIL_SMTP_PASS':
            return undefined; // Missing password
          default:
            return undefined;
        }
      });

      expect(() => {
        new EmailService(configService);
      }).toThrow(AppException);
    });
  });

  describe('sendEmail', () => {
    it('should send email successfully', async () => {
      // Arrange
      const sendEmailDto: SendEmailDto = {
        to: '<EMAIL>',
        subject: 'Test Subject',
        body: '<h1>Test Body</h1>',
      };

      const mockResult = {
        messageId: 'test-message-id',
        response: 'Email sent successfully',
        envelope: { from: '<EMAIL>', to: ['<EMAIL>'] },
      };

      mockTransporter.sendMail.mockResolvedValue(mockResult);

      // Act
      const result = await service.sendEmail(sendEmailDto);

      // Assert
      expect(mockTransporter.sendMail).toHaveBeenCalledWith({
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Test Subject',
        html: '<h1>Test Body</h1>',
      });

      expect(result).toEqual({
        success: true,
        messageId: 'test-message-id',
        response: 'Email sent successfully',
        envelope: { from: '<EMAIL>', to: ['<EMAIL>'] },
      });
    });

    it('should throw AppException when email sending fails', async () => {
      // Arrange
      const sendEmailDto: SendEmailDto = {
        to: '<EMAIL>',
        subject: 'Test Subject',
        body: '<h1>Test Body</h1>',
      };

      const error = new Error('SMTP connection failed');
      mockTransporter.sendMail.mockRejectedValue(error);

      // Act & Assert
      await expect(service.sendEmail(sendEmailDto)).rejects.toThrow(AppException);
      expect(mockTransporter.sendMail).toHaveBeenCalled();
    });
  });

  describe('verifyConnection', () => {
    it('should return true when connection is verified successfully', async () => {
      // Arrange
      mockTransporter.verify.mockResolvedValue(true);

      // Act
      const result = await service.verifyConnection();

      // Assert
      expect(result).toBe(true);
      expect(mockTransporter.verify).toHaveBeenCalled();
    });

    it('should return false when connection verification fails', async () => {
      // Arrange
      const error = new Error('Connection failed');
      mockTransporter.verify.mockRejectedValue(error);

      // Act
      const result = await service.verifyConnection();

      // Assert
      expect(result).toBe(false);
      expect(mockTransporter.verify).toHaveBeenCalled();
    });
  });
});
