import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng banks - <PERSON><PERSON> sách ngân hàng
 */
@Entity('banks')
export class Bank {
  /**
   * Mã code
   */
  @PrimaryColumn({ name: 'bank_code', length: 20 })
  bankCode: string;

  /**
   * Tên ngân hàng
   */
  @Column({ name: 'bank_name', length: 255, nullable: true })
  bankName: string;

  /**
   * path logo
   */
  @Column({ name: 'logo_path', length: 255, nullable: true })
  logoPath: string;

  /**
   * Tên đầy đủ của ngân hàng
   */
  @Column({ name: 'full_name', length: 255, nullable: true })
  fullName: string;

  /**
   * path icon
   */
  @Column({ name: 'icon_path', length: 255, nullable: true })
  iconPath: string;

  /**
   * Mã BIN của ngân hàng
   */
  @Column({ name: 'bin', length: 20, nullable: true })
  bin: string;
}
