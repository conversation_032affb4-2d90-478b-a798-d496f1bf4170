import { Column, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm';

/**
 * Entity đại diện cho bảng permissions
 */
@Entity('permissions')
@Unique('permissions_pk', ['action', 'module'])
export class Permission {
  /**
   * ID của quyền
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Hành động
   */
  @Column({ name: 'action', type: 'varchar', length: 255, nullable: true })
  action: string;

  /**
   * <PERSON><PERSON> tả quyền
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;

  /**
   * Thời gian tạo
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number;

  /**
   * Module
   */
  @Column({ name: 'module', type: 'varchar', length: 50, nullable: true })
  module: string;
}
