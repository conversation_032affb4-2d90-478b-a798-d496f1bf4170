import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { User } from './user.entity';

/**
 * Entity đại diện cho bảng user_rag_api_key trong cơ sở dữ liệu
 * <PERSON>ư<PERSON> trữ RAG API key của từng user (mỗi user chỉ có 1 API key)
 */
@Entity('user_rag_api_key')
export class UserRagApiKey {
  /**
   * ID của người dùng (Primary Key)
   */
  @PrimaryColumn({ name: 'user_id', type: 'integer' })
  userId: number;

  /**
   * RAG API key của user (Primary Key)
   */
  @PrimaryColumn({ name: 'rag_api_key', type: 'text' })
  ragApiKey: string;

  /**
   * Thời điểm tạo
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * <PERSON>uan hệ với User entity
   */
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;
}
