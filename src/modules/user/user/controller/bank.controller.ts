import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiExtraModels, ApiParam, getSchemaPath } from '@nestjs/swagger';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { BankService } from '../service/bank.service';
import { BankResponseDto, BankQueryDto } from '../dto/bank';
import { PaginatedResponseDto } from '../dto/common';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ApiResponseDto, PaginatedResult } from '@/common/response';

/**
 * Controller xử lý API liên quan đến ngân hàng
 */
@ApiTags(SWAGGER_API_TAGS.BANKS)
@ApiExtraModels(ApiResponseDto, PaginatedResult, BankResponseDto, PaginatedResponseDto)
@Controller('banks')
@UseGuards(JwtUserGuard)
@ApiBearerAuth("JWT-auth")
export class BankController {
  constructor(private readonly bankService: BankService) {}

  /**
   * Lấy danh sách ngân hàng với phân trang và filter
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách ngân hàng với phân trang và filter' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách ngân hàng với phân trang',
    schema: ApiResponseDto.getPaginatedSchema(BankResponseDto)
  })
  async findAll(@Query() query: BankQueryDto): Promise<ApiResponseDto<PaginatedResponseDto<BankResponseDto>>> {
    const result = await this.bankService.findAll(query);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy tất cả ngân hàng (không phân trang)
   */
  @Get('all')
  @ApiOperation({ summary: 'Lấy tất cả ngân hàng (không phân trang)' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tất cả ngân hàng',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            result: {
              type: 'array',
              items: { $ref: getSchemaPath(BankResponseDto) }
            }
          }
        }
      ]
    }
  })
  async findAllBanks(): Promise<ApiResponseDto<BankResponseDto[]>> {
    const result = await this.bankService.findAllBanks();
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy ngân hàng theo mã
   */
  @Get(':bankCode')
  @ApiOperation({ summary: 'Lấy ngân hàng theo mã' })
  @ApiResponse({ status: 200, description: 'Thông tin ngân hàng', type: BankResponseDto })
  @ApiResponse({ status: 404, description: 'Không tìm thấy ngân hàng' })
  async findByCode(@Param('bankCode') bankCode: string): Promise<ApiResponseDto<BankResponseDto | null>> {
    const result = await this.bankService.findByCode(bankCode);
    return ApiResponseDto.success(result);
  }
}
