import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UserRagApiKeyRepository } from '@modules/user/repositories';
import { AppException, ErrorCode } from '@/common/exceptions';

/**
 * Service xử lý việc tự động tạo RAG API key cho user mới
 * Được gọi khi user đăng ký tài khoản thành công
 */
@Injectable()
export class RagApiKeyProvisioningService {
  private readonly logger = new Logger(RagApiKeyProvisioningService.name);
  private readonly ragApiBaseUrl: string;
  private readonly ragApiAdminKey: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly userRagApiKeyRepository: UserRagApiKeyRepository,
  ) {
    this.ragApiBaseUrl = this.configService.get<string>('FAST_API_URL', 'http://localhost:8000');
    this.ragApiAdminKey = this.configService.get<string>('RAG_API_ADMIN_KEY', '');

    if (!this.ragApiAdminKey) {
      this.logger.warn('RAG_API_ADMIN_KEY is not configured. RAG API key provisioning will not work.');
    }
  }

  /**
   * Tự động tạo RAG API key cho user mới
   * @param userId ID của user vừa được tạo
   * @param userEmail Email của user (dùng để tạo tên key)
   * @returns Promise<void>
   */
  async provisionApiKeyForNewUser(userId: number, userEmail: string): Promise<void> {
    try {
      this.logger.log(`Bắt đầu tạo RAG API key cho user ${userId} (${userEmail})`);

      // Kiểm tra user đã có API key chưa
      const existingKey = await this.userRagApiKeyRepository.findByUserId(userId);
      if (existingKey) {
        this.logger.warn(`User ${userId} đã có RAG API key, bỏ qua việc tạo mới`);
        return;
      }

      // Tạo API key từ RAG API
      const ragApiKey = await this.createRagApiKey(userId, userEmail);

      // Lưu vào database
      await this.userRagApiKeyRepository.upsertUserApiKey(userId, ragApiKey);

      this.logger.log(`Tạo RAG API key thành công cho user ${userId}: ${ragApiKey.substring(0, 10)}...`);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo RAG API key cho user ${userId}: ${error.message}`, error.stack);
      
      // Không throw error để không ảnh hưởng đến quá trình đăng ký user
      // Chỉ log error và có thể retry sau
      this.logger.warn(`RAG API key provisioning failed for user ${userId}, but user registration will continue`);
    }
  }

  /**
   * Gọi RAG API để tạo API key mới với metadata
   * @param userId ID của user
   * @param userEmail Email của user
   * @returns API key được tạo
   */
  private async createRagApiKey(userId: number, userEmail: string): Promise<string> {
    try {
      const endpoint = '/api/auth/keys/with-metadata';
      const fullUrl = `${this.ragApiBaseUrl}${endpoint}`;

      const requestBody = {
        name: `Auto-generated for User ${userId} (${userEmail})`,
        expires_at: 0, // Không hết hạn
        metadata: {
          role: 'USER',
          user_id: userId
        }
      };

      this.logger.debug(`Gửi request tạo RAG API key: ${fullUrl}`);
      this.logger.debug(`Request body: ${JSON.stringify(requestBody)}`);

      const response = await fetch(fullUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.ragApiAdminKey,
          'Accept': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`RAG API returned ${response.status}: ${errorText}`);
      }

      const responseData = await response.json();
      
      // Validate response structure
      if (!responseData.key) {
        throw new Error('RAG API response missing key field');
      }

      // Validate metadata
      if (!responseData.api_metadata || responseData.api_metadata.user_id !== userId) {
        throw new Error('RAG API response metadata validation failed');
      }

      this.logger.debug(`RAG API key created successfully: ${responseData.key.substring(0, 10)}...`);
      return responseData.key;

    } catch (error) {
      this.logger.error(`Failed to create RAG API key: ${error.message}`);
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        `Không thể tạo RAG API key: ${error.message}`
      );
    }
  }

  /**
   * Retry tạo RAG API key cho user (dùng cho background job)
   * @param userId ID của user
   * @param userEmail Email của user
   * @param maxRetries Số lần retry tối đa
   * @returns Promise<boolean> - true nếu thành công
   */
  async retryProvisionApiKey(userId: number, userEmail: string, maxRetries: number = 3): Promise<boolean> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        this.logger.log(`Retry attempt ${attempt}/${maxRetries} for user ${userId}`);
        await this.provisionApiKeyForNewUser(userId, userEmail);
        return true;
      } catch (error) {
        this.logger.warn(`Retry attempt ${attempt} failed for user ${userId}: ${error.message}`);
        
        if (attempt === maxRetries) {
          this.logger.error(`All retry attempts failed for user ${userId}`);
          return false;
        }

        // Wait before next retry (exponential backoff)
        const waitTime = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
    return false;
  }

  /**
   * Kiểm tra xem user đã có RAG API key chưa
   * @param userId ID của user
   * @returns true nếu user đã có API key
   */
  async hasApiKey(userId: number): Promise<boolean> {
    try {
      const existingKey = await this.userRagApiKeyRepository.findByUserId(userId);
      return !!existingKey;
    } catch (error) {
      this.logger.error(`Error checking API key for user ${userId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Lấy thông tin cấu hình RAG API
   * @returns Thông tin cấu hình (để debug)
   */
  getConfiguration(): { baseUrl: string; hasAdminKey: boolean } {
    return {
      baseUrl: this.ragApiBaseUrl,
      hasAdminKey: !!this.ragApiAdminKey
    };
  }
}
