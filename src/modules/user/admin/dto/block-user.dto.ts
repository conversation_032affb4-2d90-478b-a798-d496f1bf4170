import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

/**
 * DTO cho việc khóa người dùng
 */
export class BlockUserDto {
  /**
   * Lý do khóa người dùng
   */
  @ApiProperty({
    description: 'Lý do khóa người dùng',
    example: 'Vi phạm điều khoản sử dụng',
    required: true
  })
  @IsNotEmpty()
  @IsString()
  reason: string;

  /**
   * Thông tin bổ sung (tùy chọn)
   */
  @ApiProperty({
    description: 'Thông tin bổ sung',
    example: { details: 'Spam nhiều lần', reportedBy: '<EMAIL>' },
    required: false
  })
  @IsOptional()
  info?: Record<string, any>;
}
