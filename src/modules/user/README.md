# Module User

## Tổng quan

Module User cung cấp các chức năng quản lý người dùng trong hệ thống. Module này quản lý thông tin cá nhân, thông tin doanh nghiệp, cài đặt thông báo và các thông tin liên quan đến người dùng.

## Cấu trúc module

```
user/
├── entities/               # Entities mapping với database
│   ├── user.entity.ts      # Entity chính của người dùng
│   ├── business-info.entity.ts # Thông tin doanh nghiệp
│   ├── user-manage-notification.entity.ts # Cài đặt thông báo
│   ├── two-factor-auth.entity.ts # Xác thực hai yếu tố
│   ├── auth-verification-log.entity.ts # Log xác thực
│   ├── user-role.entity.ts # Vai trò người dùng
│   └── device-info.entity.ts # Thông tin thiết bị
├── user.controller.ts      # Controller x<PERSON> lý request
├── user.service.ts         # Service xử lý logic nghiệp vụ
└── user.module.ts          # Module definition
```

## Các entity chính

1. **User**: Entity chính lưu trữ thông tin cơ bản của người dùng
2. **BusinessInfo**: Thông tin doanh nghiệp của người dùng
3. **UserManageNotification**: Cài đặt thông báo của người dùng
4. **TwoFactorAuth**: Cấu hình xác thực hai yếu tố
5. **AuthVerificationLog**: Log các hoạt động xác thực
6. **UserRole**: Vai trò của người dùng trong hệ thống
7. **DeviceInfo**: Thông tin thiết bị đăng nhập của người dùng

## Chức năng chính

### Quản lý thông tin người dùng
- Tạo, cập nhật, xóa thông tin người dùng
- Quản lý thông tin cá nhân
- Quản lý thông tin doanh nghiệp

### Quản lý cài đặt
- Cài đặt thông báo
- Cài đặt bảo mật
- Cài đặt xác thực hai yếu tố

### Quản lý vai trò
- Phân quyền người dùng
- Quản lý vai trò trong hệ thống

## API Endpoints

- `GET /user/profile` - Lấy thông tin cá nhân
- `PUT /user/profile` - Cập nhật thông tin cá nhân
- `GET /user/business-info` - Lấy thông tin doanh nghiệp
- `PUT /user/business-info` - Cập nhật thông tin doanh nghiệp
- `GET /user/notification-settings` - Lấy cài đặt thông báo
- `PUT /user/notification-settings` - Cập nhật cài đặt thông báo
- `GET /user/security` - Lấy cài đặt bảo mật
- `PUT /user/security` - Cập nhật cài đặt bảo mật
- `POST /user/enable-2fa` - Bật xác thực hai yếu tố
- `POST /user/disable-2fa` - Tắt xác thực hai yếu tố

## Cách sử dụng

### Lấy thông tin người dùng

```typescript
// Lấy thông tin người dùng theo ID
const user = await userService.findById(userId);
```

### Cập nhật thông tin người dùng

```typescript
// Cập nhật thông tin người dùng
const updatedUser = await userService.update(userId, {
  fullName: 'John Doe',
  phoneNumber: '0123456789',
  address: '123 Main St'
});
```

### Quản lý vai trò

```typescript
// Thêm vai trò cho người dùng
await userService.addRole(userId, 'admin');

// Kiểm tra vai trò của người dùng
const hasRole = await userService.hasRole(userId, 'admin');
```

## Liên kết với các module khác

- **Auth Module**: Xác thực và phân quyền
- **Email Module**: Gửi email thông báo
- **Subscription Module**: Quản lý gói dịch vụ của người dùng
