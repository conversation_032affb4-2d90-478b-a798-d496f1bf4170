import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { AffiliateCustomerQueryDto } from '@modules/affiliate/user/dto';
import { PaginatedResult } from '@/common/response';

@Injectable()
export class UserRepository {
  constructor(
    @InjectRepository(User)
    private readonly repository: Repository<User>
  ) {}

  /**
   * Tìm người dùng theo ID
   * @param id ID của người dùng
   * @returns Thông tin người dùng hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<User | null> {
    return this.repository.createQueryBuilder('user')
      .where('user.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm danh sách người dùng theo danh sách ID
   * @param ids Danh sách ID người dùng
   * @returns Danh sách người dùng
   */
  async findByIds(ids: number[]): Promise<User[]> {
    if (!ids.length) return [];

    // Sử dụng cú pháp SQL IN hợp lệ cho PostgreSQL
    return this.repository.createQueryBuilder('user')
      .where('user.id IN (:...ids)', { ids })
      .getMany();
  }

  /**
   * Đếm số khách hàng theo tài khoản affiliate và khoảng thời gian
   * @param affiliateAccountId ID tài khoản affiliate
   * @param begin Thời gian bắt đầu
   * @param end Thời gian kết thúc
   * @returns Số lượng khách hàng
   */
  async countByAffiliateAccountIdAndTimeRange(
    affiliateAccountId: number,
    begin: number,
    end: number
  ): Promise<number> {
    return this.repository
      .createQueryBuilder('user')
      .where('user.affiliateAccountId = :affiliateAccountId', { affiliateAccountId })
      .andWhere('user.createdAt >= :begin', { begin })
      .andWhere('user.createdAt <= :end', { end })
      .getCount();
  }

  /**
   * Cập nhật số dư điểm của người dùng
   * @param userId ID người dùng
   * @param amount Số điểm cần cập nhật (có thể âm để trừ điểm)
   * @returns Thông tin người dùng sau khi cập nhật
   */
  async updateUserBalance(userId: number, amount: number): Promise<User> {
    if (!userId) {
      throw new Error('ID người dùng không được cung cấp');
    }

    // Tìm người dùng
    const user = await this.findById(userId);
    if (!user) {
      throw new Error(`Không tìm thấy người dùng với ID ${userId}`);
    }

    // Chuyển đổi sang number để đảm bảo tính toán chính xác
    const currentBalance = Number(user.pointsBalance || 0);
    const amountToUpdate = Number(amount);

    // Tính toán số dư mới
    const newBalance = currentBalance + amountToUpdate;

    // Kiểm tra số dư không âm nếu là trừ điểm
    if (amountToUpdate < 0 && newBalance < 0) {
      throw new Error(`Số dư không đủ. Cần ${Math.abs(amountToUpdate)} R-Point, hiện có ${currentBalance} R-Point`);
    }

    // Cập nhật số dư
    await this.repository
      .createQueryBuilder()
      .update(User)
      .set({ pointsBalance: newBalance })
      .where('id = :userId', { userId })
      .execute();

    // Trả về thông tin người dùng đã cập nhật
    user.pointsBalance = newBalance;
    return user;
  }

  /**
   * Tăng số dư điểm cho người bán
   * @param sellerId ID người bán
   * @param amount Số điểm cần tăng
   * @returns Thông tin người bán sau khi cập nhật
   */
  async increaseSellerBalance(sellerId: number, amount: number): Promise<User> {
    if (!sellerId) {
      throw new Error('ID người bán không được cung cấp');
    }

    if (amount <= 0) {
      throw new Error('Số điểm cần tăng phải lớn hơn 0');
    }

    // Tìm người bán
    const seller = await this.findById(sellerId);
    if (!seller) {
      throw new Error(`Không tìm thấy người bán với ID ${sellerId}`);
    }

    // Chuyển đổi sang number để đảm bảo tính toán chính xác
    const currentBalance = Number(seller.pointsBalance || 0);
    const amountToAdd = Number(amount);

    // Tính toán số dư mới
    const newBalance = currentBalance + amountToAdd;

    // Cập nhật số dư
    await this.repository
      .createQueryBuilder()
      .update(User)
      .set({ pointsBalance: newBalance })
      .where('id = :sellerId', { sellerId })
      .execute();

    // Trả về thông tin người bán đã cập nhật
    seller.pointsBalance = newBalance;
    return seller;
  }

  /**
   * Tìm danh sách khách hàng affiliate với phân trang
   * @param affiliateAccountId ID tài khoản affiliate
   * @param queryDto Tham số truy vấn
   * @returns Danh sách khách hàng với phân trang
   */
  async findAffiliateCustomersWithPagination(
    affiliateAccountId: number,
    queryDto: AffiliateCustomerQueryDto
  ): Promise<PaginatedResult<User>> {
    const { page = 1, limit = 10, begin, end, search, sortBy = 'createdAt', sortDirection = 'DESC' } = queryDto;

    const skip = (page - 1) * limit;

    // Xây dựng query
    const queryBuilder = this.repository.createQueryBuilder('user')
      .where('user.affiliateAccountId = :affiliateAccountId', { affiliateAccountId });

    // Thêm điều kiện thời gian nếu có
    if (begin) {
      queryBuilder.andWhere('user.createdAt >= :begin', { begin });
    }

    if (end) {
      queryBuilder.andWhere('user.createdAt <= :end', { end });
    }

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      queryBuilder.andWhere(
        '(user.fullName LIKE :search OR user.email LIKE :search OR user.phoneNumber LIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Đếm tổng số bản ghi
    const totalItems = await queryBuilder.getCount();

    // Thêm sắp xếp và phân trang
    queryBuilder
      .orderBy(`user.${sortBy}`, sortDirection)
      .skip(skip)
      .take(limit);

    // Lấy dữ liệu
    const items = await queryBuilder.getMany();

    // Tính toán metadata
    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page
      }
    };
  }

  /**
   * Tìm kiếm người dùng theo tên hoặc email
   * @param search Từ khóa tìm kiếm
   * @returns Danh sách người dùng
   */
  async findByNameOrEmail(search: string): Promise<User[]> {
    return this.repository.createQueryBuilder('user')
      .where('user.fullName LIKE :search OR user.email LIKE :search', { search: `%${search}%` })
      .getMany();
  }
}
