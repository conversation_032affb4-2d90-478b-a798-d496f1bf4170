import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DeviceInfo } from '../entities/device-info.entity';
import { v4 as uuidv4 } from 'uuid';

/**
 * Repository cho DeviceInfo
 */
@Injectable()
export class DeviceInfoRepository {
  constructor(
    @InjectRepository(DeviceInfo)
    private readonly repository: Repository<DeviceInfo>,
  ) {}

  /**
   * Tạo thông tin thiết bị mới
   * @param data Dữ liệu thiết bị
   * @returns Thông tin thiết bị đã tạo
   */
  async create(data: Partial<DeviceInfo>): Promise<DeviceInfo> {
    // Tạo ID nếu không có
    if (!data.id) {
      data.id = uuidv4();
    }

    const deviceInfo = this.repository.create(data);
    return this.repository.save(deviceInfo);
  }

  /**
   * Tìm hoặc tạo thông tin thiết bị
   * @param fingerprint Fingerprint của thiết bị
   * @param userId ID của người dùng
   * @param ipAddress Địa chỉ IP
   * @param userAgent User agent
   * @param browserInfo Thông tin trình duyệt
   * @returns Thông tin thiết bị
   */
  async findOrCreate(
    fingerprint: string,
    userId: number,
    ipAddress?: string,
    userAgent?: string,
    browserInfo?: {
      browser?: string;
      os?: string;
    },
  ): Promise<DeviceInfo> {
    // Tìm thiết bị theo fingerprint và userId
    const existingDevice = await this.repository.findOne({
      where: {
        fingerprint,
        userId,
      },
    });

    if (existingDevice) {
      // Cập nhật thông tin thiết bị nếu đã tồn tại
      const now = Date.now();

      existingDevice.ipAddress = ipAddress || existingDevice.ipAddress;
      existingDevice.userAgent = userAgent || existingDevice.userAgent;
      existingDevice.browser = browserInfo?.browser || existingDevice.browser;
      // Sử dụng trường operatingSystem thay vì os
      existingDevice.operatingSystem = browserInfo?.os || existingDevice.operatingSystem;
      // Không có các trường này trong entity, bỏ qua
      // browserVersion, osVersion, device, deviceType, deviceVendor
      existingDevice.lastLogin = now;
      existingDevice.updatedAt = now;

      return this.repository.save(existingDevice);
    }

    // Tạo thiết bị mới nếu chưa tồn tại
    const now = Date.now();
    return this.create({
      fingerprint,
      userId,
      ipAddress,
      userAgent,
      browser: browserInfo?.browser,
      operatingSystem: browserInfo?.os,
      isTrusted: false, // Mặc định thiết bị chưa đáng tin cậy
      lastLogin: now,
      createdAt: now,
      updatedAt: now,
    });
  }

  /**
   * Tìm thông tin thiết bị theo ID
   * @param id ID của thiết bị
   * @returns Thông tin thiết bị
   */
  async findById(id: string): Promise<DeviceInfo | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm thông tin thiết bị theo ID người dùng
   * @param userId ID của người dùng
   * @returns Danh sách thông tin thiết bị
   */
  async findByUserId(userId: number): Promise<DeviceInfo[]> {
    return this.repository.find({
      where: { userId },
      order: { lastLogin: 'DESC' },
    });
  }
}
