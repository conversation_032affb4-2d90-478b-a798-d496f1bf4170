import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

/**
 * DTO cho yêu cầu làm mới token
 */
export class RefreshTokenDto {
  @ApiProperty({
    description: 'Refresh token (tùy chọn, nếu không cung cấp sẽ lấy từ cookie)',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    required: false,
  })
  @IsString({ message: 'Refresh token phải là chuỗi' })
  @IsOptional()
  refreshToken?: string;
}

/**
 * DTO cho phản hồi làm mới token
 */
export class RefreshTokenResponseDto {
  @ApiProperty({
    description: 'Access token mới',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Thời gian hết hạn của token (giây)',
    example: 86400,
    required: false,
  })
  expiresIn?: number;

  @ApiProperty({
    description: 'Thời điểm hết hạn của token (timestamp)',
    example: 1746968772000,
  })
  expiresAt: number;
}
