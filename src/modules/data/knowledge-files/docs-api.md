# Tài liệu API

## Thông tin chung

### Base URL
Tất cả các endpoint đều sử dụng base URL: `/api`

**Lưu ý:** Một số endpoint có thể sử dụng `/user` thay vì `/api`. Trong tài liệu này, chúng tôi giả định `/api` là base path chính để đảm bảo tính nhất quán, trừ khi được chỉ định khác.

### Định dạng phản hồi chuẩn
Mọi phản hồi bao gồm:

- `code`: Mã trạng thái HTTP
- `message`: Thông điệp mô tả kết quả
- `data`: (tùy chọn) Dữ liệu trả về từ API

### Mã lỗi thường gặp
- **400**: Yêu cầu không hợp lệ
- **403**: <PERSON>h<PERSON><PERSON> có quyền
- **404**: <PERSON><PERSON><PERSON><PERSON> tìm thấy
- **500**: Lỗi máy chủ nội bộ

---

## Các endpoint API

### 1. Tạo Vector Store

**Endpoint:** `/vector-stores`
**Method:** `POST`
**Mô tả:** API này được sử dụng để tạo một vector store mới phục vụ cho AI/RAG.

#### Request Body
```json
{
  "name": "string" // Tên vector store
}
```

#### Ví dụ Request
```json
{
  "name": "VectorStoreAI"
}
```

#### Response
- **Thành công:**
  - Code: `201`
  - Body:
    ```json
    {
      "code": 201,
      "message": "Tạo vector store thành công."
    }
    ```

- **Thất bại:**
  - Code: `400`
    - Body:
      ```json
      {
        "code": 400,
        "message": "Thông tin đầu vào không hợp lệ."
      }
      ```
  - Code: `500`
    - Body:
      ```json
      {
        "code": 500,
        "message": "Lỗi máy chủ, vui lòng thử lại sau."
      }
      ```

**Lưu ý:** Mã trạng thái thành công được đổi thành `201` (Created) thay vì `200` để phù hợp với chuẩn RESTful cho việc tạo tài nguyên.

---

### 2. Xem Danh Sách Vector Stores

**Endpoint:** `/vector-stores`
**Method:** `GET`
**Mô tả:** API này được sử dụng để lấy danh sách tất cả các vector store của một chủ sở hữu cụ thể.

#### Query Parameters
- `search`: string (tùy chọn) - Tên vector store cần tìm kiếm
- `page`: number (tùy chọn) - Số trang, mặc định là `1`
- `limit`: number (tùy chọn) - Số lượng kết quả trên một trang, mặc định là `10`
- `sortBy`: string (tùy chọn) - Trường để sắp xếp, mặc định là `"createdAt"`
- `sortDirection`: string (tùy chọn) - Hướng sắp xếp, mặc định là `"desc"`

#### Ví dụ Request
```
GET /vector-stores?page=1&limit=20&search=VectorStore&sortBy=name&sortDirection=asc
```

#### Response
- **Thành công:**
  - Code: `200`
  - Body:
    ```json
    {
      "code": 200,
      "data": {
        "items": [
          {
            "storeId": 1,
            "storeName": "VectorStoreAI",
            "size": 1024,
            "agents": 2,
            "files": 3,
            "createdAt": 1234567890,
            "updatedAt": 1234567890
          },
          {
            "storeId": 2,
            "storeName": "ProductCatalog",
            "size": 5120,
            "agents": 2,
            "files": 3,
            "createdAt": 1234567890,
            "updatedAt": 1234567890
          }
        ],
        "meta": {
          "totalItems": 15,
          "currentPage": 1,
          "itemsPerPage": 20,
          "totalPages": 1
        }
      },
      "message": "Lấy danh sách vector store thành công."
    }
    ```

- **Thất bại:**
  - Code: `400`
    - Body:
      ```json
      {
        "code": 400,
        "message": "Thông tin đầu vào không hợp lệ."
      }
      ```
  - Code: `500`
    - Body:
      ```json
      {
        "code": 500,
        "message": "Lỗi máy chủ, vui lòng thử lại sau."
      }
      ```

**Lưu ý:** Đã sửa lỗi trùng lặp `"totalItems"` trong `meta` và chuẩn hóa các khóa thành `"totalItems"`, `"currentPage"`, `"itemsPerPage"`, `"totalPages"`.

---

### 3. Xem Chi Tiết Vector Store

**Endpoint:** `/vector-stores/{id}/files`
**Method:** `GET`
**Mô tả:** API này được sử dụng để xem danh sách các file tri thức đã được liên kết với vector store cụ thể.

#### Path Parameters
- `id`: string - ID duy nhất của vector store

#### Query Parameters
- `page`: number (tùy chọn) - Số trang, mặc định là `1`
- `limit`: number (tùy chọn) - Số lượng kết quả trên một trang, mặc định là `10`

#### Ví dụ Request
```
GET /vector-stores/a1b2c3d4-e5f6-7890-abcd-ef1234567890/files?page=1&limit=10
```

#### Response
- **Thành công:**
  - Code: `200`
  - Body:
    ```json
    {
      "code": 200,
      "data": {
        "items": [
          {
            "id": 1,
            "name": "knowledge_file1.pdf",
            "createdAt": 1629026400,
            "storage": 512
          },
          {
            "id": 2,
            "name": "knowledge_file2.docx",
            "createdAt": 1629112800,
            "storage": 256
          }
        ],
        "meta": {
          "totalItems": 15,
          "currentPage": 1,
          "itemsPerPage": 10,
          "totalPages": 2
        }
      },
      "message": "Lấy chi tiết vector store thành công."
    }
    ```

- **Thất bại:**
  - Code: `404`
    - Body:
      ```json
      {
        "code": 404,
        "message": "Không tìm thấy vector store với ID đã cung cấp."
      }
      ```
  - Code: `500`
    - Body:
      ```json
      {
        "code": 500,
        "message": "Lỗi máy chủ, vui lòng thử lại sau."
      }
      ```

**Lưu ý:** Chuẩn hóa tên trường `"created_at"` thành `"createdAt"` và sửa các khóa trong `meta` để nhất quán.

---

### 4. Xóa Vector Store

**Endpoint:** `/vector-stores/{id}`
**Method:** `DELETE`
**Mô tả:** API này được sử dụng để xóa một vector store và gỡ bỏ liên kết với tất cả các file được liên kết. Thao tác này không xóa các file tri thức.

#### Path Parameters
- `id`: string - ID duy nhất của vector store cần xóa

#### Ví dụ Request
```
DELETE /vector-stores/a1b2c3d4-e5f6-7890-abcd-ef1234567890
```

#### Response
- **Thành công:**
  - Code: `200`
  - Body:
    ```json
    {
      "code": 200,
      "message": "Đã xóa vector store thành công."
    }
    ```

- **Thất bại:**
  - Code: `403`
    - Body:
      ```json
      {
        "code": 403,
        "message": "Bạn không có quyền thực hiện thao tác này."
      }
      ```
  - Code: `500`
    - Body:
      ```json
      {
        "code": 500,
        "message": "Lỗi máy chủ, vui lòng thử lại sau."
      }
      ```

---

### 5. Gán File Vào Vector Store

**Endpoint:** `/vector-stores/{vectorStoreId}/files`
**Method:** `POST`
**Mô tả:** API này được sử dụng để gán một hoặc nhiều file tri thức vào vector store. Các file sẽ được xử lý và chuyển đổi thành vector để sử dụng trong AI/RAG.

#### Path Parameters
- `vectorStoreId`: string - ID duy nhất của vector store

#### Request Body
```json
{
  "fileIds": [string] // Mảng các ID file cần gán
}
```

#### Ví dụ Request
```
POST /vector-stores/a1b2c3d4-e5f6-7890-abcd-ef1234567890/files
```
Body:
```json
{
  "fileIds": ['a1b2c3d4-e5f6-7890-abcd-ef1234567890', 'a1b2c3d4-e5f6-7890-abcd-ef1234567890']
}
```

#### Response
- **Thành công:**
  - Code: `200`
  - Body:
    ```json
    {
      "code": 200,
      "message": "Đã gán file vào vector store thành công."
    }
    ```

- **Thất bại:**
  - Code: `403`
    - Body:
      ```json
      {
        "code": 403,
        "message": "Bạn không có quyền thực hiện thao tác này."
      }
      ```
  - Code: `500`
    - Body:
      ```json
      {
        "code": 500,
        "message": "Lỗi máy chủ, vui lòng thử lại sau."
      }
      ```

---

### 6. Thêm Nhiều Files Tri Thức

**Endpoint:** `/files/batch`
**Method:** `POST`
**Mô tả:** API này được sử dụng để thêm nhiều file tri thức mới vào hệ thống. Các file có thể được liên kết với vector store sau bằng API riêng.

#### Request Body
```json
{
  "files": [
    {
      "name": "string", // Tên file
      "mime": "string", // Loại file (MIME type, phải nằm trong FileTypeEnum)
      "storage": number // Dung lượng file (bytes)
    }
  ]
}
```

#### Các loại file được hỗ trợ (FileTypeEnum)

**Chú ý quan trọng**: Hệ thống **chỉ chấp nhận** các loại file có MIME type được định nghĩa trong `FileTypeEnum`. Bất kỳ loại file nào khác sẽ bị từ chối với lỗi 400 Bad Request.

Các loại file được hỗ trợ:

| File Extension | MIME Type |
|---------------|------------|
| .c | `text/x-c` |
| .cpp | `text/x-c++` |
| .cs | `text/x-csharp` |
| .css | `text/css` |
| .doc | `application/msword` |
| .docx | `application/vnd.openxmlformats-officedocument.wordprocessingml.document` |
| .go | `text/x-golang` |
| .html | `text/html` |
| .java | `text/x-java` |
| .js | `text/javascript` |
| .json | `application/json` |
| .md | `text/markdown` |
| .pdf | `application/pdf` |
| .php | `text/x-php` |
| .pptx | `application/vnd.openxmlformats-officedocument.presentationml.presentation` |
| .py | `text/x-python` |
| .rb | `text/x-ruby` |
| .sh | `application/x-sh` |
| .tex | `text/x-tex` |
| .ts | `application/typescript` |
| .txt | `text/plain` |

#### Ví dụ Request
```
POST /files/batch
```
Body:
```json
{
  "files": [
    {
      "name": "Tài liệu hướng dẫn.pdf",
      "mime": "application/pdf",
      "storage": 1024
    },
    {
      "name": "Tài liệu kỹ thuật.docx",
      "mime": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "storage": 2048
    }
  ]
}
```

#### Response
- **Thành công:**
  - Code: `201`
  - Body:
    ```json
    {
      "code": 201,
      "message": "Đã tạo files tri thức thành công."
    }
    ```

- **Thất bại:**
  - Code: `400`
    - Body:
      ```json
      {
        "code": 400,
        "message": "Dữ liệu đầu vào không hợp lệ."
      }
      ```
  - Code: `403`
    - Body:
      ```json
      {
        "code": 403,
        "message": "Bạn không có quyền thực hiện thao tác này."
      }
      ```
  - Code: `500`
    - Body:
      ```json
      {
        "code": 500,
        "message": "Lỗi máy chủ, vui lòng thử lại sau."
      }
      ```

**Lưu ý:** Đã sửa MIME type của file `.docx` cho chính xác.

---

### 7. Xem Danh Sách Files

**Endpoint:** `/files`
**Method:** `GET`
**Mô tả:** API này được sử dụng để lấy danh sách các file tri thức trong hệ thống.

#### Query Parameters
- `vectorStoreId`: string (tùy chọn) - ID của vector store để lọc file
- `extensions`: string (tùy chọn) - Lọc theo định dạng file (ví dụ: "pdf,docx,txt")
- `search`: string (tùy chọn) - Tên file để tìm kiếm
- `page`: number (tùy chọn) - Số trang, mặc định là `1`
- `limit`: number (tùy chọn) - Số lượng kết quả trên một trang, mặc định là `10`
- `sortBy`: string (tùy chọn) - Trường sắp xếp (ví dụ: "createdAt", "updatedAt")
- `sortDirection`: string (tùy chọn) - Hướng sắp xếp ("asc" hoặc "desc")

#### Ví dụ Request
```
GET /files?vectorStoreId=1234&extensions=pdf&page=1&limit=20&sortBy=createdAt&sortDirection=desc
```

#### Response
- **Thành công:**
  - Code: `200`
  - Body:
    ```json
    {
      "code": 200,
      "data": {
        "items": [
          {
            "id": 1,
            "name": "Tài liệu hướng dẫn.pdf",
            "extension": "pdf",
            "storage": 1024,
            "vectorStoreId": "1234",
            "vectorStoreName": "Vector Store A",
            "downloadURL": "https://s3.amazonaws.com/bucket-name/path/to/file.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=...",
            "createdAt": 1629026400,
            "updatedAt": 1629026400
          },
          {
            "id": 2,
            "name": "Tài liệu kỹ thuật.docx",
            "extension": "docx",
            "storage": 2048,
            "vectorStoreId": "1234",
            "vectorStoreName": "Vector Store A",
            "downloadURL": "https://s3.amazonaws.com/bucket-name/path/to/file.docx?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=...",
            "createdAt": 1629022800,
            "updatedAt": 1629022800
          }
        ],
        "meta": {
          "totalItems": 15,
          "currentPage": 1,
          "itemsPerPage": 20,
          "totalPages": 1
        }
      },
      "message": "Lấy danh sách file thành công."
    }
    ```

- **Thất bại:**
  - Code: `400`
    - Body:
      ```json
      {
        "code": 400,
        "message": "Tham số không hợp lệ."
      }
      ```
  - Code: `403`
    - Body:
      ```json
      {
        "code": 403,
        "message": "Bạn không có quyền truy cập danh sách file này."
      }
      ```
  - Code: `500`
    - Body:
      ```json
      {
        "code": 500,
        "message": "Lỗi máy chủ, vui lòng thử lại sau."
      }
      ```

**Lưu ý:** Đã sửa `"extensions"` thành `"extension"` (số ít) trong phản hồi và sửa lỗi `"dowloadURL"` thành `"downloadURL"`.

---

### 8. Xóa File

**Endpoint:** `/files/{id}`
**Method:** `DELETE`
**Mô tả:** API này được sử dụng để xóa một file tri thức khỏi S3 và cơ sở dữ liệu va Openai.