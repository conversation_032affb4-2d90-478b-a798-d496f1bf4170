import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class FileInVectorStoreDto {
  @ApiProperty({
    description: 'ID của file',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Tên file',
    example: 'Tài liệu hướng dẫn.pdf',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Định dạng file',
    example: 'pdf',
  })
  @Expose()
  extension: string;

  @ApiProperty({
    description: 'Dung lượng file (bytes)',
    example: 1024000,
  })
  @Expose()
  storage: number;

  @ApiProperty({
    description: 'Thời điểm tạo (Unix timestamp)',
    example: 1629026400000,
  })
  @Expose()
  createdAt: number;

  @ApiProperty({
    description: 'Trạng thái file',
    example: 'APPROVED',
  })
  @Expose()
  status: string;
}
