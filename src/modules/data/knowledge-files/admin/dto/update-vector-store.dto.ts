import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MaxLength, IsOptional, IsNumber, Min } from 'class-validator';

/**
 * DTO cho việc cập nhật vector store
 */
export class UpdateVectorStoreDto {
  /**
   * Tên của vector store
   * @example "Vector Store của hệ thống"
   */
  @ApiProperty({
    description: 'Tên của vector store',
    example: 'Vector Store của hệ thống',
    required: true,
  })
  @IsNotEmpty({ message: 'Tên vector store không được để trống' })
  @IsString({ message: 'Tên vector store phải là chuỗi' })
  @MaxLength(100, { message: 'Tên vector store không được vượt quá 100 ký tự' })
  name: string;

  /**
   * Dung lượng của vector store (bytes)
   * @example 2048
   */
  @ApiProperty({
    description: 'Dung lượng của vector store (bytes)',
    example: 2048,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Dung lượng phải là số' })
  @Min(0, { message: 'Dung lượng không được âm' })
  storage?: number;
}
