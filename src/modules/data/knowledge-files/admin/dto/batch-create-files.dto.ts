import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  MaxLength,
  ValidateNested
} from 'class-validator';

class FileDto {
  @ApiProperty({
    description: 'Tên file',
    example: 'Tài liệu hướng dẫn.pdf',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Tên file không được để trống' })
  @IsString({ message: 'Tên file phải là chuỗi' })
  @MaxLength(255, { message: 'Tên file không được vượt quá 255 ký tự' })
  name: string;

  @ApiProperty({
    description: 'Loại file (MIME type)',
    example: 'application/pdf',
    maxLength: 100,
  })
  @IsNotEmpty({ message: 'Loại file không được để trống' })
  @IsString({ message: 'Loại file phải là chuỗi' })
  @MaxLength(100, { message: 'Loại file không được vượt quá 100 ký tự' })
  mime: string;

  @ApiProperty({
    description: 'Dung lượng file (bytes)',
    example: 1024,
  })
  @IsNotEmpty({ message: 'Dung lượng file không được để trống' })
  @IsNumber({}, { message: 'Dung lượng file phải là số' })
  @IsPositive({ message: 'Dung lượng file phải là số dương' })
  storage: number;
}

export class BatchCreateFilesDto {
  @ApiProperty({
    description: 'Danh sách các file cần tạo',
    type: [FileDto],
  })
  @IsArray({ message: 'Files phải là một mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất một file' })
  @ValidateNested({ each: true })
  @Type(() => FileDto)
  files: FileDto[];
}
