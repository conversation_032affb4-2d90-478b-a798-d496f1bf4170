import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions/app.exception';

/**
 * Mã lỗi cho các thao tác với file tri thức và vector store
 */
export const KNOWLEDGE_FILE_ERROR_CODES = {
  // Knowledge File Error Codes (20101-20199)
  KNOWLEDGE_FILE_CREATE_ERROR: new ErrorCode(
    20101,
    'Lỗi khi tạo file tri thức',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  KNOWLEDGE_FILE_LIST_ERROR: new ErrorCode(
    20102,
    'Lỗi khi lấy danh sách file tri thức',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  KNOWLEDGE_FILE_DELETE_ERROR: new ErrorCode(
    20103,
    'Lỗi khi xóa file tri thức',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  KNOWLEDGE_FILE_NOT_FOUND: new ErrorCode(
    20104,
    'Không tìm thấy file tri thức',
    HttpStatus.NOT_FOUND,
  ),

  KNOWLEDGE_FILE_UPLOAD_ERROR: new ErrorCode(
    20105,
    'Lỗi khi tải lên file tri thức',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  KNOWLEDGE_FILE_DOWNLOAD_ERROR: new ErrorCode(
    20106,
    'Lỗi khi tải xuống file tri thức',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  KNOWLEDGE_FILE_PERMISSION_ERROR: new ErrorCode(
    20107,
    'Không có quyền truy cập file tri thức',
    HttpStatus.FORBIDDEN,
  ),

  KNOWLEDGE_FILE_INVALID_STATUS: new ErrorCode(
    20108,
    'Trạng thái file không hợp lệ cho thao tác này',
    HttpStatus.BAD_REQUEST,
  ),

  KNOWLEDGE_FILE_STATUS_CHANGE_ERROR: new ErrorCode(
    20109,
    'Lỗi khi thay đổi trạng thái file',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Vector Store Error Codes (20201-20299)
  VECTOR_STORE_CREATE_ERROR: new ErrorCode(
    20201,
    'Lỗi khi tạo vector store',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  VECTOR_STORE_LIST_ERROR: new ErrorCode(
    20202,
    'Lỗi khi lấy danh sách vector store',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  VECTOR_STORE_DETAIL_ERROR: new ErrorCode(
    20203,
    'Lỗi khi lấy chi tiết vector store',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  VECTOR_STORE_DELETE_ERROR: new ErrorCode(
    20204,
    'Lỗi khi xóa vector store',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  VECTOR_STORE_NOT_FOUND: new ErrorCode(
    20205,
    'Không tìm thấy vector store',
    HttpStatus.NOT_FOUND,
  ),

  VECTOR_STORE_ASSIGN_FILES_ERROR: new ErrorCode(
    20206,
    'Lỗi khi gán file vào vector store',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  VECTOR_STORE_REMOVE_FILES_ERROR: new ErrorCode(
    20207,
    'Lỗi khi xóa file khỏi vector store',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  VECTOR_STORE_UPDATE_ERROR: new ErrorCode(
    20208,
    'Lỗi khi cập nhật vector store',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  FILE_PROCESSING_ERROR: new ErrorCode(
    20209,
    'Lỗi khi xử lý file',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  FILE_NOT_FOUND: new ErrorCode(
    20210,
    'Không tìm thấy file',
    HttpStatus.NOT_FOUND,
  ),
};
