import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho SSE tracking response
 */
export class TrackingResponseDto {
  @ApiProperty({
    description: 'ID của file',
    example: 'file-KBYXBHVZX8MMk4BP',
  })
  @Expose()
  file_id: string;

  @ApiProperty({
    description: 'Tiến độ xử lý (0-100)',
    example: 50,
    minimum: 0,
    maximum: 100,
  })
  @Expose()
  progress: number;

  @ApiProperty({
    description: 'Trạng thái xử lý',
    example: 'processing',
    enum: ['pending', 'processing', 'completed', 'error', 'cancelled'],
  })
  @Expose()
  status: string;

  @ApiProperty({
    description: 'Thông báo mô tả tiến độ',
    example: 'Đang tạo embeddings',
  })
  @Expose()
  message: string;

  @ApiProperty({
    description: 'Chi tiết bổ sung',
    example: 'Đã xử lý 10/20 chunks',
    required: false,
  })
  @Expose()
  details?: string;

  @ApiProperty({
    description: 'Timestamp của event',
    example: 1640995200000,
  })
  @Expose()
  timestamp: number;

  @ApiProperty({
    description: 'Metadata bổ sung',
    example: {
      storage_updated: true,
      vector_store_id: 'vs_123',
      storage_size: 1024000,
      chunks_count: 20,
    },
    required: false,
  })
  @Expose()
  metadata?: Record<string, any>;
}

/**
 * DTO cho tracking progress steps
 */
export class TrackingProgressSteps {
  static readonly QUEUED = 0;
  static readonly DOWNLOAD_STARTED = 10;
  static readonly DOWNLOAD_COMPLETED = 30;
  static readonly CONTENT_EXTRACTION = 50;
  static readonly CHUNKING_COMPLETED = 70;
  static readonly EMBEDDING_GENERATION = 90;
  static readonly ALL_COMPLETED = 100;

  static getStepMessage(progress: number): string {
    switch (progress) {
      case this.QUEUED:
        return 'File đã được xếp hàng chờ xử lý';
      case this.DOWNLOAD_STARTED:
        return 'Bắt đầu tải file từ storage';
      case this.DOWNLOAD_COMPLETED:
        return 'Hoàn thành tải file';
      case this.CONTENT_EXTRACTION:
        return 'Đang trích xuất nội dung';
      case this.CHUNKING_COMPLETED:
        return 'Hoàn thành chia nhỏ nội dung';
      case this.EMBEDDING_GENERATION:
        return 'Đang tạo vector embeddings';
      case this.ALL_COMPLETED:
        return 'Hoàn thành xử lý file';
      default:
        return 'Đang xử lý...';
    }
  }
}

/**
 * DTO cho tracking status
 */
export enum TrackingStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  ERROR = 'error',
  CANCELLED = 'cancelled',
}
