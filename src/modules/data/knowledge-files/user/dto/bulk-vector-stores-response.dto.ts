import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho thông tin vector store trong response bulk create
 */
export class BulkVectorStoreItemDto {
  @ApiProperty({
    description: 'ID của vector store',
    example: 'vs_681c63f7b8d48191a3394cb3b025b0e7',
  })
  @Expose()
  storeId: string;

  @ApiProperty({
    description: 'Tên vector store',
    example: 'Vector Store 1',
  })
  @Expose()
  storeName: string;

  @ApiProperty({
    description: 'Dung lượng đã sử dụng (bytes)',
    example: 0,
  })
  @Expose()
  size: number;

  @ApiProperty({
    description: 'Số lượng agents sử dụng vector store',
    example: 0,
  })
  @Expose()
  agents: number;

  @ApiProperty({
    description: 'Số lượng files trong vector store',
    example: 0,
  })
  @Expose()
  files: number;

  @ApiProperty({
    description: 'Thời điểm tạo (Unix timestamp)',
    example: 1640995200000,
  })
  @Expose()
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật (Unix timestamp)',
    example: 1640995200000,
  })
  @Expose()
  updatedAt: number;
}

/**
 * DTO cho response của bulk create vector stores
 */
export class BulkVectorStoresResponseDto {
  @ApiProperty({
    description: 'Số lượng vector stores đã tạo thành công',
    example: 2,
  })
  @Expose()
  createdCount: number;

  @ApiProperty({
    description: 'Thông điệp kết quả',
    example: 'Tạo vector stores thành công',
  })
  @Expose()
  message: string;

  @ApiProperty({
    description: 'Trạng thái thành công',
    example: true,
  })
  @Expose()
  success: boolean;

  @ApiProperty({
    description: 'Danh sách các vector stores đã tạo',
    type: [BulkVectorStoreItemDto],
  })
  @Expose()
  vectorStores: BulkVectorStoreItemDto[];
}
