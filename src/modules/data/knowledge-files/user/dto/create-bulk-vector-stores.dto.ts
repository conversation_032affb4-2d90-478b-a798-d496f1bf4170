import { ApiProperty } from '@nestjs/swagger';
import { ArrayMaxSize, ArrayMinSize, IsArray, IsNotEmpty, IsString, MaxLength, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho một vector store trong bulk creation
 */
export class VectorStoreItemDto {
  @ApiProperty({
    description: 'Tên vector store',
    example: 'VectorStoreAI',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Tên vector store không được để trống' })
  @IsString({ message: 'Tên vector store phải là chuỗi' })
  @MaxLength(255, { message: 'Tên vector store không được vượt quá 255 ký tự' })
  name: string;
}

/**
 * DTO cho việc tạo nhiều vector stores cùng lúc
 */
export class CreateBulkVectorStoresDto {
  /**
   * Danh sách các vector stores cần tạo
   */
  @ApiProperty({
    description: 'Danh sách các vector stores cần tạo',
    type: [VectorStoreItemDto],
    example: [
      {
        name: 'Vector Store 1'
      },
      {
        name: 'Vector Store 2'
      }
    ],
    minItems: 1,
    maxItems: 50,
  })
  @IsArray({ message: 'vectorStores phải là một mảng' })
  @IsNotEmpty({ message: 'Danh sách vector stores không được để trống' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất 1 vector store để tạo' })
  @ArrayMaxSize(50, { message: 'Không thể tạo quá 50 vector stores cùng lúc' })
  @ValidateNested({ each: true })
  @Type(() => VectorStoreItemDto)
  vectorStores: VectorStoreItemDto[];
}
