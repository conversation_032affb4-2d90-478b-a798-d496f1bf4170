import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Observable, Subject } from 'rxjs';
import { RagFileProcessingService } from '@shared/services/ai/rag-file-processing.service';
import { TrackingResponseDto, TrackingStatus } from '../dto/tracking-response.dto';
import { AppException } from '@common/exceptions';
import { KNOWLEDGE_FILE_ERROR_CODES } from '../exceptions/error-codes';
import { VectorStore, KnowledgeFile } from '../../entities';
import { UserRagApiKeyRepository } from '@modules/user/repositories';

@Injectable()
export class TrackingUserService {
  private readonly logger = new Logger(TrackingUserService.name);

  constructor(
    @InjectRepository(VectorStore)
    private readonly vectorStoreRepository: Repository<VectorStore>,
    @InjectRepository(KnowledgeFile)
    private readonly knowledgeFileRepository: Repository<KnowledgeFile>,
    private readonly ragFileProcessingService: RagFileProcessingService,
    private readonly userRagApiKeyRepository: UserRagApiKeyRepository,
  ) {}

  /**
   * Theo dõi tiến độ xử lý file từ RAG API và cập nhật storage
   * @param fileId ID của file cần theo dõi
   * @param userId ID của người dùng
   * @returns Observable stream của tracking events
   */
  async trackFileProgress(
    fileId: string,
    userId: number,
  ): Promise<Observable<TrackingResponseDto>> {
    try {
      this.logger.log(`Bắt đầu tracking file ${fileId} cho user ${userId}`);

      // Kiểm tra file có tồn tại và thuộc về user không
      const knowledgeFile = await this.knowledgeFileRepository.findOne({
        where: {
          fileId: fileId,
          ownedBy: userId,
        },
      });

      if (!knowledgeFile) {
        throw new AppException(
          KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_NOT_FOUND,
          'File không tồn tại hoặc bạn không có quyền truy cập',
        );
      }

      // Tạo Subject để stream events
      const eventSubject = new Subject<TrackingResponseDto>();

      // Bắt đầu tracking từ RAG API
      this.startRagTracking(fileId, knowledgeFile, eventSubject);

      return eventSubject.asObservable();
    } catch (error) {
      this.logger.error(
        `Error starting tracking for file ${fileId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Bắt đầu tracking từ RAG API
   */
  private async startRagTracking(
    fileId: string,
    knowledgeFile: KnowledgeFile,
    eventSubject: Subject<TrackingResponseDto>,
  ): Promise<void> {
    try {
      // Gọi RAG API để tracking
      const ragTrackingStream = await this.ragFileProcessingService.trackFileProgress(fileId);

      ragTrackingStream.subscribe({
        next: async (ragEvent) => {
          try {
            // Chuyển đổi event từ RAG API
            const trackingEvent = this.transformRagEvent(ragEvent);

            // Xử lý storage update nếu cần
            await this.handleStorageUpdate(ragEvent, knowledgeFile);

            // Emit event
            eventSubject.next(trackingEvent);

            // Đóng stream khi hoàn thành hoặc lỗi
            if (['completed', 'error', 'cancelled'].includes(trackingEvent.status)) {
              eventSubject.complete();
            }
          } catch (error) {
            this.logger.error(`Error processing RAG event: ${error.message}`);
            eventSubject.error(error);
          }
        },
        error: (error) => {
          this.logger.error(`RAG tracking error: ${error.message}`);
          eventSubject.error(error);
        },
        complete: () => {
          this.logger.log(`RAG tracking completed for file ${fileId}`);
          eventSubject.complete();
        },
      });
    } catch (error) {
      this.logger.error(`Error starting RAG tracking: ${error.message}`);
      eventSubject.error(error);
    }
  }

  /**
   * Chuyển đổi event từ RAG API sang format của hệ thống
   */
  private transformRagEvent(ragEvent: any): TrackingResponseDto {
    return {
      file_id: ragEvent.file_id,
      progress: ragEvent.progress || 0,
      status: ragEvent.status || TrackingStatus.PENDING,
      message: ragEvent.message || 'Đang xử lý...',
      details: ragEvent.details,
      timestamp: ragEvent.timestamp || Date.now(),
      metadata: ragEvent.metadata || {},
    };
  }

  /**
   * Xử lý cập nhật storage khi có thông báo từ RAG API
   */
  private async handleStorageUpdate(ragEvent: any, knowledgeFile: KnowledgeFile): Promise<void> {
    try {
      const metadata = ragEvent.metadata || {};

      // Log chi tiết về event nhận được
      this.logger.log(`Nhận RAG event: status=${ragEvent.status}, progress=${ragEvent.progress}`);
      this.logger.log(`Metadata: ${JSON.stringify(metadata)}`);

      // Kiểm tra nếu có storage update
      if (metadata.storage_updated && metadata.vector_store_id && metadata.storage_size) {
        const vectorStoreId = metadata.vector_store_id;
        const storageSize = metadata.storage_size;

        this.logger.log(`🔄 Cập nhật storage cho vector store ${vectorStoreId}: ${storageSize} bytes`);

        // Kiểm tra vector store có tồn tại không
        const existingVectorStore = await this.vectorStoreRepository.findOne({
          where: { id: vectorStoreId }
        });

        if (!existingVectorStore) {
          this.logger.warn(`⚠️ Vector store ${vectorStoreId} không tồn tại trong database`);
          return;
        }

        this.logger.log(`📊 Vector store hiện tại: storage=${existingVectorStore.storage}, sẽ cập nhật thành ${storageSize}`);

        // Cập nhật storage trong vector store
        const updateResult = await this.vectorStoreRepository.update(
          { id: vectorStoreId },
          {
            storage: storageSize,
            updateAt: Date.now(),
          },
        );

        this.logger.log(`✅ Kết quả cập nhật vector store: affected=${updateResult.affected}`);

        // Cập nhật storage trong knowledge file nếu có
        if (metadata.file_storage_size) {
          const fileUpdateResult = await this.knowledgeFileRepository.update(
            { id: knowledgeFile.id },
            {
              storage: metadata.file_storage_size,
            },
          );
          this.logger.log(`✅ Cập nhật file storage: ${metadata.file_storage_size} bytes, affected=${fileUpdateResult.affected}`);
        }

        this.logger.log(`🎉 Đã cập nhật storage thành công cho vector store ${vectorStoreId}`);
      } else {
        // Log khi không có storage update
        if (!metadata.storage_updated) {
          this.logger.debug(`📝 Event không có storage_updated flag`);
        }
        if (!metadata.vector_store_id) {
          this.logger.debug(`📝 Event không có vector_store_id`);
        }
        if (!metadata.storage_size) {
          this.logger.debug(`📝 Event không có storage_size`);
        }
      }

      // Xử lý khi processing hoàn thành
      if (metadata.processing_completed && ragEvent.status === 'completed') {
        this.logger.log(`🏁 File ${ragEvent.file_id} đã hoàn thành xử lý với ${metadata.chunks_count || 0} chunks`);

        // Kiểm tra nếu có final_storage_size trong completion event
        if (metadata.final_storage_size && metadata.vector_store_id) {
          this.logger.log(`🔄 Cập nhật final storage từ completion event: ${metadata.final_storage_size} bytes`);

          const finalUpdateResult = await this.vectorStoreRepository.update(
            { id: metadata.vector_store_id },
            {
              storage: metadata.final_storage_size,
              updateAt: Date.now(),
            },
          );

          this.logger.log(`✅ Final storage update: affected=${finalUpdateResult.affected}`);
        }
      }
    } catch (error) {
      this.logger.error(`❌ Error handling storage update: ${error.message}`, error.stack);
      // Không throw error để không làm gián đoạn tracking stream
    }
  }
}
