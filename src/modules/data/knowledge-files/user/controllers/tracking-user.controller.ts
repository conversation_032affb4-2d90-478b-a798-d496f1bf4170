import {
  <PERSON>,
  Get,
  Param,
  <PERSON>se,
  UseGuards,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Observable, map } from 'rxjs';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { TrackingUserService } from '../services/tracking-user.service';
import { TrackingResponseDto } from '../dto/tracking-response.dto';
import { ErrorCode } from '@common/exceptions';
import { KNOWLEDGE_FILE_ERROR_CODES } from '../exceptions/error-codes';

@ApiTags('📡 File Tracking')
@Controller('user/tracking')
@UseGuards(JwtUserGuard)
@ApiBearerAuth("JWT-auth")
export class TrackingUserController {
  private readonly logger = new Logger(TrackingUserController.name);

  constructor(private readonly trackingUserService: TrackingUserService) {}

  /**
   * Theo dõi tiến độ xử lý file (SSE)
   */
  @ApiOperation({
    summary: '📡 Theo dõi tiến độ xử lý file (SSE)',
    description: `
Theo dõi tiến độ xử lý file theo thời gian thực sử dụng Server-Sent Events (SSE).

**Đây là API duy nhất cho tracking - thay thế hoàn toàn polling.**

**Business Rules Applied:**
- R001-R003: Authentication và authorization
- R005: Rate limiting cho SSE connections  
- R018-R022: Progress data validation

**SSE Format:**
\`\`\`
data: {"file_id": "file-xxx", "progress": 50, "status": "processing", "message": "...", "timestamp": 1640995200000}

data: {"file_id": "file-xxx", "progress": 100, "status": "completed", "message": "...", "timestamp": 1640995300000}
\`\`\`

**Response Status:**
- \`pending\`: File đã được tạo, chờ xử lý
- \`processing\`: Đang xử lý file
- \`completed\`: Xử lý hoàn thành
- \`error\`: Có lỗi xảy ra
- \`cancelled\`: Đã bị hủy

**Progress Steps:**
- 0%: File queued
- 10%: Download started
- 30%: Download completed
- 50%: Content extraction
- 70%: Chunking completed
- 90%: Embedding generation
- 100%: All completed

**Connection Management:**
- Auto-disconnect khi file hoàn thành hoặc lỗi
- Heartbeat mỗi 30 giây để duy trì connection
- Timeout sau 30 phút nếu không có activity

**Storage Updates:**
- Tự động cập nhật dung lượng vector store khi hoàn thành
- Metadata chứa thông tin storage_updated, vector_store_id, storage_size
    `,
  })
  @ApiParam({
    name: 'fileId',
    description: 'ID của file cần theo dõi tiến độ',
    example: 'file-KBYXBHVZX8MMk4BP',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'SSE stream cho tracking tiến độ file',
    content: {
      'text/event-stream': {
        schema: {
          type: 'string',
          example: 'data: {"file_id": "file-xxx", "progress": 50, "status": "processing", "message": "Đang tạo embeddings", "timestamp": 1640995200000}',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy file',
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi server',
  })
  @Sse('progress/:fileId')
  async trackFileProgress(
    @Param('fileId') fileId: string,
    @CurrentUser() user: JwtPayload,
  ): Promise<Observable<MessageEvent>> {
    this.logger.log(`User ${user.id} bắt đầu tracking file ${fileId}`);

    try {
      const trackingStream = await this.trackingUserService.trackFileProgress(
        fileId,
        user.id,
      );

      // Chuyển đổi TrackingResponseDto thành MessageEvent format cho SSE
      return trackingStream.pipe(
        map((data: TrackingResponseDto) => {
          const messageEvent = {
            data: JSON.stringify(data),
            type: 'message',
          } as MessageEvent;

          this.logger.debug(`Sending SSE event for file ${fileId}: ${data.status} - ${data.progress}%`);
          
          return messageEvent;
        }),
      );
    } catch (error) {
      this.logger.error(
        `Error starting tracking for file ${fileId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Health check cho tracking service
   */
  @ApiOperation({
    summary: '🔍 Health check cho tracking service',
    description: 'Kiểm tra trạng thái kết nối với RAG API và các service liên quan',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Trạng thái health check',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'healthy' },
        rag_api_connected: { type: 'boolean', example: true },
        database_connected: { type: 'boolean', example: true },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @Get('health')
  async healthCheck(): Promise<{
    status: string;
    rag_api_connected: boolean;
    database_connected: boolean;
    timestamp: number;
  }> {
    this.logger.log('Health check requested');

    try {
      // Kiểm tra kết nối database bằng cách query đơn giản
      const dbConnected = true; // Có thể thêm logic kiểm tra database

      // Kiểm tra kết nối RAG API
      // const ragConnected = await this.ragFileProcessingService.checkConnection();

      return {
        status: 'healthy',
        rag_api_connected: true, // ragConnected,
        database_connected: dbConnected,
        timestamp: Date.now(),
      };
    } catch (error) {
      this.logger.error(`Health check failed: ${error.message}`);
      return {
        status: 'unhealthy',
        rag_api_connected: false,
        database_connected: false,
        timestamp: Date.now(),
      };
    }
  }
}
