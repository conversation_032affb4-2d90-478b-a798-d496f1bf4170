import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MediaAdminController } from './controllers/media-admin.controller';
import { MediaAdminService } from './services/media-admin.service';
import { MediaTrackingAdminController } from './controllers/media-tracking-admin.controller';
import { MediaTrackingAdminService } from './services/media-tracking-admin.service';
import { Media } from '../entities/media.entity';
import { MediaRepository } from '../repositories';
import {DataSource} from "typeorm";
import {User} from "@modules/user/entities";
import { AgentMediaRepository } from '@modules/agent/repositories/agent-media.repository';
import { MediaValidationHelper } from '../helpers/validation.helper';
import { RagFileProcessingService } from '@shared/services/ai/rag-file-processing.service';
import { HttpModule } from '@nestjs/axios';
import { UserRepository } from '@modules/user/repositories';

@Module({
  imports: [TypeOrmModule.forFeature([Media,User]), HttpModule],
  controllers: [MediaAdminController, MediaTrackingAdminController],
  providers: [MediaAdminService,
    {
      provide: MediaRepository,
      useFactory: (dataSource: DataSource) => {
        return new MediaRepository(dataSource);
      },
      inject: [DataSource],
    },
    {
      provide: AgentMediaRepository,
      useFactory: (dataSource: DataSource) => {
        return new AgentMediaRepository(dataSource);
      },
      inject: [DataSource],
    },
    {
      provide: UserRepository,
      useFactory: (dataSource: DataSource) => {
        return dataSource.getRepository(User).extend(UserRepository.prototype);
      },
      inject: [DataSource],
    },
    MediaValidationHelper,
    RagFileProcessingService,
    MediaTrackingAdminService,
  ],
  exports: [MediaAdminService],
})
export class MediaAdminModule {}
