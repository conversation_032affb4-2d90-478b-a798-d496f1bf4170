import { ApiProperty } from '@nestjs/swagger';
import { IsA<PERSON>y, IsNotEmpty, IsOptional, IsUUID } from 'class-validator';

export class DeleteAgentMediaDto {
  @ApiProperty({
    description: 'ID của media cần x<PERSON><PERSON> liên kế<PERSON> (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty()
  @IsUUID('4')
  mediaId: string;

  @ApiProperty({
    description: 'Danh sách ID của các agent cần x<PERSON><PERSON> liên kết (UUID)',
    type: [String],
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  agentIds?: string[];
}
