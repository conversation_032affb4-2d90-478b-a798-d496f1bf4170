import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString } from 'class-validator';

export class CreatePresignedUrlDto {
  @ApiProperty({ description: 'Key of the file in S3' })
  @IsString()
  key: string;

  @ApiProperty({ description: 'Expiration time in milliseconds' })
  @IsNumber()
  expirationTimeInMillis: number;

  @ApiProperty({ description: 'Type of the file' })
  @IsString()
  type: string;

  @ApiProperty({ description: 'Maximum size of the file in bytes' })
  @IsNumber()
  maxSize: number;
}
