// import { Test, TestingModule } from '@nestjs/testing';
// import { MediaUserService} from "@modules/data/media/user/services";
// import { MediaRepository } from '../../repositories';
// import { S3Service } from '@/shared/services/s3.service';
// import { AgentMediaRepository } from '@modules/agent/repositories';
// import { getRepositoryToken } from '@nestjs/typeorm';
// import { User } from '@modules/user/entities';
// import { Media } from '../../entities/media.entity';
// import { AppException } from '@/common';
// import { MediaStatusEnum } from '@modules/data/media/enums/media-status.enum';
// import { SortDirection } from '@common/dto';
// import {initializeTransactionalContext} from "typeorm-transactional";
// import {DataSource} from "typeorm";

// jest.mock('@/shared/services/s3.service');

// beforeAll(() => {
//   initializeTransactionalContext();
// });
// let dataSource: DataSource;
// const mockMediaRepository = () => ({
//   createQueryBuilder: jest.fn(),
//   find: jest.fn(),
//   manager: {
//     connection: {
//       createQueryRunner: jest.fn().mockReturnValue({
//         connect: jest.fn(),
//         startTransaction: jest.fn(),
//         commitTransaction: jest.fn(),
//         rollbackTransaction: jest.fn(),
//         release: jest.fn(),
//         manager: {
//           delete: jest.fn(),
//           update: jest.fn(),
//         },
//       }),
//     },
//   },
// });

// const mockAgentMediaRepository = {};
// const mockUserRepository = () => ({
//   createQueryBuilder: jest.fn(() => ({
//     select: jest.fn().mockReturnThis(),
//     where: jest.fn().mockReturnThis(),
//     getOne: jest.fn(),
//   })),
// });

// const mockS3Service = () => ({
//   createPresignedDownloadUrl: jest.fn(),
//   getDownloadUrl: jest.fn(),
// });

// describe('MediaUserService', () => {
//   let service: MediaUserService;
//   let mediaRepository;
//   let s3Service;
//   let userRepository;

//   beforeEach(async () => {
//     const module: TestingModule = await Test.createTestingModule({
//       providers: [
//         MediaUserService,
//         { provide: MediaRepository, useFactory: mockMediaRepository },
//         { provide: AgentMediaRepository, useValue: mockAgentMediaRepository },
//         { provide: S3Service, useFactory: mockS3Service },
//         { provide: getRepositoryToken(User), useFactory: mockUserRepository },
//       ],
//     }).compile();



//     service = module.get<MediaUserService>(MediaUserService);
//     mediaRepository = module.get(MediaRepository);
//     s3Service = module.get(S3Service);
//     userRepository = module.get(getRepositoryToken(User));
//   });

//   describe('findById', () => {
//     it('should throw if media is not found', async () => {
//       mediaRepository.createQueryBuilder.mockReturnValue({
//         select: jest.fn().mockReturnThis(),
//         where: jest.fn().mockReturnThis(),
//         andWhere: jest.fn().mockReturnThis(),
//         getOne: jest.fn().mockResolvedValue(null),
//       });

//       await expect(service.findById('fake-id', 1)).rejects.toThrow(AppException);
//     });

//     it('should throw if user does not own the media', async () => {
//       mediaRepository.createQueryBuilder.mockReturnValue({
//         select: jest.fn().mockReturnThis(),
//         where: jest.fn().mockReturnThis(),
//         andWhere: jest.fn().mockReturnThis(),
//         getOne: jest.fn().mockResolvedValue({ id: 'media-id', ownedBy: 999 }),
//       });

//       await expect(service.findById('media-id', 1)).rejects.toThrow(AppException);
//     });

//     it('should return media response dto', async () => {
//       mediaRepository.createQueryBuilder.mockReturnValue({
//         select: jest.fn().mockReturnThis(),
//         where: jest.fn().mockReturnThis(),
//         andWhere: jest.fn().mockReturnThis(),
//         getOne: jest.fn().mockResolvedValue({
//           id: 'media-id',
//           name: 'Test Media',
//           description: 'desc',
//           tags: ['tag'],
//           ownedBy: 1,
//           storageKey: 'key'
//         }),
//       });

//       userRepository.createQueryBuilder().getOne.mockResolvedValue({
//         fullName: 'User Test',
//         avatar: 'avatar.jpg'
//       });

//       s3Service.createPresignedDownloadUrl.mockResolvedValue('presigned-url');
//       s3Service.getDownloadUrl.mockReturnValue('view-url');

//       const result = await service.findById('media-id', 1);
//       // @ts-ignore
//       expect(result.result.downloadURL).toEqual('presigned-url');
//       // @ts-ignore
//       expect(result.result.viewURL).toEqual('view-url');
//     });
//   });

//   describe('deleteManyByUser', () => {
//     it('should throw if ids are empty', async () => {
//       await expect(service.deleteManyByUser(1, [])).rejects.toThrow(AppException);
//     });

//     it('should return skipped if no deletable media', async () => {
//       mediaRepository.find.mockResolvedValue([{ id: 'id1', ownedBy: 2, status: MediaStatusEnum.DELETED }]);

//       await expect(service.deleteManyByUser(1, ['id1'])).rejects.toThrow(AppException);
//     });
//   });

//   describe('findAllByUser', () => {
//     it('should return paginated media', async () => {
//       mediaRepository.createQueryBuilder.mockReturnValue({
//         select: jest.fn().mockReturnThis(),
//         andWhere: jest.fn().mockReturnThis(),
//         orderBy: jest.fn().mockReturnThis(),
//         skip: jest.fn().mockReturnThis(),
//         take: jest.fn().mockReturnThis(),
//         getManyAndCount: jest.fn().mockResolvedValue([[{
//           id: 'media-id',
//           name: 'Test Media',
//           description: 'desc',
//           tags: ['tag'],
//           storageKey: 'key',
//         }], 1])
//       });

//       userRepository.createQueryBuilder().getOne.mockResolvedValue({
//         fullName: 'User Test',
//         avatar: 'avatar.jpg'
//       });

//       s3Service.createPresignedDownloadUrl.mockResolvedValue('presigned-url');
//       s3Service.getDownloadUrl.mockReturnValue('view-url');

//       const result = await service.findAllByUser(1, { page: 1, limit: 10, sortDirection: SortDirection.DESC });
//       // @ts-ignore
//       expect(result.result.items.length).toBe(1);
//       // @ts-ignore
//       expect(result.result.items[0].downloadURL).toEqual('presigned-url');
//     });
//   });
// });