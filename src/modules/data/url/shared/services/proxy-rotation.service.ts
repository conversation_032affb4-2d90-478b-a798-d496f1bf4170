import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { AxiosRequestConfig } from 'axios';

export interface ProxyConfig {
  host: string;
  port: number;
  auth?: {
    username: string;
    password: string;
  };
  protocol?: 'http' | 'https' | 'socks4' | 'socks5';
  isActive: boolean;
  lastUsed?: number;
  failureCount: number;
  maxFailures: number;
}

@Injectable()
export class ProxyRotationService {
  private readonly logger = new Logger(ProxyRotationService.name);

  private proxies: ProxyConfig[] = [];
  private currentProxyIndex = 0;
  private readonly MAX_FAILURES_PER_PROXY = 3;
  private readonly PROXY_COOLDOWN_TIME = 5 * 60 * 1000; // 5 phút

  constructor(private readonly httpService: HttpService) {
    this.initializeProxies();
  }

  /**
   * Khởi tạo danh sách proxy từ environment variables hoặc config
   */
  private initializeProxies(): void {
    try {
      // Load từ environment variables
      const proxyList = process.env.PROXY_LIST ? JSON.parse(process.env.PROXY_LIST) : [];

      // Proxy mặc định (free proxies - chỉ để demo, production nên dùng paid proxies)
      const defaultProxies = [
        // Có thể thêm free proxy servers ở đây
        // Lưu ý: Free proxies thường không ổn định, nên dùng paid proxies cho production
      ];

      this.proxies = [...proxyList, ...defaultProxies].map(proxy => ({
        ...proxy,
        isActive: true,
        failureCount: 0,
        maxFailures: this.MAX_FAILURES_PER_PROXY
      }));

      this.logger.log(`Initialized ${this.proxies.length} proxies from environment`);

      if (this.proxies.length === 0) {
        this.logger.warn('No proxies configured. Using direct connection only.');
      }
    } catch (error) {
      this.logger.error(`Failed to initialize proxies: ${error.message}`);
      this.proxies = [];
    }
  }

  /**
   * Lấy proxy tiếp theo trong rotation
   */
  private getNextProxy(): ProxyConfig | null {
    if (this.proxies.length === 0) {
      return null;
    }

    const now = Date.now();
    let attempts = 0;

    while (attempts < this.proxies.length) {
      const proxy = this.proxies[this.currentProxyIndex];

      // Chuyển sang proxy tiếp theo
      this.currentProxyIndex = (this.currentProxyIndex + 1) % this.proxies.length;
      attempts++;

      // Kiểm tra proxy có khả dụng không
      if (this.isProxyAvailable(proxy, now)) {
        proxy.lastUsed = now;
        return proxy;
      }
    }

    return null;
  }

  /**
   * Kiểm tra proxy có khả dụng không
   */
  private isProxyAvailable(proxy: ProxyConfig, now: number): boolean {
    if (!proxy.isActive) {
      return false;
    }

    if (proxy.failureCount >= proxy.maxFailures) {
      // Kiểm tra cooldown time
      if (proxy.lastUsed && (now - proxy.lastUsed) < this.PROXY_COOLDOWN_TIME) {
        return false;
      }
      // Reset failure count sau cooldown
      proxy.failureCount = 0;
    }

    return true;
  }

  /**
   * Báo cáo lỗi proxy
   */
  private reportProxyFailure(proxy: ProxyConfig): void {
    proxy.failureCount++;
    this.logger.warn(`Proxy ${proxy.host}:${proxy.port} failed. Failure count: ${proxy.failureCount}/${proxy.maxFailures}`);

    if (proxy.failureCount >= proxy.maxFailures) {
      this.logger.warn(`Proxy ${proxy.host}:${proxy.port} disabled due to too many failures`);
    }
  }

  /**
   * Tạo AxiosRequestConfig với proxy
   */
  public createProxyConfig(baseConfig: AxiosRequestConfig = {}): AxiosRequestConfig {
    const proxy = this.getNextProxy();

    if (!proxy) {
      this.logger.debug('No proxy available, using direct connection');
      return baseConfig;
    }

    this.logger.debug(`Using proxy: ${proxy.host}:${proxy.port}`);

    const proxyConfig: AxiosRequestConfig = {
      ...baseConfig,
      proxy: {
        host: proxy.host,
        port: proxy.port,
        ...(proxy.auth && {
          auth: {
            username: proxy.auth.username,
            password: proxy.auth.password
          }
        })
      }
    };

    return proxyConfig;
  }

  /**
   * Thực hiện request với proxy rotation và retry
   */
  public async requestWithProxyRotation<T>(
    requestFn: (config: AxiosRequestConfig) => Promise<T>,
    baseConfig: AxiosRequestConfig = {},
    maxRetries = 3
  ): Promise<T> {
    let lastError: any;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const proxyConfig = this.createProxyConfig(baseConfig);
        const result = await requestFn(proxyConfig);

        // Request thành công
        return result;
      } catch (error) {
        lastError = error;
        this.logger.warn(`Request attempt ${attempt + 1} failed: ${error.message}`);

        // Nếu có proxy được sử dụng và lỗi có thể do proxy
        if (baseConfig.proxy) {
          this.reportProxyFailure(baseConfig.proxy as any);
        }

        // Chờ một chút trước khi retry
        if (attempt < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
        }
      }
    }

    throw lastError;
  }

  /**
   * Lấy thống kê proxy
   */
  public getProxyStats(): any {
    const activeProxies = this.proxies.filter(p => p.isActive && p.failureCount < p.maxFailures);
    const failedProxies = this.proxies.filter(p => p.failureCount >= p.maxFailures);

    return {
      total: this.proxies.length,
      active: activeProxies.length,
      failed: failedProxies.length,
      currentIndex: this.currentProxyIndex,
      proxies: this.proxies.map(p => ({
        host: p.host,
        port: p.port,
        isActive: p.isActive,
        failureCount: p.failureCount,
        lastUsed: p.lastUsed
      }))
    };
  }

  /**
   * Reset tất cả proxy failures
   */
  public resetAllProxies(): void {
    this.proxies.forEach(proxy => {
      proxy.failureCount = 0;
      proxy.isActive = true;
    });
    this.logger.log('All proxies have been reset');
  }

  /**
   * Thêm proxy mới
   */
  public addProxy(proxyConfig: Omit<ProxyConfig, 'isActive' | 'failureCount' | 'maxFailures'>): void {
    this.proxies.push({
      ...proxyConfig,
      isActive: true,
      failureCount: 0,
      maxFailures: this.MAX_FAILURES_PER_PROXY
    });
    this.logger.log(`Added new proxy: ${proxyConfig.host}:${proxyConfig.port}`);
  }

  /**
   * Xóa proxy
   */
  public removeProxy(host: string, port: number): void {
    const index = this.proxies.findIndex(p => p.host === host && p.port === port);
    if (index !== -1) {
      this.proxies.splice(index, 1);
      this.logger.log(`Removed proxy: ${host}:${port}`);

      // Điều chỉnh currentProxyIndex nếu cần
      if (this.currentProxyIndex >= this.proxies.length) {
        this.currentProxyIndex = 0;
      }
    }
  }
}
