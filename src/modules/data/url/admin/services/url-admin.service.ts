import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Url } from '../../entities/url.entity';
import { CrawlSession } from '../../entities/crawl-session.entity';
import { UrlRepository } from '../../repositories/url.repository';
import { CrawlSessionRepository } from '../../repositories/crawl-session.repository';
import { PaginatedResult } from '@common/response/api-response-dto';
import { FindAllUrlAdminDto } from '../dto/find-all-url-admin.dto';
import { CreateUrlAdminDto } from '../dto/create-url-admin.dto';
import { UpdateUrlAdminDto } from '../dto/update-url-admin.dto';
import { OwnerType } from '../../constants/owner-type.enum';
import {
  CrawlAdminDto,
  StartCrawlWithTrackingAdminDto,
  CrawlProgressAdminResponseDto,
  CrawlSessionListAdminResponseDto,
  StartCrawlAdminResponseDto
} from '../dto/crawl-admin.dto';
import { URL_ERROR_CODES } from '../../exceptions';
import { AppException } from '@/common';
import { QueueService } from '@shared/queue';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bull';
import { QueueName } from '@shared/queue/queue.constants';

/**
 * Service quản lý session crawl cho Admin
 * Tương tự như UrlUserService nhưng dành cho admin
 */
@Injectable()
export class UrlAdminService {
  private readonly logger = new Logger(UrlAdminService.name);

  constructor(
    @InjectRepository(Url)
    private readonly urlRepository: Repository<Url>,
    @InjectRepository(CrawlSession)
    private readonly crawlSessionRepository: Repository<CrawlSession>,
    private readonly urlCustomRepository: UrlRepository,
    private readonly crawlSessionCustomRepository: CrawlSessionRepository,
    private readonly queueService: QueueService,
    @InjectQueue(QueueName.CRAWL_URL_ADMIN)
    private readonly crawlAdminQueue: Queue,
  ) {}

  /**
   * Bắt đầu crawl với tracking session cho admin
   * @param employeeId ID của admin
   * @param crawlDto Thông tin crawl
   * @returns Session ID và Job ID
   */
  async startCrawlWithTracking(employeeId: number, crawlDto: StartCrawlWithTrackingAdminDto): Promise<StartCrawlAdminResponseDto> {
    try {
      this.logger.log(`Bắt đầu admin crawl với tracking cho employee ${employeeId}, URL: ${crawlDto.url}`);

      // Tạo session ID duy nhất cho admin
      const timestamp = Date.now();
      const sessionId = `crawl_admin_${employeeId}_${timestamp}`;

      // Tạo CrawlSession cho admin
      const crawlSession = new CrawlSession();
      crawlSession.id = sessionId;
      crawlSession.userId = employeeId; // Sử dụng employeeId như userId
      crawlSession.url = crawlDto.url;
      crawlSession.status = 'running';
      crawlSession.config = {
        depth: crawlDto.depth,
        maxUrls: crawlDto.maxUrls || 0,
        ignoreRobotsTxt: crawlDto.ignoreRobotsTxt || false,
      };
      crawlSession.progress = {
        totalUrls: 0,
        processedUrls: 0,
        successfulUrls: 0,
        failedUrls: 0,
        currentDepth: 0,
        percentage: 0,
      };
      crawlSession.startTime = timestamp;
      crawlSession.metadata = {
        source: 'admin',
        employeeId: employeeId,
      };

      // Lưu session vào database
      await this.crawlSessionRepository.save(crawlSession);

      // Enqueue job với sessionId cho admin
      const jobId = await this.queueService.addCrawlUrlAdminJob({
        employeeId,
        crawlDto: {
          url: crawlDto.url,
          depth: crawlDto.depth,
          ignoreRobotsTxt: crawlDto.ignoreRobotsTxt,
          maxUrls: crawlDto.maxUrls,
        },
        sessionId, // Thêm sessionId vào job data
      });

      // Cập nhật jobId vào session
      await this.crawlSessionRepository.update(sessionId, { jobId });

      this.logger.log(`Đã tạo admin crawl session ${sessionId} và job ${jobId}`);

      return {
        sessionId,
        jobId,
        message: 'Tiếp nhận yêu cầu crawl admin. Sử dụng sessionId để theo dõi tiến độ.',
      };
    } catch (error) {
      this.logger.error(`Lỗi khi bắt đầu admin crawl với tracking: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_CRAWL_FAILED,
        `Không thể bắt đầu admin crawl: ${error.message}`,
      );
    }
  }

  /**
   * Lấy tiến độ crawl theo session ID cho admin
   * @param employeeId ID của admin
   * @param sessionId ID của session
   * @returns Thông tin tiến độ crawl
   */
  async getCrawlProgress(employeeId: number, sessionId: string): Promise<CrawlProgressAdminResponseDto> {
    try {
      this.logger.debug(`Lấy tiến độ admin crawl cho session ${sessionId} của employee ${employeeId}`);

      const session = await this.crawlSessionCustomRepository.findSessionById(sessionId);

      if (!session) {
        throw new AppException(
          URL_ERROR_CODES.URL_NOT_FOUND,
          'Không tìm thấy session crawl',
        );
      }

      // Kiểm tra quyền truy cập (admin có thể xem tất cả hoặc chỉ của mình)
      // Tùy vào business logic, có thể bỏ qua check này cho admin
      if (session.userId !== employeeId && session.metadata?.source !== 'admin') {
        throw new AppException(
          URL_ERROR_CODES.URL_ACCESS_DENIED,
          'Bạn không có quyền truy cập session này',
        );
      }

      return {
        sessionId: session.id,
        url: session.url,
        status: session.status,
        progress: session.progress,
        config: session.config,
        result: session.result,
        errors: session.errors,
        startTime: session.startTime,
        endTime: session.endTime,
        createdAt: session.createdAt,
        updatedAt: session.updatedAt,
        metadata: session.metadata,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi lấy tiến độ admin crawl: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_FETCH_FAILED,
        `Không thể lấy tiến độ admin crawl: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách sessions crawl của admin
   * @param employeeId ID của admin
   * @param page Trang hiện tại
   * @param limit Số lượng items mỗi trang
   * @param status Lọc theo trạng thái (optional)
   * @returns Danh sách sessions với phân trang
   */
  async getAdminCrawlSessions(
    employeeId: number,
    page: number = 1,
    limit: number = 20,
    status?: string,
  ): Promise<CrawlSessionListAdminResponseDto> {
    try {
      this.logger.debug(`Lấy danh sách admin sessions cho employee ${employeeId}, page ${page}, limit ${limit}`);

      // Admin có thể xem tất cả sessions hoặc chỉ của mình
      // Tùy vào business logic
      const { sessions, total } = await this.crawlSessionCustomRepository.findUserSessions(
        employeeId,
        page,
        limit,
        status,
      );

      const totalPages = Math.ceil(total / limit);

      const sessionItems = sessions.map(session => ({
        sessionId: session.id,
        url: session.url,
        status: session.status,
        percentage: session.progress?.percentage || 0,
        processedUrls: session.progress?.processedUrls || 0,
        totalUrls: session.progress?.totalUrls || 0,
        createdAt: session.createdAt,
        endTime: session.endTime,
        message: session.result?.message,
      }));

      return {
        sessions: sessionItems,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách admin sessions: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_FETCH_FAILED,
        `Không thể lấy danh sách admin sessions: ${error.message}`,
      );
    }
  }

  /**
   * Hủy session crawl đang chạy cho admin
   * @param employeeId ID của admin
   * @param sessionId ID của session
   * @returns true nếu hủy thành công
   */
  async cancelCrawlSession(employeeId: number, sessionId: string): Promise<boolean> {
    try {
      this.logger.log(`Hủy admin crawl session ${sessionId} cho employee ${employeeId}`);

      const session = await this.crawlSessionCustomRepository.findSessionById(sessionId);

      if (!session) {
        throw new AppException(
          URL_ERROR_CODES.URL_NOT_FOUND,
          'Không tìm thấy session crawl',
        );
      }

      // Kiểm tra quyền truy cập (admin có thể hủy tất cả hoặc chỉ của mình)
      if (session.userId !== employeeId && session.metadata?.source !== 'admin') {
        throw new AppException(
          URL_ERROR_CODES.URL_ACCESS_DENIED,
          'Bạn không có quyền hủy session này',
        );
      }

      // Chỉ có thể hủy session đang chạy
      if (session.status !== 'running') {
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_PARAMS,
          'Chỉ có thể hủy session đang chạy',
        );
      }

      // Hủy job trong Bull queue nếu có jobId
      let jobCancelled = false;
      if (session.jobId) {
        try {
          this.logger.log(`🛑 Đang hủy admin job ${session.jobId} trong Bull queue...`);

          // Tìm job trong queue
          const job = await this.crawlAdminQueue.getJob(session.jobId);
          if (job) {
            const jobState = await job.getState();
            this.logger.log(`📊 Admin Job ${session.jobId} state: ${jobState}`);

            if (jobState === 'waiting' || jobState === 'delayed') {
              // Job chưa chạy, có thể remove
              await job.remove();
              jobCancelled = true;
              this.logger.log(`✅ Đã remove admin job ${session.jobId} khỏi queue`);
            } else if (jobState === 'active') {
              // Job đang chạy, cập nhật session để worker biết cần dừng
              this.logger.log(`🛑 Admin Job ${session.jobId} đang active, cập nhật session để báo hiệu dừng`);

              // Cập nhật session status thành cancelled ngay lập tức
              await this.crawlSessionCustomRepository.updateSessionResult(
                sessionId,
                'cancelled',
                {
                  urlsProcessed: session.progress?.processedUrls || 0,
                  urlsSaved: 0,
                  message: 'Session đã bị hủy bởi admin',
                },
                Date.now(),
              );

              jobCancelled = true;
              this.logger.log(`✅ Đã cập nhật admin session ${sessionId} thành cancelled để worker dừng`);
            } else {
              this.logger.warn(`⚠️ Admin Job ${session.jobId} ở trạng thái ${jobState}, không thể hủy`);
            }
          } else {
            this.logger.warn(`⚠️ Không tìm thấy admin job ${session.jobId} trong queue`);
          }
        } catch (jobError) {
          this.logger.error(`❌ Lỗi khi hủy admin job ${session.jobId}: ${jobError.message}`);
        }
      }

      // ✅ Đếm số URLs thực tế đã lưu vào database cho admin này
      let actualUrlsSaved = 0;
      try {
        // Đếm URLs đã lưu trong khoảng thời gian crawl
        const startTime = session.startTime || Date.now();
        const endTime = Date.now();
        actualUrlsSaved = await this.urlCustomRepository.countUrlsByUserAndTimeRange(
          employeeId,
          startTime,
          endTime
        );
        this.logger.log(`📊 Admin đã lưu thực tế ${actualUrlsSaved} URLs vào database cho session ${sessionId}`);
      } catch (countError) {
        this.logger.warn(`⚠️ Không thể đếm admin URLs đã lưu: ${countError.message}`);
        // Fallback: sử dụng giá trị từ result hiện tại nếu có
        actualUrlsSaved = session.result?.urlsSaved || 0;
      }

      // ✅ Sử dụng kết quả thực tế thay vì progress
      const cancelResult = {
        urlsProcessed: session.progress?.processedUrls || 0,
        urlsSaved: actualUrlsSaved, // ✅ Sử dụng số thực tế đã lưu
        message: jobCancelled
          ? `Admin session và job đã bị hủy. Đã lưu ${actualUrlsSaved} URLs vào database.`
          : `Admin session đã bị hủy (job có thể đã hoàn thành). Đã lưu ${actualUrlsSaved} URLs vào database.`,
      };

      // Cập nhật trạng thái session
      const success = await this.crawlSessionCustomRepository.updateSessionResult(
        sessionId,
        'cancelled',
        cancelResult,
        Date.now(),
      );

      if (success) {
        this.logger.log(`✅ Đã hủy admin crawl session ${sessionId} (Job cancelled: ${jobCancelled})`);
      }

      return success;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi hủy admin crawl session: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_PROCESSING_ERROR,
        `Không thể hủy admin session: ${error.message}`,
      );
    }
  }

  // ===== CRUD METHODS =====

  /**
   * Lấy danh sách URL với phân trang và tìm kiếm
   * @param queryParams Tham số tìm kiếm và phân trang
   * @returns Danh sách URL với phân trang
   */
  async findAllUrls(queryParams: FindAllUrlAdminDto): Promise<PaginatedResult<Url>> {
    try {
      this.logger.debug(`Finding all URLs with params: ${JSON.stringify(queryParams)}`);

      const result = await this.urlCustomRepository.findUrlsByOwner(null, queryParams);

      this.logger.debug(`Found ${result.items.length} URLs`);
      return result;
    } catch (error) {
      this.logger.error(`Error finding URLs: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_FETCH_FAILED,
        `Không thể lấy danh sách URL: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết URL theo ID
   * @param id ID của URL
   * @returns Thông tin URL
   */
  async findUrlById(id: string): Promise<Url> {
    try {
      this.logger.debug(`Finding URL with ID: ${id}`);

      const url = await this.urlCustomRepository.findUrlById(id);

      if (!url) {
        throw new AppException(
          URL_ERROR_CODES.URL_NOT_FOUND,
          'Không tìm thấy URL',
        );
      }

      this.logger.debug(`URL found: ${url.url}`);
      return url;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error finding URL by ID: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_FETCH_FAILED,
        `Không thể lấy thông tin URL: ${error.message}`,
      );
    }
  }

  /**
   * Tạo URL mới
   * @param employeeId ID của admin tạo URL
   * @param createUrlDto Thông tin URL cần tạo
   * @returns URL đã tạo
   */
  async createUrl(employeeId: number, createUrlDto: CreateUrlAdminDto): Promise<Url> {
    try {
      this.logger.debug(`Creating new URL: ${createUrlDto.url} by admin: ${employeeId}`);

      // Kiểm tra URL đã tồn tại chưa
      const existingUrl = await this.urlRepository.findOne({
        where: { url: createUrlDto.url }
      });

      if (existingUrl) {
        throw new AppException(
          URL_ERROR_CODES.URL_ALREADY_EXISTS,
          'URL này đã tồn tại trong hệ thống',
        );
      }

      // Tạo URL mới với employeeId làm owner và set ownedByEnum = ADMIN
      const urlData = {
        ...createUrlDto,
        ownedBy: employeeId,
        ownedByEnum: createUrlDto.ownedByEnum || OwnerType.ADMIN,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      const newUrl = this.urlRepository.create(urlData);
      const savedUrl = await this.urlRepository.save(newUrl);

      this.logger.log(`Created new URL with ID: ${savedUrl.id} by admin: ${employeeId}`);
      return savedUrl;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error creating URL: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_CREATION_FAILED,
        `Không thể tạo URL: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật thông tin URL
   * @param id ID của URL
   * @param updateUrlDto Thông tin cập nhật
   * @param employeeId ID của admin cập nhật URL
   * @returns URL đã cập nhật
   */
  async updateUrl(id: string, updateUrlDto: UpdateUrlAdminDto, employeeId: number): Promise<Url> {
    try {
      this.logger.debug(`Updating URL with ID: ${id} by admin: ${employeeId}`);

      // Kiểm tra URL tồn tại
      const existingUrl = await this.findUrlById(id);

      // Kiểm tra URL mới có trùng không (nếu có thay đổi URL)
      if (updateUrlDto.url && updateUrlDto.url !== existingUrl.url) {
        const duplicateUrl = await this.urlRepository.findOne({
          where: { url: updateUrlDto.url }
        });

        if (duplicateUrl) {
          throw new AppException(
            URL_ERROR_CODES.URL_ALREADY_EXISTS,
            'URL này đã tồn tại trong hệ thống',
          );
        }
      }

      // Cập nhật URL với thông tin mới và gán admin làm owner
      const updateData: any = {
        ...updateUrlDto,
        ownedBy: employeeId,
        updatedAt: Date.now(),
      };

      // Nếu có cập nhật ownedByEnum, sử dụng giá trị đó, nếu không giữ nguyên
      if (updateUrlDto.ownedByEnum !== undefined) {
        updateData.ownedByEnum = updateUrlDto.ownedByEnum;
      }

      Object.assign(existingUrl, updateData);
      const updatedUrl = await this.urlRepository.save(existingUrl);

      this.logger.log(`Updated URL with ID: ${id} by admin: ${employeeId}`);
      return updatedUrl;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error updating URL: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_UPDATE_FAILED,
        `Không thể cập nhật URL: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều URL
   * @param ids Danh sách ID cần xóa
   */
  async deleteUrls(ids: string[]): Promise<void> {
    try {
      this.logger.debug(`Deleting URLs with IDs: ${ids.join(', ')}`);

      // Kiểm tra tất cả URLs tồn tại
      const urls = await this.urlRepository.find({
        where: { id: In(ids) }
      });

      if (urls.length !== ids.length) {
        const foundIds = urls.map(url => url.id);
        const missingIds = ids.filter(id => !foundIds.includes(id));
        throw new AppException(
          URL_ERROR_CODES.URL_NOT_FOUND,
          `Không tìm thấy URL với ID: ${missingIds.join(', ')}`,
        );
      }

      // Xóa URLs
      await this.urlRepository.remove(urls);

      this.logger.log(`Deleted ${ids.length} URLs`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error deleting URLs: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_DELETE_FAILED,
        `Không thể xóa URL: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật trạng thái kích hoạt của URL
   * @param id ID của URL
   * @param isActive Trạng thái mới
   * @returns URL đã cập nhật
   */
  async updateUrlStatus(id: string, isActive: boolean): Promise<Url> {
    try {
      this.logger.debug(`Updating URL status with ID: ${id}, isActive: ${isActive}`);

      const url = await this.findUrlById(id);
      url.isActive = isActive;

      const updatedUrl = await this.urlRepository.save(url);

      this.logger.log(`Updated URL status with ID: ${id} to ${isActive}`);
      return updatedUrl;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error updating URL status: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_UPDATE_FAILED,
        `Không thể cập nhật trạng thái URL: ${error.message}`,
      );
    }
  }

  /**
   * Đảo ngược trạng thái kích hoạt của URL
   * @param id ID của URL
   * @returns URL đã cập nhật
   */
  async toggleUrlStatus(id: string): Promise<Url> {
    try {
      this.logger.debug(`Toggling URL status with ID: ${id}`);

      const url = await this.findUrlById(id);
      url.isActive = !url.isActive;

      const updatedUrl = await this.urlRepository.save(url);

      this.logger.log(`Toggled URL status with ID: ${id} to ${url.isActive}`);
      return updatedUrl;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error toggling URL status: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_UPDATE_FAILED,
        `Không thể đảo ngược trạng thái URL: ${error.message}`,
      );
    }
  }

  /**
   * Crawl URL (Legacy method - sẽ deprecated)
   * @param employeeId ID của admin
   * @param crawlDto Thông tin crawl
   * @returns Kết quả crawl
   */
  async crawlUrl(employeeId: number, crawlDto: CrawlAdminDto): Promise<any> {
    try {
      this.logger.log(`Legacy admin crawl URL: ${crawlDto.url}`);

      // Tạo session tạm thời cho legacy crawl
      const result = await this.startCrawlWithTracking(employeeId, crawlDto);

      // Return format tương thích với legacy
      return {
        status: 'success',
        message: `Legacy crawl started with session: ${result.sessionId}`,
        sessionId: result.sessionId,
        jobId: result.jobId,
        urlsProcessed: 0,
        urlsSaved: 0,
      };
    } catch (error) {
      this.logger.error(`Legacy admin crawl failed: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_CRAWL_FAILED,
        `Không thể crawl URL: ${error.message}`,
      );
    }
  }
}
