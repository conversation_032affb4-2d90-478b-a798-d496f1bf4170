// <PERSON><PERSON><PERSON> nghĩa các interface thay vì class để tránh trùng lặp
interface SortDirectionType2 {
  ASC: string;
  DESC: string;
}

interface FindAllUrlAdminDtoType2 {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: string;
  keyword?: string;
  type?: string;
  tags?: string[];
  userId?: number;
  isActive?: boolean;
}

// Khởi tạo các đối tượng từ interface
const SortDirectionEnum2: SortDirectionType2 = {
  ASC: 'ASC',
  DESC: 'DESC'
};

// Mock UrlRepository class
class UrlRepositoryTest2 {
  findUrlById = jest.fn();
  findUrlsByOwner = jest.fn();
  searchUrls = jest.fn();
}

// Mock UrlAdminService
class UrlAdminServiceTest2 {
  constructor(
    private readonly urlRepository: any,
    private readonly urlCustomRepository: UrlRepositoryTest2
  ) {}

  async findAllUrls(queryParams: FindAllUrlAdminDtoType2) {
    return this.urlCustomRepository.findUrlsByOwner(null, queryParams);
  }

  async searchUrls(keyword: string, limit: number = 10) {
    if (!keyword || keyword.trim() === '') {
      throw new Error('Từ khóa tìm kiếm không được để trống');
    }
    return this.urlCustomRepository.searchUrls(keyword, limit);
  }
}

describe('Kiểm thử tìm kiếm URL của admin', () => {
  let service: UrlAdminServiceTest2;
  let urlCustomRepository: UrlRepositoryTest2;

  const mockUrl = {
    id: 'test-id',
    url: 'https://example.com',
    title: 'Test URL',
    content: 'Test content',
    type: 'web',
    tags: ['test'],
    ownedBy: 1,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    urlEmbedding: null,
    titleEmbedding: null,
    contentEmbedding: null,
    isActive: true,
  };

  const mockUrls = [
    { ...mockUrl },
    {
      ...mockUrl,
      id: 'test-id-2',
      url: 'https://example.com/2',
      title: 'Test URL 2',
      content: 'Test content 2',
      tags: ['test', 'nestjs'],
      isActive: true,
    },
    {
      ...mockUrl,
      id: 'test-id-3',
      url: 'https://example.com/3',
      title: 'NestJS Tutorial',
      content: 'Learn NestJS',
      tags: ['nestjs', 'tutorial'],
      ownedBy: 2,
      isActive: false,
    },
  ];

  const mockPaginatedResult = {
    items: mockUrls,
    meta: {
      totalItems: 3,
      itemCount: 3,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  beforeEach(() => {
    // Tạo repository và service
    const repository = {};
    urlCustomRepository = new UrlRepositoryTest2();
    service = new UrlAdminServiceTest2(repository, urlCustomRepository);

    // Setup mock methods
    urlCustomRepository.findUrlsByOwner.mockResolvedValue(mockPaginatedResult);
    urlCustomRepository.searchUrls.mockResolvedValue(mockUrls);
  });

  describe('findAllUrls - Tìm kiếm URL theo nhiều tiêu chí', () => {
    it('Phải trả về danh sách URL có phân trang', async () => {
      const result = await service.findAllUrls({});
      expect(result).toEqual(mockPaginatedResult);
      expect(urlCustomRepository.findUrlsByOwner).toHaveBeenCalledWith(null, expect.any(Object));
    });

    it('Phải tìm kiếm được URL theo từ khóa', async () => {
      const queryParams: FindAllUrlAdminDtoType2 = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirectionEnum2.DESC,
        keyword: 'test',
      };

      await service.findAllUrls(queryParams);
      expect(urlCustomRepository.findUrlsByOwner).toHaveBeenCalledWith(null, expect.objectContaining({
        keyword: 'test'
      }));
    });

    it('Phải tìm kiếm được URL theo loại', async () => {
      const queryParams: FindAllUrlAdminDtoType2 = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirectionEnum2.DESC,
        type: 'web',
      };

      await service.findAllUrls(queryParams);
      expect(urlCustomRepository.findUrlsByOwner).toHaveBeenCalledWith(null, expect.objectContaining({
        type: 'web'
      }));
    });

    it('Phải tìm kiếm được URL theo thẻ', async () => {
      const queryParams: FindAllUrlAdminDtoType2 = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirectionEnum2.DESC,
        tags: ['nestjs', 'tutorial'],
      };

      await service.findAllUrls(queryParams);
      expect(urlCustomRepository.findUrlsByOwner).toHaveBeenCalledWith(null, expect.objectContaining({
        tags: ['nestjs', 'tutorial']
      }));
    });

    it('Phải tìm kiếm được URL theo ID người dùng', async () => {
      const queryParams: FindAllUrlAdminDtoType2 = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirectionEnum2.DESC,
        userId: 1,
      };

      await service.findAllUrls(queryParams);
      expect(urlCustomRepository.findUrlsByOwner).toHaveBeenCalledWith(null, expect.objectContaining({
        userId: 1
      }));
    });

    it('Phải tìm kiếm được URL theo trạng thái kích hoạt', async () => {
      const queryParams: FindAllUrlAdminDtoType2 = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirectionEnum2.DESC,
        isActive: true,
      };

      await service.findAllUrls(queryParams);
      expect(urlCustomRepository.findUrlsByOwner).toHaveBeenCalledWith(null, expect.objectContaining({
        isActive: true
      }));
    });

    it('Phải tìm kiếm được URL với nhiều tiêu chí kết hợp', async () => {
      const queryParams: FindAllUrlAdminDtoType2 = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirectionEnum2.DESC,
        keyword: 'nestjs',
        type: 'web',
        tags: ['tutorial'],
        userId: 1,
        isActive: true
      };

      await service.findAllUrls(queryParams);
      expect(urlCustomRepository.findUrlsByOwner).toHaveBeenCalledWith(null, expect.objectContaining({
        keyword: 'nestjs',
        type: 'web',
        tags: ['tutorial'],
        userId: 1,
        isActive: true
      }));
    });
  });

  describe('searchUrls - Tìm kiếm nhanh URL theo từ khóa', () => {
    it('Phải trả về danh sách URL phù hợp với từ khóa', async () => {
      const result = await service.searchUrls('test', 10);
      expect(result).toEqual(mockUrls);
      expect(urlCustomRepository.searchUrls).toHaveBeenCalledWith('test', 10);
    });

    it('Phải ném ngoại lệ nếu từ khóa trống', async () => {
      await expect(service.searchUrls('')).rejects.toThrow('Từ khóa tìm kiếm không được để trống');
    });

    it('Phải giới hạn số lượng kết quả trả về', async () => {
      await service.searchUrls('test', 5);
      expect(urlCustomRepository.searchUrls).toHaveBeenCalledWith('test', 5);
    });
  });
});
