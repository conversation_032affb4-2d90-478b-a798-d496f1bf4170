import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber } from 'class-validator';

/**
 * DTO cho việc gán quyền cho vai trò
 */
export class AssignRolePermissionsDto {
  @ApiProperty({
    description: 'Danh sách ID của các quyền',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsNotEmpty({ message: 'Danh sách quyền không được để trống' })
  @IsArray({ message: 'Danh sách quyền phải là mảng' })
  @IsNumber({}, { each: true, message: 'ID quyền phải là số' })
  permissionIds: number[];
}
