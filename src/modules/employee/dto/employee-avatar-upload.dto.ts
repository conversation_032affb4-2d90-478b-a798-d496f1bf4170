import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';

/**
 * Enum cho các loại hình ảnh được chấp nhận
 */
export enum ImageTypeEnum {
  JPEG = 'image/jpeg',
  PNG = 'image/png',
  GIF = 'image/gif',
}

/**
 * Enum cho kích thước tối đa của file
 */
export enum FileSizeEnum {
  ONE_MB = 1048576, // 1MB
  TWO_MB = 2097152, // 2MB
  FIVE_MB = 5242880, // 5MB
}

/**
 * DTO cho việc tạo URL tải lên avatar nhân viên
 */
export class EmployeeAvatarUploadDto {
  @ApiProperty({
    description: 'Loại hình ảnh',
    enum: ImageTypeEnum,
    example: 'image/jpeg',
  })
  @IsNotEmpty({ message: '<PERSON><PERSON><PERSON> hình ảnh không được để trống' })
  @IsEnum(ImageTypeEnum, { message: '<PERSON><PERSON><PERSON> hình ảnh không hợp lệ' })
  imageType: ImageTypeEnum;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> thước tối đa của file (bytes)',
    enum: FileSizeEnum,
    example: FileSizeEnum.TWO_MB,
  })
  @IsNotEmpty({ message: 'Kích thước tối đa không được để trống' })
  @IsEnum(FileSizeEnum, { message: 'Kích thước tối đa không hợp lệ' })
  maxSize: FileSizeEnum;
}

/**
 * DTO cho việc cập nhật avatar nhân viên
 */
export class UpdateEmployeeAvatarDto {
  @ApiProperty({
    description: 'Khóa của avatar trên S3',
    example: 'avatars/employee-1/avatar-1-1620000000000.jpg',
  })
  @IsNotEmpty({ message: 'Khóa avatar không được để trống' })
  avatarKey: string;
}

/**
 * DTO cho response khi tạo URL tải lên avatar
 */
export class AvatarUploadResponseDto {
  @ApiProperty({
    description: 'URL tạm thời để tải lên avatar',
    example: 'https://example.com/presigned-url',
  })
  uploadUrl: string;

  @ApiProperty({
    description: 'Khóa của avatar trên S3',
    example: 'avatars/employee-1/avatar-1-1620000000000.jpg',
  })
  avatarKey: string;

  @ApiProperty({
    description: 'Thời gian hết hạn của URL (giây)',
    example: 300,
  })
  expiresIn: number;
}
