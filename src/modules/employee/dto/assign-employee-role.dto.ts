import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber } from 'class-validator';

/**
 * DTO cho việc gán vai trò cho nhân viên
 */
export class AssignEmployeeRoleDto {
  @ApiProperty({
    description: 'Danh sách ID của các vai trò',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsNotEmpty({ message: 'Danh sách vai trò không được để trống' })
  @IsArray({ message: 'Danh sách vai trò phải là mảng' })
  @IsNumber({}, { each: true, message: 'ID vai trò phải là số' })
  roleIds: number[];
}
