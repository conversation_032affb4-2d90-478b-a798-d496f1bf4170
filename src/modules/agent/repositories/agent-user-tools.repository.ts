import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AgentUserTools } from '@modules/agent/entities/agent-user-tools.entity';
import { UserToolsCustom } from '@modules/tools/entities/user-tools-custom.entity';
import { Transactional } from 'typeorm-transactional';
import { PaginatedResult } from '@common/response';

/**
 * Repository cho AgentUserTools
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến quan hệ giữa agent và tools
 */
@Injectable()
export class AgentUserToolsRepository extends Repository<AgentUserTools> {
  private readonly logger = new Logger(AgentUserToolsRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentUserTools, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AgentUserTools
   * @returns SelectQueryBuilder cho AgentUserTools
   */
  private createBaseQuery(): SelectQueryBuilder<AgentUserTools> {
    return this.createQueryBuilder('agentUserTools');
  }

  /**
   * Lấy danh sách tools của một agent với phân trang
   * @param userAgentId ID của agent
   * @param userId ID của người dùng
   * @param page Trang hiện tại
   * @param limit Số lượng items per page
   * @returns Danh sách tools có phân trang
   */
  async getAgentToolsWithPagination(
    userAgentId: string,
    userId: number,
    page: number = 1,
    limit: number = 10,
  ): Promise<PaginatedResult<UserToolsCustom>> {
    try {
      const queryBuilder = this.dataSource
        .getRepository(UserToolsCustom)
        .createQueryBuilder('tool')
        .innerJoin(
          AgentUserTools,
          'agentTool',
          'agentTool.customToolId = tool.id'
        )
        .where('agentTool.userAgentId = :userAgentId', { userAgentId })
        .andWhere('tool.userId = :userId', { userId })
        .andWhere('tool.active = :active', { active: true })
        .select([
          'tool.id',
          'tool.toolName',
          'tool.toolDescription',
          'tool.endpoint',
          'tool.method',
          'tool.baseUrl',
          'tool.status',
          'tool.active',
          'tool.createdAt',
          'tool.updatedAt'
        ])
        .orderBy('tool.createdAt', 'DESC');

      // Tính offset
      const offset = (page - 1) * limit;
      
      // Lấy tổng số và dữ liệu
      const [items, total] = await queryBuilder
        .skip(offset)
        .take(limit)
        .getManyAndCount();

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(total / limit);

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
          hasItems: items.length > 0,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách tools của agent: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm quan hệ giữa agent và tool
   * @param userAgentId ID của agent
   * @param customToolId ID của tool
   * @returns AgentUserTools nếu tìm thấy, null nếu không tìm thấy
   */
  async findByAgentIdAndToolId(
    userAgentId: string,
    customToolId: string,
  ): Promise<AgentUserTools | null> {
    return this.createBaseQuery()
      .where('agentUserTools.userAgentId = :userAgentId', { userAgentId })
      .andWhere('agentUserTools.customToolId = :customToolId', { customToolId })
      .getOne();
  }

  /**
   * Lấy danh sách existing relationships để check trùng lặp
   * @param userAgentId ID của agent
   * @param customToolIds Danh sách ID của tools cần check
   * @returns Danh sách tool IDs đã tồn tại relationship
   */
  async findExistingRelationships(
    userAgentId: string,
    customToolIds: string[],
  ): Promise<string[]> {
    if (!customToolIds || customToolIds.length === 0) {
      return [];
    }

    try {
      const existingRelations = await this.createBaseQuery()
        .select(['agentUserTools.customToolId'])
        .where('agentUserTools.userAgentId = :userAgentId', { userAgentId })
        .andWhere('agentUserTools.customToolId IN (:...customToolIds)', { customToolIds })
        .getMany();

      return existingRelations.map(rel => rel.customToolId);
    } catch (error) {
      this.logger.error(`Lỗi khi tìm existing relationships: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Bulk insert các relationships mới
   * @param userAgentId ID của agent
   * @param customToolIds Danh sách ID của tools
   * @returns Số lượng relationships đã được tạo
   */
  @Transactional()
  async bulkInsertRelationships(
    userAgentId: string,
    customToolIds: string[],
  ): Promise<number> {
    if (!customToolIds || customToolIds.length === 0) {
      return 0;
    }

    try {
      // Tạo danh sách relationships để insert
      const relationshipsToInsert = customToolIds.map(customToolId => ({
        userAgentId,
        customToolId,
      }));

      const result = await this.createQueryBuilder()
        .insert()
        .into(AgentUserTools)
        .values(relationshipsToInsert)
        .execute();

      const insertedCount = result.identifiers?.length || 0;
      this.logger.debug(`Bulk inserted ${insertedCount} agent-tool relationships for agent ${userAgentId}`);

      return insertedCount;
    } catch (error) {
      this.logger.error(`Lỗi khi bulk insert relationships: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Bulk add tools với check existing relationships
   * @param userAgentId ID của agent
   * @param customToolIds Danh sách ID của tools
   * @returns Object chứa thông tin về số lượng added và skipped
   */
  @Transactional()
  async bulkAddTools(
    userAgentId: string,
    customToolIds: string[],
  ): Promise<{ addedCount: number; skippedCount: number; existingIds: string[] }> {
    if (!customToolIds || customToolIds.length === 0) {
      return { addedCount: 0, skippedCount: 0, existingIds: [] };
    }

    try {
      // 1. Lấy tất cả existing relationships trong 1 query
      const existingToolIds = await this.findExistingRelationships(userAgentId, customToolIds);

      // 2. Tìm các tools chưa có relationship
      const existingToolIdsSet = new Set(existingToolIds);
      const newToolIds = customToolIds.filter(
        id => !existingToolIdsSet.has(id)
      );

      // 3. Bulk insert các relationships mới
      let addedCount = 0;
      if (newToolIds.length > 0) {
        addedCount = await this.bulkInsertRelationships(userAgentId, newToolIds);
      }

      const skippedCount = existingToolIds.length;

      this.logger.debug(
        `Bulk add tools for agent ${userAgentId}: ` +
        `${addedCount} added, ${skippedCount} skipped (already exist)`
      );

      return {
        addedCount,
        skippedCount,
        existingIds: existingToolIds,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi bulk add tools: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Bulk remove tools từ agent
   * @param userAgentId ID của agent
   * @param customToolIds Danh sách ID của tools cần remove
   * @returns Số lượng relationships đã được xóa
   */
  @Transactional()
  async bulkRemoveTools(
    userAgentId: string,
    customToolIds: string[],
  ): Promise<number> {
    if (!customToolIds || customToolIds.length === 0) {
      return 0;
    }

    try {
      const result = await this.createBaseQuery()
        .delete()
        .where('userAgentId = :userAgentId', { userAgentId })
        .andWhere('customToolId IN (:...customToolIds)', { customToolIds })
        .execute();

      const deletedCount = result.affected || 0;
      this.logger.debug(`Bulk removed ${deletedCount} agent-tool relationships for agent ${userAgentId}`);

      return deletedCount;
    } catch (error) {
      this.logger.error(`Lỗi khi bulk remove tools: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Kiểm tra xem agent có tồn tại không
   * @param userAgentId ID của agent
   * @param userId ID của người dùng
   * @returns true nếu agent tồn tại, false nếu không
   */
  async checkAgentExists(userAgentId: string, userId: number): Promise<boolean> {
    try {
      const count = await this.dataSource
        .getRepository('agents_user')
        .createQueryBuilder('agentUser')
        .where('agentUser.id = :userAgentId', { userAgentId })
        .andWhere('agentUser.userId = :userId', { userId })
        .getCount();

      return count > 0;
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra agent tồn tại: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Kiểm tra xem các tools có tồn tại và thuộc về user không
   * @param customToolIds Danh sách ID của tools
   * @param userId ID của người dùng
   * @returns Danh sách tool IDs hợp lệ
   */
  async validateToolsExistence(
    customToolIds: string[],
    userId: number,
  ): Promise<string[]> {
    if (!customToolIds || customToolIds.length === 0) {
      return [];
    }

    try {
      const validTools = await this.dataSource
        .getRepository(UserToolsCustom)
        .createQueryBuilder('tool')
        .select(['tool.id'])
        .where('tool.id IN (:...customToolIds)', { customToolIds })
        .andWhere('tool.userId = :userId', { userId })
        .andWhere('tool.active = :active', { active: true })
        .getMany();

      return validTools.map(tool => tool.id);
    } catch (error) {
      this.logger.error(`Lỗi khi validate tools existence: ${error.message}`, error.stack);
      throw error;
    }
  }
}
