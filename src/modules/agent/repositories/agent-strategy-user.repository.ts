import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { AgentStrategyUser } from '@modules/agent/entities/agents-strategy-user.entity';
import { PaginatedResult } from '@common/response';
import { AppException } from '@common/exceptions';
import { STRATEGY_ERROR_CODES } from '@modules/agent/exceptions/strategy-error.code';
import { QueryAgentStrategyUserDto } from '@modules/agent/user/dto/agent-strategy-user';
import { QueryDto } from '@common/dto';

/**
 * Repository cho AgentStrategyUser
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến strategy user
 */
@Injectable()
export class AgentStrategyUserRepository extends Repository<AgentStrategyUser> {
  private readonly logger = new Logger(AgentStrategyUserRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentStrategyUser, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho AgentStrategyUser
   * @returns SelectQueryBuilder cho AgentStrategyUser
   */
  createBaseQuery(): SelectQueryBuilder<AgentStrategyUser> {
    return this.createQueryBuilder('strategyUser');
  }

  /**
   * Lấy danh sách strategy user theo userId với phân trang
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn và phân trang
   * @returns Danh sách strategy user có phân trang với thông tin strategy gốc
   */
  async findPaginatedByUserId(
    userId: number,
    queryDto: QueryDto,
  ): Promise<PaginatedResult<any>> {
    try {
      const { page, limit, search } = queryDto;

      // Tạo query builder với join để lấy thông tin strategy gốc
      const queryBuilder = this.createQueryBuilder('strategyUser')
        .leftJoin('agents_strategy', 'strategy', 'strategy.id = strategyUser.agents_strategy_id')
        .leftJoin('agents', 'agent', 'agent.id = strategy.id')
        .select([
          'strategyUser.id AS strategy_user_id',
          'strategyUser.agents_strategy_id AS agents_strategy_id',
          'strategyUser.example AS example',
          'strategyUser.user_id AS user_id',
          'strategyUser.owned_at AS owned_at',
          'strategyUser.user_model_id AS user_model_id',
          'strategyUser.key_llm_id AS key_llm_id',
          'strategy.content AS strategy_content',
          'strategy.example_default AS strategy_example_default',
          'agent.name AS strategy_name',
        ])
        .where('strategyUser.user_id = :userId', { userId });

      // Tìm kiếm theo tên strategy
      if (search) {
        queryBuilder.andWhere('agent.name ILIKE :search', { search: `%${search}%` });
      }

      // Sắp xếp theo thời gian sở hữu mới nhất
      queryBuilder.orderBy('strategyUser.owned_at', 'DESC');

      // Thực hiện phân trang
      const offset = (page - 1) * limit;
      const [rawResults, totalItems] = await Promise.all([
        queryBuilder.offset(offset).limit(limit).getRawMany(),
        queryBuilder.getCount(),
      ]);

      // Kiểm tra có dữ liệu không
      const hasItems = totalItems > 0;

      // Transform raw results
      const items = rawResults.map(raw => ({
        id: raw.strategy_user_id,
        agentsStrategyId: raw.agents_strategy_id,
        example: raw.example || [],
        userId: raw.user_id,
        ownedAt: parseInt(raw.owned_at),
        userModelId: raw.user_model_id,
        keyLlmId: raw.key_llm_id,
        strategyName: raw.strategy_name,
        strategyContent: raw.strategy_content || [],
        strategyExampleDefault: raw.strategy_example_default || [],
      }));

      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
          hasItems,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách strategy user cho user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND,
        'Không thể lấy danh sách strategy user'
      );
    }
  }

  /**
   * Tìm strategy user theo ID và userId
   * @param id ID của strategy user
   * @param userId ID của người dùng
   * @returns Strategy user hoặc null
   */
  async findByIdAndUserId(id: string, userId: number): Promise<AgentStrategyUser | null> {
    try {
      return await this.createBaseQuery()
        .where('strategyUser.id = :id', { id })
        .andWhere('strategyUser.userId = :userId', { userId })
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm strategy user ${id} cho user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND,
        'Không thể tìm strategy user'
      );
    }
  }

  /**
   * Cập nhật strategy user
   * @param id ID của strategy user
   * @param userId ID của người dùng
   * @param updateData Dữ liệu cập nhật
   * @returns Strategy user đã cập nhật
   */
  @Transactional()
  async updateByIdAndUserId(
    id: string,
    userId: number,
    updateData: Partial<AgentStrategyUser>,
  ): Promise<AgentStrategyUser> {
    try {
      const result = await this.createQueryBuilder()
        .update(AgentStrategyUser)
        .set(updateData)
        .where('id = :id', { id })
        .andWhere('userId = :userId', { userId })
        .execute();

      if (result.affected === 0) {
        throw new AppException(
          STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND,
          'Không tìm thấy strategy user để cập nhật'
        );
      }

      // Lấy lại dữ liệu đã cập nhật
      const updatedStrategyUser = await this.findByIdAndUserId(id, userId);
      if (!updatedStrategyUser) {
        throw new AppException(
          STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND,
          'Không thể lấy dữ liệu strategy user sau khi cập nhật'
        );
      }

      return updatedStrategyUser;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật strategy user ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy thông tin strategy user với thông tin strategy gốc
   * @param id ID của strategy user
   * @param userId ID của người dùng
   * @returns Thông tin đầy đủ của strategy user
   */
  async findDetailByIdAndUserId(id: string, userId: number): Promise<any> {
    try {
      const rawResult = await this.createQueryBuilder('strategyUser')
        .leftJoin('agents_strategy', 'strategy', 'strategy.id = strategyUser.agents_strategy_id')
        .leftJoin('agents', 'agent', 'agent.id = strategy.id')
        .select([
          'strategyUser.id AS strategy_user_id',
          'strategyUser.agents_strategy_id AS agents_strategy_id',
          'strategyUser.example AS example',
          'strategyUser.user_id AS user_id',
          'strategyUser.owned_at AS owned_at',
          'strategyUser.user_model_id AS user_model_id',
          'strategyUser.key_llm_id AS key_llm_id',
          'strategy.content AS strategy_content',
          'strategy.example_default AS strategy_example_default',
          'agent.name AS strategy_name',
        ])
        .where('strategyUser.id = :id', { id })
        .andWhere('strategyUser.user_id = :userId', { userId })
        .getRawOne();

      if (!rawResult) {
        return null;
      }

      return {
        id: rawResult.strategy_user_id,
        agentsStrategyId: rawResult.agents_strategy_id,
        example: rawResult.example || [],
        userId: rawResult.user_id,
        ownedAt: parseInt(rawResult.owned_at),
        userModelId: rawResult.user_model_id,
        keyLlmId: rawResult.key_llm_id,
        strategyName: rawResult.strategy_name,
        strategyContent: rawResult.strategy_content || [],
        strategyExampleDefault: rawResult.strategy_example_default || [],
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết strategy user ${id}: ${error.message}`, error.stack);
      throw new AppException(
        STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND,
        'Không thể lấy chi tiết strategy user'
      );
    }
  }
}
