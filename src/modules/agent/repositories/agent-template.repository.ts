import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AgentTemplate } from '@modules/agent/entities';
import { PaginatedResult } from '@/common/response';
import { AgentTemplateStatus } from '@modules/agent/constants';

/**
 * Repository cho AgentTemplate
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến mẫu agent
 */
@Injectable()
export class AgentTemplateRepository extends Repository<AgentTemplate> {
  private readonly logger = new Logger(AgentTemplateRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentTemplate, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AgentTemplate
   * @returns SelectQueryBuilder cho AgentTemplate
   */
  private createBaseQuery(): SelectQueryBuilder<AgentTemplate> {
    return this.createQueryBuilder('agentTemplate');
  }

  /**
   * Tìm mẫu agent theo ID
   * @param id ID của mẫu agent
   * @returns AgentTemplate nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: string): Promise<AgentTemplate | null> {
    return this.createBaseQuery()
      .where('agentTemplate.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm mẫu agent theo ID loại agent
   * @param typeId ID của loại agent
   * @returns Danh sách mẫu agent
   */
  async findByTypeId(typeId: number): Promise<AgentTemplate[]> {
    return this.createBaseQuery()
      .where('agentTemplate.typeId = :typeId', { typeId })
      .getMany();
  }

  /**
   * Lấy danh sách mẫu agent với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tìm theo tên agent)
   * @param status Trạng thái của mẫu agent
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách mẫu agent với phân trang
   */
  async findPaginated(
    page: number,
    limit: number,
    search?: string,
    status?: AgentTemplateStatus,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<AgentTemplate>> {
    // Tạo query builder cho AgentTemplate
    const qb = this.createBaseQuery();

    // Thêm điều kiện lọc theo trạng thái nếu có
    if (status) {
      qb.andWhere('agentTemplate.status = :status', { status });
    }

    // Thêm điều kiện chỉ lấy các agent template không bị xóa (deletedBy IS NULL)
    qb.andWhere('agentTemplate.deleted_by IS NULL');

    // Lấy danh sách ID của các mẫu agent
    const agentIds = await qb.select('agentTemplate.id').getRawMany();

    // Nếu không có mẫu agent nào, trả về kết quả trống
    if (agentIds.length === 0) {
      return {
        items: [],
        meta: {
          totalItems: 0,
          itemCount: 0,
          itemsPerPage: limit,
          totalPages: 0,
          currentPage: page,
        },
      };
    }

    // Lấy danh sách ID
    const ids = agentIds.map(item => item.agentTemplate_id);

    // Tạo query builder cho Agent để lấy thông tin chi tiết
    const agentQb = this.dataSource.createQueryBuilder()
      .select('agent')
      .from('agents', 'agent')
      .where('agent.id IN (:...ids)', { ids });

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      agentQb.andWhere('agent.name ILIKE :search', { search: `%${search}%` });
    }

    // Thêm điều kiện chỉ lấy các agent không bị xóa mềm
    agentQb.andWhere('agent.deleted_at IS NULL');

    // Đếm tổng số lượng
    const total = await agentQb.getCount();

    // Thêm phân trang và sắp xếp
    agentQb.orderBy(`agent.${sortBy}`, sortDirection)
      .offset((page - 1) * limit)
      .limit(limit);

    // Lấy danh sách agent
    const agents = await agentQb.getRawMany();

    // Lấy danh sách mẫu agent tương ứng
    const templateIds = agents.map(agent => agent.agent_id);

    // Kiểm tra nếu không có templateIds nào, trả về mảng trống
    let templates: AgentTemplate[] = [];
    if (templateIds.length > 0) {
      templates = await this.createBaseQuery()
        .where('agentTemplate.id IN (:...templateIds)', { templateIds })
        .getMany();
    }

    return {
      items: templates,
      meta: {
        totalItems: total,
        itemCount: templates.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Cập nhật trạng thái của mẫu agent
   * @param id ID của mẫu agent
   * @param status Trạng thái mới
   * @param updatedBy ID của nhân viên cập nhật
   * @returns Số lượng bản ghi đã cập nhật
   */
  async updateStatus(id: string, status: AgentTemplateStatus, updatedBy?: number): Promise<number> {
    const updateData: Partial<AgentTemplate> = { status };

    if (updatedBy) {
      updateData.updatedBy = updatedBy;
    }

    const result = await this.update(id, updateData);
    return result.affected || 0;
  }

  /**
   * Tìm mẫu agent đã xóa theo ID
   * @param id ID của mẫu agent
   * @returns AgentTemplate nếu tìm thấy, null nếu không tìm thấy
   */
  async findDeletedById(id: string): Promise<AgentTemplate | null> {
    return this.createBaseQuery()
      .where('agentTemplate.id = :id', { id })
      .andWhere('agentTemplate.deleted_by IS NOT NULL')
      .getOne();
  }

  /**
   * Khôi phục mẫu agent đã xóa
   * @param id ID của mẫu agent
   * @param updatedBy ID của nhân viên khôi phục
   * @returns Số lượng bản ghi đã được khôi phục
   */
  async restoreTemplate(id: string, updatedBy: number): Promise<number> {
    const updateData: Partial<AgentTemplate> = {
      deletedBy: null,
      updatedBy,
    };

    const result = await this.update(id, updateData);
    return result.affected || 0;
  }

  /**
   * Khôi phục nhiều mẫu agent đã xóa
   * @param ids Danh sách ID của các mẫu agent
   * @param updatedBy ID của nhân viên khôi phục
   * @returns Số lượng bản ghi đã được khôi phục
   */
  async restoreMany(ids: string[], updatedBy: number): Promise<number> {
    if (!ids || ids.length === 0) {
      return 0;
    }

    const result = await this.createQueryBuilder()
      .update(AgentTemplate)
      .set({
        deletedBy: null,
        updatedBy,
      })
      .where('id IN (:...ids)', { ids })
      .andWhere('deleted_by IS NOT NULL')
      .execute();

    return result.affected || 0;
  }

  /**
   * Lấy danh sách mẫu agent đã xóa với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tìm theo tên agent)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách mẫu agent đã xóa với phân trang
   */
  async findDeletedPaginated(
    page: number,
    limit: number,
    search?: string,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<AgentTemplate>> {
    // Tạo query builder cho AgentTemplate đã xóa
    const qb = this.createBaseQuery();

    // Thêm điều kiện chỉ lấy các agent template đã bị xóa (deletedBy IS NOT NULL)
    qb.andWhere('agentTemplate.deleted_by IS NOT NULL');

    // Lấy danh sách ID của các mẫu agent đã xóa
    const agentIds = await qb.select('agentTemplate.id').getRawMany();

    // Nếu không có mẫu agent nào đã xóa, trả về kết quả trống
    if (agentIds.length === 0) {
      return {
        items: [],
        meta: {
          totalItems: 0,
          itemCount: 0,
          itemsPerPage: limit,
          totalPages: 0,
          currentPage: page,
        },
      };
    }

    // Lấy danh sách ID
    const ids = agentIds.map(item => item.agentTemplate_id);

    // Tạo query builder cho Agent để lấy thông tin chi tiết
    const agentQb = this.dataSource.createQueryBuilder()
      .select('agent')
      .from('agents', 'agent')
      .where('agent.id IN (:...ids)', { ids });

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      agentQb.andWhere('agent.name ILIKE :search', { search: `%${search}%` });
    }

    // Thêm điều kiện chỉ lấy các agent đã bị xóa mềm
    agentQb.andWhere('agent.deleted_at IS NOT NULL');

    // Đếm tổng số lượng
    const total = await agentQb.getCount();

    // Thêm phân trang và sắp xếp
    agentQb.orderBy(`agent.${sortBy}`, sortDirection)
      .offset((page - 1) * limit)
      .limit(limit);

    // Lấy danh sách agent
    const agents = await agentQb.getRawMany();

    // Lấy danh sách mẫu agent tương ứng
    const templateIds = agents.map(agent => agent.agent_id);

    // Kiểm tra nếu không có templateIds nào, trả về mảng trống
    let templates: AgentTemplate[] = [];
    if (templateIds.length > 0) {
      templates = await this.createBaseQuery()
        .where('agentTemplate.id IN (:...templateIds)', { templateIds })
        .getMany();
    }

    return {
      items: templates,
      meta: {
        totalItems: total,
        itemCount: templates.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }
}
