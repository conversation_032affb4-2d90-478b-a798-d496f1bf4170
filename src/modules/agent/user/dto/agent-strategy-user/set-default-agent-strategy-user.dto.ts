import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { IStrategyContentStep } from '@modules/agent/interfaces/strategy-content-step.interface';

/**
 * DTO cho việc set dữ liệu default cho agent strategy user
 */
export class SetDefaultAgentStrategyUserDto {
  /**
   * Danh sách ví dụ (example) mặc định sẽ được set cho strategy user
   */
  @ApiProperty({
    description: 'Danh sách ví dụ (example) mặc định sẽ được set cho strategy user',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Ví dụ mặc định: Khi người dùng hỏi về sản phẩm' }
      }
    },
    example: [
      { stepOrder: 1, content: 'Ví dụ mặc định: Khi người dùng hỏi về sản phẩm' },
      { stepOrder: 2, content: 'Ví dụ mặc định: <PERSON>hi người dùng cần hỗ trợ thanh toán' }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object)
  @IsNotEmpty()
  example: IStrategyContentStep[];
}
