import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID, ArrayMinSize, ArrayMaxSize, ArrayUnique } from 'class-validator';

/**
 * DTO cho việc gỡ bỏ multi-agent khỏi agent cha
 */
export class RemoveMultiAgentDto {
  /**
   * <PERSON>h sách ID của các agent con cần gỡ bỏ
   */
  @ApiProperty({
    description: 'Danh sách ID của các agent con cần gỡ bỏ (tối thiểu 1, tối đa 20)',
    type: [String],
    example: [
      '550e8400-e29b-41d4-a716-************',
      '550e8400-e29b-41d4-a716-************'
    ],
    minItems: 1,
    maxItems: 20,
  })
  @IsArray({ message: 'childAgentIds phải là một mảng' })
  @ArrayMinSize(1, { message: '<PERSON><PERSON><PERSON> có ít nhất 1 agent con' })
  @ArrayMaxSize(20, { message: '<PERSON>h<PERSON><PERSON> được vượt quá 20 agent con' })
  @ArrayUnique({ message: '<PERSON>hông được có agent ID trùng lặp' })
  @IsUUID('4', { each: true, message: 'Mỗi agent ID phải là UUID hợp lệ' })
  childAgentIds: string[];
}
