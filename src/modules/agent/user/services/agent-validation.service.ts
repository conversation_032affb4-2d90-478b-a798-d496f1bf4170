import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AgentUserRepository, TypeAgentRepository } from '@modules/agent/repositories';
import { TypeAgentConfig } from '@modules/agent/interfaces/type-agent-config.interface';

/**
 * Service xử lý validation cho Agent và TypeAgent features
 */
@Injectable()
export class AgentValidationService {
  private readonly logger = new Logger(AgentValidationService.name);

  constructor(
    private readonly agentUserRepository: AgentUserRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
  ) {}

  /**
   * Validate agent ownership và TypeAgent feature
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param requiredFeature Feature cần kiểm tra trong TypeAgent config
   */
  async validateAgentAndFeature(
    agentId: string,
    userId: number,
    requiredFeature: keyof TypeAgentConfig,
  ): Promise<void> {
    // 1. Kiểm tra agent ownership
    const agentData = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);
    
    if (!agentData) {
      this.logger.warn(`Agent ${agentId} không tồn tại hoặc không thuộc về user ${userId}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    // 2. Lấy TypeAgent config
    const typeAgent = await this.typeAgentRepository.findById(agentData.agentUser.typeId);
    
    if (!typeAgent) {
      this.logger.error(`TypeAgent ${agentData.agentUser.typeId} không tồn tại`);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // 3. Kiểm tra feature có được enable không
    const isFeatureEnabled = typeAgent.config[requiredFeature];
    
    if (!isFeatureEnabled) {
      this.logger.warn(
        `Feature ${requiredFeature} không được enable cho TypeAgent ${typeAgent.id} (${typeAgent.name})`
      );
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_FEATURE_NOT_ENABLED,
        `Tính năng ${this.getFeatureDisplayName(requiredFeature)} không được hỗ trợ cho loại agent này`
      );
    }

    this.logger.log(
      `Validation thành công: Agent ${agentId} thuộc user ${userId}, feature ${requiredFeature} enabled`
    );
  }

  /**
   * Chỉ validate agent ownership (không check feature)
   * @param agentId ID của agent
   * @param userId ID của người dùng
   */
  async validateAgentOwnership(agentId: string, userId: number): Promise<void> {
    const agentData = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);
    
    if (!agentData) {
      this.logger.warn(`Agent ${agentId} không tồn tại hoặc không thuộc về user ${userId}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    this.logger.log(`Agent ownership validation thành công: Agent ${agentId} thuộc user ${userId}`);
  }

  /**
   * Validate multiple features cùng lúc
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param requiredFeatures Danh sách features cần kiểm tra
   */
  async validateAgentAndMultipleFeatures(
    agentId: string,
    userId: number,
    requiredFeatures: (keyof TypeAgentConfig)[],
  ): Promise<void> {
    // 1. Kiểm tra agent ownership
    const agentData = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);
    
    if (!agentData) {
      this.logger.warn(`Agent ${agentId} không tồn tại hoặc không thuộc về user ${userId}`);
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    // 2. Lấy TypeAgent config
    const typeId = agentData.agentUser.typeId;

    // Kiểm tra typeId có null không
    if (!typeId) {
      this.logger.error(`Agent ${agentId} has null typeId - database inconsistency detected`);
      throw new AppException(
        AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
        'Agent chưa được gán loại agent. Vui lòng liên hệ admin để khắc phục.'
      );
    }

    const typeAgent = await this.typeAgentRepository.findById(typeId);

    if (!typeAgent) {
      this.logger.error(`TypeAgent ${typeId} không tồn tại`);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // 3. Kiểm tra tất cả features
    const disabledFeatures = requiredFeatures.filter(feature => !typeAgent.config[feature]);
    
    if (disabledFeatures.length > 0) {
      const featureNames = disabledFeatures.map(f => this.getFeatureDisplayName(f)).join(', ');
      this.logger.warn(
        `Features không được enable: ${disabledFeatures.join(', ')} cho TypeAgent ${typeAgent.id}`
      );
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_FEATURE_NOT_ENABLED,
        `Các tính năng sau không được hỗ trợ cho loại agent này: ${featureNames}`
      );
    }

    this.logger.log(
      `Multiple features validation thành công: Agent ${agentId}, features: ${requiredFeatures.join(', ')}`
    );
  }

  /**
   * Lấy tên hiển thị của feature
   * @param feature Feature key
   * @returns Tên hiển thị
   */
  private getFeatureDisplayName(feature: keyof TypeAgentConfig): string {
    const featureDisplayNames: Record<keyof TypeAgentConfig, string> = {
      enableAgentProfileCustomization: 'Tùy chỉnh hồ sơ Agent',
      enableTaskConversionTracking: 'Theo dõi chuyển đổi',
      enableDynamicStrategyExecution: 'Thực thi chiến lược động',
      enableMultiAgentCollaboration: 'Hợp tác đa Agent',
      enableOutputToMessenger: 'Đầu ra qua Messenger',
      enableOutputToWebsiteLiveChat: 'Đầu ra qua Live Chat Website',
      enableResourceUsage: 'Sử dụng tài nguyên',
      enableOutputToZaloOA: 'Đầu ra qua Zalo OA',
    };

    return featureDisplayNames[feature] || feature;
  }
}
