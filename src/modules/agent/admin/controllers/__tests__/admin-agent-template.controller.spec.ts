import { Test, TestingModule } from '@nestjs/testing';
import { AdminAgentTemplateController } from '../admin-agent-template.controller';
import { AdminAgentTemplateService } from '../../services/admin-agent-template.service';
import { AgentTemplateStatus } from '@modules/agent/constants';
import { ApiResponseDto } from '@/common/response';

describe('AdminAgentTemplateController', () => {
  let controller: AdminAgentTemplateController;
  let service: AdminAgentTemplateService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminAgentTemplateController],
      providers: [
        {
          provide: AdminAgentTemplateService,
          useValue: {
            findAll: jest.fn(),
            findById: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
            updateStatus: jest.fn(),
            remove: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<AdminAgentTemplateController>(AdminAgentTemplateController);
    service = module.get<AdminAgentTemplateService>(AdminAgentTemplateService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return paginated list of agent templates', async () => {
      const queryDto = {
        page: 1,
        limit: 10,
        status: AgentTemplateStatus.PUBLISHED,
      };
      
      const paginatedResult = {
        items: [
          {
            id: 'template-1',
            name: 'Template 1',
            model: 'gpt-4',
            status: AgentTemplateStatus.PUBLISHED,
          },
        ],
        meta: {
          totalItems: 1,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };
      
      service.findAll.mockResolvedValue(paginatedResult);
      
      const result = await controller.findAll(queryDto as any);
      
      expect(service.findAll).toHaveBeenCalledWith(queryDto);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual(paginatedResult);
    });
  });

  describe('findOne', () => {
    it('should return agent template details', async () => {
      const id = 'template-1';
      const templateDetail = {
        id,
        name: 'Template 1',
        modelConfig: { modelId: 'gpt-4' },
        status: AgentTemplateStatus.PUBLISHED,
      };
      
      service.findById.mockResolvedValue(templateDetail);
      
      const result = await controller.findOne(id);
      
      expect(service.findById).toHaveBeenCalledWith(id);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual(templateDetail);
    });
  });

  describe('create', () => {
    it('should create a new agent template', async () => {
      const createDto = {
        name: 'New Template',
        typeId: 1,
        modelConfig: { modelId: 'gpt-4' },
        status: AgentTemplateStatus.DRAFT,
      };
      
      const employee = { id: 1 };
      const serviceResult = {
        id: 'new-template-id',
        avatarUrlUpload: 'https://s3.example.com/upload',
      };
      
      service.create.mockResolvedValue(serviceResult);
      
      const result = await controller.create(createDto as any, employee as any);
      
      expect(service.create).toHaveBeenCalledWith(createDto, employee.id);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual(serviceResult);
    });
  });

  describe('update', () => {
    it('should update agent template', async () => {
      const id = 'template-1';
      const updateDto = {
        name: 'Updated Template',
        modelConfig: { modelId: 'gpt-4' },
      };
      
      const employee = { id: 1 };
      const serviceResult = {
        avatarUrlUpload: 'https://s3.example.com/upload',
      };
      
      service.update.mockResolvedValue(serviceResult);
      
      const result = await controller.update(id, updateDto as any, employee as any);
      
      expect(service.update).toHaveBeenCalledWith(id, updateDto, employee.id);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual(serviceResult);
    });
  });

  describe('updateStatus', () => {
    it('should update agent template status', async () => {
      const id = 'template-1';
      const updateStatusDto = { status: AgentTemplateStatus.PUBLISHED };
      const employee = { id: 1 };
      
      service.updateStatus.mockResolvedValue(undefined);
      
      const result = await controller.updateStatus(id, updateStatusDto, employee as any);
      
      expect(service.updateStatus).toHaveBeenCalledWith(id, updateStatusDto, employee.id);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toBeNull();
      expect(result.message).toBe('Cập nhật trạng thái agent template thành công');
    });
  });

  describe('remove', () => {
    it('should remove agent template', async () => {
      const id = 'template-1';
      const employee = { id: 1 };
      
      service.remove.mockResolvedValue(undefined);
      
      const result = await controller.remove(id, employee as any);
      
      expect(service.remove).toHaveBeenCalledWith(id, employee.id);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toBeNull();
      expect(result.message).toBe('Xóa agent template thành công');
    });
  });
});
