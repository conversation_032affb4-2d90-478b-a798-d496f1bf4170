import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsObject } from 'class-validator';

/**
 * DTO cho việc cập nhật MCP system
 */
export class UpdateMcpSystemDto {
  /**
   * Tên server MCP
   */
  @ApiPropertyOptional({
    description: 'Tên server MCP',
    example: 'filesystem-server-updated',
  })
  @IsOptional()
  @IsString({ message: 'Tên server MCP phải là chuỗi' })
  nameServer?: string;

  /**
   * Mô tả về MCP system
   */
  @ApiPropertyOptional({
    description: 'Mô tả về MCP system',
    example: 'Server MCP để quản lý hệ thống file - đã cập nhật',
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;

  /**
   * Cấu hình MCP dạng JSON
   */
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> hình MCP dạng JSON',
    example: {
      command: 'node',
      args: ['server-updated.js'],
      env: {
        NODE_ENV: 'production',
        DEBUG: 'true'
      }
    },
  })
  @IsOptional()
  @IsObject({ message: 'Cấu hình MCP phải là object' })
  config?: Record<string, any>;
}
