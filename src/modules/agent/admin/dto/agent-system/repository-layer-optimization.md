# Repository Layer Optimization - Agent System Management

## Overview
Di chuyển bulk operations logic từ Service layer xuống Repository layer để tuân thủ nguyên tắc **Separation of Concerns** và **Single Responsibility Principle**.

## Architecture Changes

### Before: Service-Heavy Approach
```
Controller → Service (Business Logic + Database Queries) → Repository (Basic CRUD)
```

### After: Repository-Centric Approach  
```
Controller → Service (Business Logic Only) → Repository (Optimized Database Operations)
```

## Implementation Details

### 1. AgentSystemRepository - New Methods

#### **findExistingIds()**
```typescript
async findExistingIds(ids: string[]): Promise<string[]> {
  const results = await this.createQueryBuilder('as')
    .leftJoin('agents', 'a', 'as.id = a.id')
    .select(['as.id'])
    .where('as.id IN (:...ids)', { ids })
    .andWhere('as.deletedBy IS NULL')
    .andWhere('a.deletedAt IS NULL')
    .getRawMany();

  return results.map(item => item.as_id);
}
```

#### **bulkSoftDelete()**
```typescript
async bulkSoftDelete(ids: string[], employeeId: number): Promise<number> {
  const result = await this.createQueryBuilder()
    .update(AgentSystem)
    .set({ 
      deletedBy: employeeId,
      updatedBy: employeeId 
    })
    .where('id IN (:...ids)', { ids })
    .andWhere('deletedBy IS NULL')
    .execute();

  return result.affected || 0;
}
```

#### **findDeletedIds()**
```typescript
async findDeletedIds(ids: string[]): Promise<string[]> {
  const results = await this.createQueryBuilder('as')
    .leftJoin('agents', 'a', 'as.id = a.id')
    .select(['as.id'])
    .where('as.id IN (:...ids)', { ids })
    .andWhere('as.deletedBy IS NOT NULL')
    .andWhere('a.deletedAt IS NOT NULL')
    .getRawMany();

  return results.map(item => item.as_id);
}
```

#### **bulkRestore()**
```typescript
async bulkRestore(ids: string[]): Promise<number> {
  const result = await this.createQueryBuilder()
    .update(AgentSystem)
    .set({ deletedBy: null })
    .where('id IN (:...ids)', { ids })
    .andWhere('deletedBy IS NOT NULL')
    .execute();

  return result.affected || 0;
}
```

### 2. AgentRepository - New Methods

#### **bulkSoftDelete()**
```typescript
async bulkSoftDelete(ids: string[]): Promise<number> {
  const currentTimestamp = Date.now();
  const result = await this.createQueryBuilder()
    .update(Agent)
    .set({ deletedAt: currentTimestamp })
    .where('id IN (:...ids)', { ids })
    .andWhere('deletedAt IS NULL')
    .execute();

  return result.affected || 0;
}
```

#### **bulkRestore()**
```typescript
async bulkRestore(ids: string[]): Promise<number> {
  const result = await this.createQueryBuilder()
    .update(Agent)
    .set({ deletedAt: null })
    .where('id IN (:...ids)', { ids })
    .andWhere('deletedAt IS NOT NULL')
    .execute();

  return result.affected || 0;
}
```

### 3. Service Layer - Simplified Logic

#### **Before: Service với Database Queries**
```typescript
// Service trực tiếp thực hiện database queries
const existingAgentSystems = await this.agentSystemRepository
  .createQueryBuilder('as')
  .leftJoin('agents', 'a', 'as.id = a.id')
  .select(['as.id', 'a.id'])
  .where('as.id IN (:...ids)', { ids })
  .andWhere('as.deletedBy IS NULL')
  .andWhere('a.deletedAt IS NULL')
  .getRawMany();

await this.agentSystemRepository
  .createQueryBuilder()
  .update()
  .set({ deletedBy: employeeId })
  .where('id IN (:...existingIds)', { existingIds })
  .execute();
```

#### **After: Service chỉ Business Logic**
```typescript
// Service chỉ gọi repository methods
const existingIds = await this.agentSystemRepository.findExistingIds(ids);
const agentSystemUpdated = await this.agentSystemRepository.bulkSoftDelete(existingIds, employeeId);
const agentsUpdated = await this.agentRepository.bulkSoftDelete(existingIds);
```

## Benefits of Repository-Centric Approach

### 1. **Separation of Concerns**
- **Service Layer**: Chỉ xử lý business logic, validation, error handling
- **Repository Layer**: Chuyên trách database operations và query optimization

### 2. **Reusability**
- Repository methods có thể được tái sử dụng bởi nhiều services khác nhau
- Bulk operations có thể được sử dụng trong các contexts khác

### 3. **Testability**
- Dễ dàng mock repository methods trong unit tests
- Service tests tập trung vào business logic thay vì database queries

### 4. **Maintainability**
- Database logic được tập trung tại repository layer
- Thay đổi database schema chỉ cần update repository methods

### 5. **Performance Optimization**
- Repository có thể optimize queries mà không ảnh hưởng service logic
- Dễ dàng thêm caching, indexing strategies

## Code Quality Improvements

### 1. **Single Responsibility Principle**
```typescript
// Service: Business Logic Only
async removes(ids: string[], employeeId: number) {
  // Validation
  if (!ids || ids.length === 0) {
    return { deletedIds: [], errorIds: [] };
  }

  // Business Logic
  const existingIds = await this.agentSystemRepository.findExistingIds(ids);
  const errorIds = ids.filter(id => !existingIds.includes(id));

  // Database Operations (delegated to repository)
  await this.agentSystemRepository.bulkSoftDelete(existingIds, employeeId);
  await this.agentRepository.bulkSoftDelete(existingIds);

  // Response Logic
  return { deletedIds: existingIds, errorIds };
}
```

### 2. **Repository: Database Operations Only**
```typescript
// Repository: Pure Database Logic
async bulkSoftDelete(ids: string[], employeeId: number): Promise<number> {
  // Pure SQL operation
  const result = await this.createQueryBuilder()
    .update(AgentSystem)
    .set({ deletedBy: employeeId })
    .where('id IN (:...ids)', { ids })
    .execute();

  return result.affected || 0;
}
```

## Performance Comparison

| Aspect | Before | After | Improvement |
|--------|--------|-------|-------------|
| **Code Lines** | 50+ lines in service | 10 lines in service + repository methods | 80% reduction in service complexity |
| **Query Count** | Same (3 queries) | Same (3 queries) | No change |
| **Maintainability** | Medium | High | Easier to maintain |
| **Testability** | Medium | High | Easier to test |
| **Reusability** | Low | High | Repository methods reusable |

## Testing Strategy

### 1. **Repository Tests**
```typescript
describe('AgentSystemRepository', () => {
  it('should find existing IDs correctly', async () => {
    const ids = ['id1', 'id2', 'invalid-id'];
    const result = await repository.findExistingIds(ids);
    expect(result).toEqual(['id1', 'id2']);
  });

  it('should bulk soft delete correctly', async () => {
    const ids = ['id1', 'id2'];
    const affected = await repository.bulkSoftDelete(ids, employeeId);
    expect(affected).toBe(2);
  });
});
```

### 2. **Service Tests**
```typescript
describe('AdminAgentSystemService', () => {
  it('should handle bulk remove with mixed valid/invalid IDs', async () => {
    // Mock repository methods
    mockRepository.findExistingIds.mockResolvedValue(['id1']);
    mockRepository.bulkSoftDelete.mockResolvedValue(1);

    const result = await service.removes(['id1', 'invalid'], employeeId);
    
    expect(result.deletedIds).toEqual(['id1']);
    expect(result.errorIds).toEqual(['invalid']);
  });
});
```

## Migration Guide

### Step 1: Add Repository Methods
- Implement bulk methods in repositories
- Add proper error handling and logging

### Step 2: Update Service Layer
- Replace direct queries with repository method calls
- Simplify service logic to focus on business rules

### Step 3: Update Tests
- Create repository-specific tests
- Update service tests to mock repository methods

### Step 4: Validate Performance
- Ensure no performance regression
- Monitor query execution plans

## Future Enhancements

1. **Generic Bulk Operations**: Create base repository with generic bulk methods
2. **Query Caching**: Add caching layer in repository methods
3. **Batch Processing**: Implement batching for very large datasets
4. **Audit Logging**: Add audit trails in repository layer

## Conclusion

Repository-centric approach provides:
- ✅ **Better Code Organization**: Clear separation between business logic and data access
- ✅ **Improved Testability**: Easier to unit test both service and repository layers
- ✅ **Enhanced Reusability**: Repository methods can be used across multiple services
- ✅ **Better Maintainability**: Database logic centralized in repository layer
- ✅ **Performance Consistency**: Same performance with better code structure

This refactoring maintains the same performance benefits while significantly improving code quality and maintainability.
