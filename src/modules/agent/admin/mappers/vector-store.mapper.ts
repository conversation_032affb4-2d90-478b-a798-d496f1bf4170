import { VectorStoreDto } from '../dto/common';

/**
 * Mapper cho việc chuyển đổi thông tin vector store sang DTO
 */
export class VectorStoreMapper {
  /**
   * Chuyển đổi thông tin vector store sang DTO
   * @param vectorStoreId ID của vector store
   * @param vectorStoreName Tên của vector store (nếu có)
   * @returns VectorStoreDto hoặc null nếu không có vector store
   */
  static toDto(
    vectorStoreId: string | null,
    vectorStoreName?: string
  ): VectorStoreDto | null {
    if (!vectorStoreId) {
      return null;
    }

    return {
      vectorStoreId: vectorStoreId,
      vectorStoreName: vectorStoreName || 'Vector Store',
    };
  }
}
