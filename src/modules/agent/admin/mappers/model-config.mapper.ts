import { ModelConfigResponseDto } from '../dto/common';
import { ModelConfig } from '@modules/agent/interfaces/model-config.interface';

/**
 * Mapper cho việc chuyển đổi cấu hình model sang DTO
 */
export class ModelConfigMapper {
  /**
   * Chuyển đổi cấu hình model sang DTO
   * Đã loại bỏ các trường deprecated: modelId, providerName
   * @param modelConfig Cấu hình model
   * @returns ModelConfigResponseDto
   */
  static toResponseDto(modelConfig: ModelConfig): ModelConfigResponseDto {
    return {
      temperature: modelConfig?.temperature,
      top_p: modelConfig?.top_p,
      top_k: modelConfig?.top_k,
      max_tokens: modelConfig?.max_tokens,
    };
  }
}
