import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import * as request from 'supertest';
import { SubscriptionUserModule } from '../subscription-user.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { Subscription } from '@modules/subscription/entities';
import { Plan } from '../../entities/plan.entity';
import { PlanPricing } from '@modules/subscription/entities';
import { UsageLog } from '@modules/subscription/entities';

// This is a mock implementation for the JwtAuthGuard
// It will allow all requests to pass through without authentication
class MockJwtAuthGuard {
  canActivate() {
    return true;
  }
}

describe('SubscriptionUserController (Integration)', () => {
  let app: INestApplication;
  let moduleFixture: TestingModule;

  beforeAll(async () => {
    // Create a testing module with real implementations but mock database
    moduleFixture = await Test.createTestingModule({
      imports: [
        // Use in-memory SQLite for testing
        TypeOrmModule.forRootAsync({
          useFactory: () => ({
            type: 'sqlite',
            database: ':memory:',
            entities: [Subscription, Plan, PlanPricing, UsageLog],
            synchronize: true,
          }),
        }),
        // Import the subscription user module
        SubscriptionUserModule,
        // Mock JWT module
        JwtModule.register({
          secret: 'test-secret',
          signOptions: { expiresIn: '1h' },
        }),
        // Mock config module
        ConfigModule.forRoot({
          isGlobal: true,
        }),
      ],
      providers: [
        ConfigService,
      ],
    })
      // Override the JwtAuthGuard with our mock implementation
      .overrideGuard(JwtUserGuard)
      .useClass(MockJwtAuthGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    // Use validation pipe to validate DTOs
    app.useGlobalPipes(new ValidationPipe({
      transform: true,
      whitelist: true,
    }));
    await app.init();

    // Seed the database with test data
    await seedDatabase(moduleFixture);
  });

  afterAll(async () => {
    await app.close();
  });

  // Helper function to seed the database with test data
  async function seedDatabase(module: TestingModule) {
    const planRepository = module.get('PlanRepository');
    const planPricingRepository = module.get('PlanPricingRepository');
    const subscriptionRepository = module.get('SubscriptionRepository');
    const usageLogRepository = module.get('UsageLogRepository');

    // Create test plans
    const plan1 = await planRepository.save({
      name: 'Basic Plan',
      description: 'Basic plan for new users',
      packageType: 'TIME_ONLY',
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    const plan2 = await planRepository.save({
      name: 'Premium Plan',
      description: 'Premium plan with more features',
      packageType: 'USAGE_BASED',
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    // Create test plan pricing options
    const pricing1 = await planPricingRepository.save({
      planId: plan1.id,
      billingCycle: 'MONTHLY',
      price: 9.99,
      usageLimit: 1000,
      usageUnit: 'API_CALLS',
      isActive: true,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    const pricing2 = await planPricingRepository.save({
      planId: plan1.id,
      billingCycle: 'YEARLY',
      price: 99.99,
      usageLimit: 12000,
      usageUnit: 'API_CALLS',
      isActive: true,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    const pricing3 = await planPricingRepository.save({
      planId: plan2.id,
      billingCycle: 'MONTHLY',
      price: 19.99,
      usageLimit: 5000,
      usageUnit: 'API_CALLS',
      isActive: true,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    // Create test subscriptions
    const subscription1 = await subscriptionRepository.save({
      userId: 10,
      planPricingId: pricing1.id,
      startDate: Date.now(),
      endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
      autoRenew: true,
      status: 'ACTIVE',
      usageLimit: pricing1.usageLimit,
      currentUsage: 100,
      remainingValue: pricing1.usageLimit - 100,
      usageUnit: pricing1.usageUnit,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    // Create test usage logs
    await usageLogRepository.save({
      subscriptionId: subscription1.id,
      feature: 'API_CALL',
      amount: 50,
      usageTime: Date.now() - 2 * 24 * 60 * 60 * 1000,
      createdAt: Date.now() - 2 * 24 * 60 * 60 * 1000
    });

    await usageLogRepository.save({
      subscriptionId: subscription1.id,
      feature: 'API_CALL',
      amount: 50,
      usageTime: Date.now() - 1 * 24 * 60 * 60 * 1000,
      createdAt: Date.now() - 1 * 24 * 60 * 60 * 1000
    });
  }

  // Test cases
  describe('GET /user/plans', () => {
    it('should return a list of plans', () => {
      return request(app.getHttpServer())
        .get('/user/plans')
        .expect(200)
        .expect(res => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Success');
          expect(res.body.result).toHaveProperty('items');
          expect(res.body.result).toHaveProperty('meta');
          expect(res.body.result.items.length).toBeGreaterThan(0);
        });
    });
  });

  describe('GET /user/plans/:id', () => {
    it('should return a plan by id', () => {
      return request(app.getHttpServer())
        .get('/user/plans/1')
        .expect(200)
        .expect(res => {
          expect(res.body.code).toBe(200);
          expect(res.body.result).toHaveProperty('id');
          expect(res.body.result).toHaveProperty('name');
          expect(res.body.result.id).toBe(1);
        });
    });

    it('should return 404 if plan not found', () => {
      return request(app.getHttpServer())
        .get('/user/plans/999')
        .expect(404);
    });
  });

  describe('GET /user/plans/:planId/pricing', () => {
    it('should return pricing options for a plan', () => {
      return request(app.getHttpServer())
        .get('/user/plans/1/pricing')
        .expect(200)
        .expect(res => {
          expect(res.body.code).toBe(200);
          expect(res.body.result).toHaveProperty('items');
          expect(res.body.result.items.length).toBeGreaterThan(0);
          expect(res.body.result.items[0]).toHaveProperty('planId');
          expect(res.body.result.items[0].planId).toBe(1);
        });
    });
  });

  describe('GET /user/plan-pricing', () => {
    it('should return all plan pricing options', () => {
      return request(app.getHttpServer())
        .get('/user/plan-pricing')
        .expect(200)
        .expect(res => {
          expect(res.body.code).toBe(200);
          expect(res.body.result).toHaveProperty('items');
          expect(res.body.result).toHaveProperty('meta');
          expect(res.body.result.items.length).toBeGreaterThan(0);
        });
    });

    it('should filter plan pricing by billingCycle', () => {
      return request(app.getHttpServer())
        .get('/user/plan-pricing?billingCycle=MONTHLY')
        .expect(200)
        .expect(res => {
          expect(res.body.code).toBe(200);
          expect(res.body.result.items.length).toBeGreaterThan(0);
          expect(res.body.result.items.every(item => item.billingCycle === 'MONTHLY')).toBe(true);
        });
    });
  });

  describe('GET /user/subscriptions', () => {
    it('should return user subscriptions', () => {
      return request(app.getHttpServer())
        .get('/user/subscriptions')
        .expect(200)
        .expect(res => {
          expect(res.body.code).toBe(200);
          expect(res.body.result).toHaveProperty('items');
          expect(res.body.result).toHaveProperty('meta');
          expect(res.body.result.items.length).toBeGreaterThan(0);
        });
    });

    it('should filter subscriptions by status', () => {
      return request(app.getHttpServer())
        .get('/user/subscriptions?status=ACTIVE')
        .expect(200)
        .expect(res => {
          expect(res.body.code).toBe(200);
          expect(res.body.result.items.length).toBeGreaterThan(0);
          expect(res.body.result.items.every(item => item.status === 'ACTIVE')).toBe(true);
        });
    });
  });

  describe('GET /user/subscriptions/:id', () => {
    it('should return a subscription by id', () => {
      return request(app.getHttpServer())
        .get('/user/subscriptions/1')
        .expect(200)
        .expect(res => {
          expect(res.body.code).toBe(200);
          expect(res.body.result).toHaveProperty('id');
          expect(res.body.result).toHaveProperty('userId');
          expect(res.body.result).toHaveProperty('planInfo');
          expect(res.body.result).toHaveProperty('pricingInfo');
          expect(res.body.result.id).toBe(1);
        });
    });
  });

  describe('GET /user/subscriptions/:subscriptionId/usage-logs', () => {
    it('should return usage logs for a subscription', () => {
      return request(app.getHttpServer())
        .get('/user/subscriptions/1/usage-logs')
        .expect(200)
        .expect(res => {
          expect(res.body.code).toBe(200);
          expect(res.body.result).toHaveProperty('items');
          expect(res.body.result).toHaveProperty('meta');
          expect(res.body.result.items.length).toBeGreaterThan(0);
          expect(res.body.result.items[0]).toHaveProperty('subscriptionId');
          expect(res.body.result.items[0].subscriptionId).toBe(1);
        });
    });
  });

  // Note: The following tests would modify data, so we're just testing the endpoints exist
  // In a real test, you might want to use transactions to roll back changes

  describe('POST /user/subscriptions', () => {
    it('should create a new subscription', () => {
      return request(app.getHttpServer())
        .post('/user/subscriptions')
        .send({
          planPricingId: 2,
          autoRenew: true
        })
        .expect(201)
        .expect(res => {
          expect(res.body.code).toBe(201);
          expect(res.body.message).toBe('Subscription created successfully');
          expect(res.body.result).toHaveProperty('id');
          expect(res.body.result).toHaveProperty('planPricingId');
          expect(res.body.result.planPricingId).toBe(2);
        });
    });
  });

  describe('PUT /user/subscriptions/:id/cancel', () => {
    it('should cancel a subscription', () => {
      return request(app.getHttpServer())
        .put('/user/subscriptions/1/cancel')
        .expect(200)
        .expect(res => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Subscription cancelled successfully');
          expect(res.body.result).toHaveProperty('id');
          expect(res.body.result).toHaveProperty('status');
          expect(res.body.result.status).toBe('CANCELLED');
        });
    });
  });

  describe('PUT /user/subscriptions/:id/auto-renew', () => {
    it('should update auto-renew setting', () => {
      return request(app.getHttpServer())
        .put('/user/subscriptions/1/auto-renew')
        .send({
          autoRenew: false
        })
        .expect(200)
        .expect(res => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Auto-renew setting updated successfully');
          expect(res.body.result).toHaveProperty('id');
          expect(res.body.result).toHaveProperty('autoRenew');
        });
    });
  });

  describe('PUT /user/subscriptions/:id/change-plan', () => {
    it('should change subscription plan', () => {
      return request(app.getHttpServer())
        .put('/user/subscriptions/1/change-plan')
        .send({
          newPlanPricingId: 3,
          effectiveImmediately: true
        })
        .expect(200)
        .expect(res => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Subscription plan changed successfully');
          expect(res.body.result).toHaveProperty('id');
        });
    });
  });
});
