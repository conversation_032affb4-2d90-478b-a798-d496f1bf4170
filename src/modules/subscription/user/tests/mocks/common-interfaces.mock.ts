// Mock for common interfaces used in tests

export interface PaginatedResult<T> {
  items: T[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: {
    field: string;
    direction: SortDirection;
  };
  search?: SearchParams;
}

export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

export interface SearchParams {
  filters?: Record<string, any>;
  query?: string;
}

export class ApiResponse<T = any> {
  code: number;
  message: string;
  result: T;

  constructor(code: number, message: string, result: T) {
    this.code = code;
    this.message = message;
    this.result = result;
  }

  static success<T>(data: T, message: string = 'Success'): ApiResponse<T> {
    return new ApiResponse(200, message, data);
  }

  static created<T>(data: T, message: string = 'Created successfully'): ApiResponse<T> {
    return new ApiResponse(201, message, data);
  }

  static error(message: string, code: number = 400): ApiResponse<null> {
    return new ApiResponse(code, message, null);
  }
}
