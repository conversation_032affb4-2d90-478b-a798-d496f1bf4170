import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SubscriptionUserService } from '@modules/subscription/user/services';
import { Subscription, SubscriptionStatus } from '@modules/subscription/entities';
import { PlanPricing } from '@modules/subscription/entities';
import { Plan, PackageType } from '../../entities/plan.entity';
import { UsageLog } from '@modules/subscription/entities';
import { SubscriptionRepository } from '@modules/subscription/repositories';
import { UsageLogRepository } from '@modules/subscription/repositories';
import { PaginatedResult } from '@/common/response';
import { NotFoundException, BadRequestException } from '@nestjs/common';

// Mock data
const mockSubscription: Subscription = {
  id: 1,
  userId: 10,
  planPricingId: 1,
  startDate: Date.now(),
  endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
  autoRenew: true,
  status: SubscriptionStatus.ACTIVE,
  usageLimit: 1000,
  currentUsage: 100,
  remainingValue: 900,
  usageUnit: 'API_CALLS',
  createdAt: Date.now(),
  updatedAt: Date.now()
};

const mockPlanPricing: PlanPricing = {
  id: 1,
  planId: 1,
  billingCycle: 'MONTHLY',
  price: 9.99,
  usageLimit: 1000,
  usageUnit: 'API_CALLS',
  isActive: true,
  createdAt: Date.now(),
  updatedAt: Date.now()
};

const mockPlan: Plan = {
  id: 1,
  name: 'Basic Plan',
  description: 'Basic plan for new users',
  packageType: PackageType.TIME_ONLY,
  createdAt: Date.now(),
  updatedAt: Date.now()
};

const mockUsageLog: UsageLog = {
  id: 1,
  subscriptionId: 1,
  feature: 'API_CALL',
  amount: 5,
  usageTime: Date.now(),
  createdAt: Date.now()
};

const mockUsageLogList: UsageLog[] = [
  mockUsageLog,
  {
    ...mockUsageLog,
    id: 2,
    feature: 'STORAGE',
    amount: 10
  }
];

const mockSubscriptionList: Subscription[] = [
  mockSubscription,
  {
    ...mockSubscription,
    id: 2,
    planPricingId: 2
  }
];

const mockPaginatedSubscriptionResult: PaginatedResult<Subscription> = {
  items: mockSubscriptionList,
  meta: {
    totalItems: 2,
    itemCount: 2,
    itemsPerPage: 10,
    totalPages: 1,
    currentPage: 1
  }
};

const mockPaginatedUsageLogResult: PaginatedResult<UsageLog> = {
  items: mockUsageLogList,
  meta: {
    totalItems: 2,
    itemCount: 2,
    itemsPerPage: 10,
    totalPages: 1,
    currentPage: 1
  }
};

describe('SubscriptionUserService', () => {
  let service: SubscriptionUserService;
  let subscriptionRepository: Repository<Subscription>;
  let planPricingRepository: Repository<PlanPricing>;
  let planRepository: Repository<Plan>;
  let usageLogRepository: Repository<UsageLog>;
  let subscriptionCustomRepository: SubscriptionRepository;
  let usageLogCustomRepository: UsageLogRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SubscriptionUserService,
        {
          provide: getRepositoryToken(Subscription),
          useValue: {
            findOne: jest.fn().mockResolvedValue(mockSubscription),
            find: jest.fn().mockResolvedValue([mockSubscription]),
            create: jest.fn().mockReturnValue(mockSubscription),
            save: jest.fn().mockResolvedValue(mockSubscription),
          },
        },
        {
          provide: getRepositoryToken(PlanPricing),
          useValue: {
            findOne: jest.fn().mockResolvedValue(mockPlanPricing),
          },
        },
        {
          provide: getRepositoryToken(Plan),
          useValue: {
            findOne: jest.fn().mockResolvedValue(mockPlan),
          },
        },
        {
          provide: getRepositoryToken(UsageLog),
          useValue: {
            findOne: jest.fn().mockResolvedValue(mockUsageLog),
          },
        },
        {
          provide: SubscriptionRepository,
          useValue: {
            findByUser: jest.fn().mockResolvedValue(mockPaginatedSubscriptionResult),
            findSubscriptionById: jest.fn().mockResolvedValue(mockSubscription),
            updateStatus: jest.fn().mockResolvedValue(mockSubscription),
            updateAutoRenew: jest.fn().mockResolvedValue(mockSubscription),
            changePlan: jest.fn().mockResolvedValue(mockSubscription),
          },
        },
        {
          provide: UsageLogRepository,
          useValue: {
            findBySubscription: jest.fn().mockResolvedValue(mockPaginatedUsageLogResult),
          },
        },
      ],
    }).compile();

    service = module.get<SubscriptionUserService>(SubscriptionUserService);
    subscriptionRepository = module.get<Repository<Subscription>>(getRepositoryToken(Subscription));
    planPricingRepository = module.get<Repository<PlanPricing>>(getRepositoryToken(PlanPricing));
    planRepository = module.get<Repository<Plan>>(getRepositoryToken(Plan));
    usageLogRepository = module.get<Repository<UsageLog>>(getRepositoryToken(UsageLog));
    subscriptionCustomRepository = module.get<SubscriptionRepository>(SubscriptionRepository);
    usageLogCustomRepository = module.get<UsageLogRepository>(UsageLogRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findSubscriptions', () => {
    it('should return all subscriptions for a user with pagination', async () => {
      const result = await service.findSubscriptions(10, null, 1, 10);
      expect(subscriptionCustomRepository.findByUser).toHaveBeenCalledWith(10, null, expect.any(Object));
      expect(result).toEqual(mockPaginatedSubscriptionResult);
    });

    it('should filter subscriptions by status when provided', async () => {
      const result = await service.findSubscriptions(10, SubscriptionStatus.ACTIVE, 1, 10);
      expect(subscriptionCustomRepository.findByUser).toHaveBeenCalledWith(10, SubscriptionStatus.ACTIVE, expect.any(Object));
      expect(result).toEqual(mockPaginatedSubscriptionResult);
    });
  });

  describe('findSubscriptionById', () => {
    it('should return a subscription by id', async () => {
      const result = await service.findSubscriptionById(1);
      expect(subscriptionCustomRepository.findSubscriptionById).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockSubscription);
    });

    it('should throw NotFoundException if subscription is not found', async () => {
      jest.spyOn(subscriptionCustomRepository, 'findSubscriptionById').mockResolvedValueOnce(null);
      await expect(service.findSubscriptionById(999)).rejects.toThrow(NotFoundException);
    });
  });

  describe('findSubscriptionWithDetails', () => {
    it('should return subscription with plan and pricing details', async () => {
      const result = await service.findSubscriptionWithDetails(1);
      expect(subscriptionCustomRepository.findSubscriptionById).toHaveBeenCalledWith(1);
      expect(planPricingRepository.findOne).toHaveBeenCalled();
      expect(planRepository.findOne).toHaveBeenCalled();
      expect(result).toHaveProperty('subscription');
      expect(result).toHaveProperty('planInfo');
      expect(result).toHaveProperty('pricingInfo');
    });

    it('should throw NotFoundException if subscription is not found', async () => {
      jest.spyOn(subscriptionCustomRepository, 'findSubscriptionById').mockResolvedValueOnce(null);
      await expect(service.findSubscriptionWithDetails(999)).rejects.toThrow(NotFoundException);
    });
  });

  describe('createSubscription', () => {
    it('should create a new subscription', async () => {
      const result = await service.createSubscription(10, 1, true);
      expect(planPricingRepository.findOne).toHaveBeenCalled();
      expect(subscriptionRepository.find).toHaveBeenCalled();
      expect(subscriptionRepository.create).toHaveBeenCalled();
      expect(subscriptionRepository.save).toHaveBeenCalled();
      expect(result).toEqual(mockSubscription);
    });

    it('should throw NotFoundException if plan pricing is not found', async () => {
      jest.spyOn(planPricingRepository, 'findOne').mockResolvedValueOnce(null);
      await expect(service.createSubscription(10, 999, true)).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException if user already has active subscriptions', async () => {
      jest.spyOn(subscriptionRepository, 'find').mockResolvedValueOnce([mockSubscription]);
      await expect(service.createSubscription(10, 1, true)).rejects.toThrow(BadRequestException);
    });
  });

  describe('cancelSubscription', () => {
    it('should cancel an active subscription', async () => {
      const result = await service.cancelSubscription(1);
      expect(subscriptionCustomRepository.findSubscriptionById).toHaveBeenCalledWith(1);
      expect(subscriptionCustomRepository.updateStatus).toHaveBeenCalledWith(1, SubscriptionStatus.CANCELLED);
      expect(result).toEqual(mockSubscription);
    });

    it('should throw BadRequestException if subscription is not active', async () => {
      jest.spyOn(subscriptionCustomRepository, 'findSubscriptionById').mockResolvedValueOnce({
        ...mockSubscription,
        status: SubscriptionStatus.CANCELLED
      });
      await expect(service.cancelSubscription(1)).rejects.toThrow(BadRequestException);
    });
  });

  describe('updateAutoRenew', () => {
    it('should update auto-renew setting for an active subscription', async () => {
      const result = await service.updateAutoRenew(1, false);
      expect(subscriptionCustomRepository.findSubscriptionById).toHaveBeenCalledWith(1);
      expect(subscriptionCustomRepository.updateAutoRenew).toHaveBeenCalledWith(1, false);
      expect(result).toEqual(mockSubscription);
    });

    it('should throw BadRequestException if subscription is not active', async () => {
      jest.spyOn(subscriptionCustomRepository, 'findSubscriptionById').mockResolvedValueOnce({
        ...mockSubscription,
        status: SubscriptionStatus.CANCELLED
      });
      await expect(service.updateAutoRenew(1, false)).rejects.toThrow(BadRequestException);
    });
  });

  describe('changePlan', () => {
    it('should change plan for an active subscription with immediate effect', async () => {
      const result = await service.changePlan(1, 2, true);
      expect(subscriptionCustomRepository.findSubscriptionById).toHaveBeenCalledWith(1);
      expect(planPricingRepository.findOne).toHaveBeenCalled();
      expect(subscriptionCustomRepository.changePlan).toHaveBeenCalled();
      expect(result).toEqual(mockSubscription);
    });

    it('should throw BadRequestException if subscription is not active', async () => {
      jest.spyOn(subscriptionCustomRepository, 'findSubscriptionById').mockResolvedValueOnce({
        ...mockSubscription,
        status: SubscriptionStatus.CANCELLED
      });
      await expect(service.changePlan(1, 2, true)).rejects.toThrow(BadRequestException);
    });

    it('should throw NotFoundException if new plan pricing is not found', async () => {
      jest.spyOn(planPricingRepository, 'findOne').mockResolvedValueOnce(null);
      await expect(service.changePlan(1, 999, true)).rejects.toThrow(NotFoundException);
    });
  });

  describe('findUsageLogs', () => {
    it('should return usage logs for a subscription with pagination', async () => {
      const result = await service.findUsageLogs(1, null, null, 1, 10);
      expect(subscriptionCustomRepository.findSubscriptionById).toHaveBeenCalledWith(1);
      expect(usageLogCustomRepository.findBySubscription).toHaveBeenCalledWith(1, null, null, expect.any(Object));
      expect(result).toEqual(mockPaginatedUsageLogResult);
    });

    it('should filter usage logs by date range when provided', async () => {
      const startDate = Date.now() - 7 * 24 * 60 * 60 * 1000;
      const endDate = Date.now();
      const result = await service.findUsageLogs(1, startDate, endDate, 1, 10);
      expect(usageLogCustomRepository.findBySubscription).toHaveBeenCalledWith(1, startDate, endDate, expect.any(Object));
      expect(result).toEqual(mockPaginatedUsageLogResult);
    });

    it('should throw NotFoundException if subscription is not found', async () => {
      jest.spyOn(subscriptionCustomRepository, 'findSubscriptionById').mockResolvedValueOnce(null);
      await expect(service.findUsageLogs(999, null, null, 1, 10)).rejects.toThrow(NotFoundException);
    });
  });
});
