import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { PlanPricingUserController } from '@modules/subscription/user/controllers';
import { PlanPricingUserService } from '@modules/subscription/user/services';
import { PlanPricing } from '@modules/subscription/entities';
import { PlanPricingFilterDto } from '@modules/subscription/dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { PaginatedResult } from '@/common/response';

// Mock data
const mockPlanPricing: PlanPricing = {
  id: 1,
  planId: 1,
  billingCycle: 'MONTHLY',
  price: 9.99,
  usageLimit: 1000,
  usageUnit: 'API_CALLS',
  isActive: true,
  createdAt: Date.now(),
  updatedAt: Date.now()
};

const mockPlanPricingList: PlanPricing[] = [
  mockPlanPricing,
  {
    ...mockPlanPricing,
    id: 2,
    billingCycle: 'YEARLY',
    price: 99.99,
    usageLimit: 12000
  }
];

const mockPaginatedResult: PaginatedResult<PlanPricing> = {
  items: mockPlanPricingList,
  meta: {
    totalItems: 2,
    itemCount: 2,
    itemsPerPage: 10,
    totalPages: 1,
    currentPage: 1
  }
};

describe('PlanPricingUserController', () => {
  let controller: PlanPricingUserController;
  let service: PlanPricingUserService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PlanPricingUserController],
      providers: [
        {
          provide: PlanPricingUserService,
          useValue: {
            findPlanPricing: jest.fn().mockResolvedValue(mockPaginatedResult),
            findPlanPricingById: jest.fn().mockResolvedValue(mockPlanPricing),
          },
        },
      ],
    }).compile();

    controller = module.get<PlanPricingUserController>(PlanPricingUserController);
    service = module.get<PlanPricingUserService>(PlanPricingUserService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return all plan pricing options with pagination', async () => {
      const filterDto: PlanPricingFilterDto = { page: 1, limit: 10 };
      const result = await controller.findAll(filterDto);

      expect(service.findPlanPricing).toHaveBeenCalledWith(null, 1, 10);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(200);
      expect(result.message).toBe('Success');
      expect(result.result).toHaveProperty('items');
      expect(result.result).toHaveProperty('meta');
    });

    it('should filter plan pricing by billingCycle when provided', async () => {
      const filterDto: PlanPricingFilterDto = {
        page: 1,
        limit: 10,
        billingCycle: 'MONTHLY'
      };
      const result = await controller.findAll(filterDto);

      expect(service.findPlanPricing).toHaveBeenCalledWith('MONTHLY', 1, 10);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result?.result?.items.length).toBe(2);
    });
  });

  describe('findOne', () => {
    it('should return a plan pricing by id', async () => {
      const result = await controller.findOne(1);

      expect(service.findPlanPricingById).toHaveBeenCalledWith(1);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(200);
      expect(result?.result?.id).toBe(1);
    });

    it('should throw NotFoundException if plan pricing is not found', async () => {
      jest.spyOn(service, 'findPlanPricingById').mockResolvedValueOnce(null);

      await expect(controller.findOne(999)).rejects.toThrow(NotFoundException);
    });
  });
});
