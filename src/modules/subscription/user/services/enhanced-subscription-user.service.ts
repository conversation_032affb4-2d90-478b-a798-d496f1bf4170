import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Subscription } from '@modules/subscription/entities/subscription.entity';
import { PlanPricing } from '@modules/subscription/entities/plan-pricing.entity';
import { Plan } from '@modules/subscription/entities/plan.entity';
import { OrderPlanHistory } from '@modules/subscription/entities/order-plan-history.entity';
import { SubscriptionRepository } from '@modules/subscription/repositories/subscription.repository';
import {User} from "@modules/user/entities";
import {PackageType, SubscriptionStatus} from "@modules/subscription/enums";

@Injectable()
export class EnhancedSubscriptionUserService {
  constructor(
    @InjectRepository(Subscription)
    private readonly subscriptionRepository: Repository<Subscription>,
    @InjectRepository(PlanPricing)
    private readonly planPricingRepository: Repository<PlanPricing>,
    @InjectRepository(Plan)
    private readonly planRepository: Repository<Plan>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(OrderPlanHistory)
    private readonly orderPlanHistoryRepository: Repository<OrderPlanHistory>,
    private readonly subscriptionCustomRepository: SubscriptionRepository,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Tạo đăng ký mới với việc trừ điểm người dùng và lưu lịch sử mua gói
   * @param userId ID của người dùng
   * @param planPricingId ID của tùy chọn giá
   * @param autoRenew Tự động gia hạn
   * @returns Đăng ký mới
   */
  async createSubscription(
    userId: number,
    planPricingId: number,
    autoRenew: boolean = true,
  ): Promise<Subscription> {
    // Sử dụng transaction để đảm bảo tính toàn vẹn dữ liệu
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Kiểm tra tùy chọn giá có tồn tại không
      const planPricing = await this.planPricingRepository.findOne({
        where: { id: planPricingId, isActive: true },
      });

      if (!planPricing) {
        throw new NotFoundException(
          `PlanPricing with ID ${planPricingId} not found or not active`,
        );
      }

      // Lấy thông tin gói dịch vụ
      const plan = await this.planRepository.findOne({
        where: { id: planPricing.planId },
      });

      if (!plan) {
        throw new NotFoundException(
          `Plan with ID ${planPricing.planId} not found`,
        );
      }

      // Kiểm tra người dùng có tồn tại không
      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Kiểm tra người dùng đã có đăng ký nào đang hoạt động không
      const activeSubscriptions = await this.subscriptionRepository.find({
        where: { userId, status: SubscriptionStatus.ACTIVE },
      });

      if (activeSubscriptions.length > 0) {
        throw new BadRequestException('User already has active subscriptions');
      }

      // Kiểm tra số điểm của người dùng có đủ không
      const pointsRequired = Number(planPricing.price);
      if (user.pointsBalance < pointsRequired) {
        throw new BadRequestException(
          `User does not have enough points. Required: ${pointsRequired}, Available: ${user.pointsBalance}`,
        );
      }

      // Tính toán thời gian bắt đầu và kết thúc
      const now = Date.now();
      let endDate: number;

      // Tính toán thời gian kết thúc dựa trên chu kỳ thanh toán và loại gói
      if (
        plan.packageType === PackageType.TIME_ONLY ||
        plan.packageType === PackageType.HYBRID
      ) {
        // Tính toán thời gian kết thúc dựa trên chu kỳ thanh toán
        switch (planPricing.billingCycle) {
          case 'MONTHLY':
            endDate = now + 30 * 24 * 60 * 60 * 1000; // 30 ngày
            break;
          case 'QUARTERLY':
            endDate = now + 90 * 24 * 60 * 60 * 1000; // 90 ngày
            break;
          case 'YEARLY':
            endDate = now + 365 * 24 * 60 * 60 * 1000; // 365 ngày
            break;
          default:
            endDate = now + 30 * 24 * 60 * 60 * 1000; // Mặc định 30 ngày
        }
      } else {
        // Đối với gói USAGE_BASED, thời gian kết thúc có thể là một giá trị xa trong tương lai
        endDate = now + 365 * 5 * 24 * 60 * 60 * 1000; // 5 năm
      }

      // Xác định usageLimit và remainingValue dựa trên loại gói
      let usageLimit = 0;
      let remainingValue = 0;

      if (
        plan.packageType === PackageType.USAGE_BASED ||
        plan.packageType === PackageType.HYBRID
      ) {
        usageLimit = planPricing.usageLimit;
        remainingValue = planPricing.usageLimit;
      }

      // Tạo đăng ký mới
      const subscription = this.subscriptionRepository.create({
        userId,
        planPricingId,
        startDate: now,
        endDate,
        autoRenew,
        status: SubscriptionStatus.ACTIVE,
        usageLimit,
        currentUsage: 0,
        remainingValue,
        usageUnit: planPricing.usageUnit,
        createdAt: now,
        updatedAt: now,
      });

      // Lưu đăng ký
      const savedSubscription = await queryRunner.manager.save(subscription);

      // Trừ điểm của người dùng
      user.pointsBalance -= pointsRequired;
      await queryRunner.manager.save(user);

      // Tạo lịch sử mua gói dịch vụ
      const orderHistory = this.orderPlanHistoryRepository.create({
        userId,
        planId: plan.id,
        planPricingId,
        subscriptionId: savedSubscription.id,
        planName: plan.name,
        point: pointsRequired.toString(),
        billingCycle: planPricing.billingCycle,
        usageLimit: planPricing.usageLimit
          ? planPricing.usageLimit.toString()
          : null,
        usageUnit: planPricing.usageUnit,
        createdAt: now.toString(),
      });

      await queryRunner.manager.save(orderHistory);

      // Commit transaction
      await queryRunner.commitTransaction();

      return savedSubscription;
    } catch (error) {
      // Rollback transaction nếu có lỗi
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Giải phóng queryRunner
      await queryRunner.release();
    }
  }
}
