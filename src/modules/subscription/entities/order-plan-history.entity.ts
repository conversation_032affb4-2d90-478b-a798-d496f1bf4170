import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  BaseEntity, // Optional: if you want Active Record pattern
} from 'typeorm';
import {OrderStatus} from "@modules/subscription/enums/order-status.enum";

// Removed User import as we are not using the relation directly

/**
 * Represents the order_plan_history table.
 * Comment from SQL: lịch sử đơn hàng gói dịch vụ
 * This version avoids direct ORM relationship mapping (@ManyToOne).
 */
@Entity('order_plan_history')
export class OrderPlanHistory extends BaseEntity {
  // Optional: extends BaseEntity

  /**
   * Primary Key: Auto-incrementing big integer.
   * Mapped from bigserial. We use 'string' type in JS/TS for safety with large numbers.
   */
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: string;

  /**
   * Plan name.
   * Mapped from varchar(255).
   * Property name is camelCase, mapped to snake_case column name.
   */
  @Column({ name: 'plan_name', type: 'varchar', length: 255, nullable: true })
  planName: string | null;

  /**
   * Points associated with the plan order.
   * Mapped from bigint, default 0, not null.
   * Using 'string' type in JS/TS for safety with large numbers.
   */
  @Column({ type: 'bigint', default: '0', nullable: false })
  point: string;

  /**
   * Foreign Key referencing the User who placed the order.
   * Stored directly as an integer ID.
   * Property name is camelCase, mapped to snake_case column name.
   */
  @Column({ name: 'user_id', type: 'int' }) // Match nullability with the original constraint
  userId: number;

  /**
   * Foreign Key referencing the Plan associated with this order.
   * Stored directly as an integer ID.
   */
  @Column({ name: 'plan_id', type: 'int' })
  planId: number;

  /**
   * Foreign Key referencing the PlanPricing associated with this order.
   * Stored directly as an integer ID.
   */
  @Column({ name: 'plan_pricing_id', type: 'int' })
  planPricingId: number;

  /**
   * Foreign Key referencing the Subscription created or extended by this order.
   * Stored directly as an integer ID.
   */
  @Column({ name: 'subscription_id', type: 'int', nullable: true })
  subscriptionId: number | null;

  /**
   * Billing cycle description.
   * Mapped from varchar(50).
   * Property name is camelCase, mapped to snake_case column name.
   */
  @Column({
    name: 'billing_cycle',
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  billingCycle: string | null;

  /**
   * Usage limit for the plan.
   * Mapped from bigint.
   * Using 'string' type in JS/TS for safety with large numbers.
   * Property name is camelCase, mapped to snake_case column name.
   */
  @Column({ name: 'usage_limit', type: 'bigint', nullable: true })
  usageLimit: string | null;

  /**
   * Unit for the usage limit (e.g., 'requests', 'MB').
   * Mapped from varchar(50).
   * Property name is camelCase, mapped to snake_case column name.
   */
  @Column({ name: 'usage_unit', type: 'varchar', length: 50, nullable: true })
  usageUnit: string | null;

  /**
   * Timestamp when the record was created.
   * Mapped from bigint, not null. Stores a Unix timestamp (e.g., milliseconds).
   * Using 'string' type in JS/TS for safety with large numbers.
   * Property name is camelCase, mapped to snake_case column name.
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: string;

  /**
   * Indicates whether this order is an extension of an existing subscription.
   * Used to differentiate between new subscriptions and renewals/extensions.
   */
  @Column({ name: 'is_extension', type: 'boolean', default: false })
  isExtension: boolean;

  /**
   * Status of the order (PENDING, COMPLETED, FAILED, CANCELLED).
   */
  @Column({
    type: 'enum',
    enum: OrderStatus,
    default: OrderStatus.COMPLETED
  })
  status: OrderStatus;
}
