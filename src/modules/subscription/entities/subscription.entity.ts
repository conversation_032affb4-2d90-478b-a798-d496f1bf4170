import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import {SubscriptionStatus} from "@modules/subscription/enums";

@Entity('subscriptions') // Bảng quản lý gói dịch vụ user đăng ký
export class Subscription {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column()
  userId: number; // Người dùng sở hữu subscription

  @Column()
  planPricingId: number; // Tùy chọn giá/chu kỳ thanh toán cụ thể mà user đăng ký (plan_pricing.id)

  @Column({ type: 'bigint' })
  startDate: number; // Ngày bắt đầu subscription (Unix timestamp)

  @Column({ type: 'bigint' })
  endDate: number; // Ngày hết hạn subscription (Unix timestamp)

  @Column({ type: 'boolean', default: true })
  autoRenew: boolean; // Có tự động gia hạn không (TRUE/FALSE)

  @Column({
    type: 'enum',
    enum: SubscriptionStatus,
    default: SubscriptionStatus.ACTIVE
  })
  status: SubscriptionStatus; // Trạng thái subscription (ACTIVE, CANCELLED,...)

  @Column({ type: 'bigint' })
  createdAt: number; // Thời điểm tạo

  @Column({ type: 'bigint' })
  updatedAt: number; // Thời điểm cập nhật

  @Column({ type: 'bigint', nullable: true })
  usageLimit: number; // Tổng số lượng tài nguyên được phép sử dụng

  @Column({ type: 'bigint', nullable: true })
  currentUsage: number; // Số lượng tài nguyên đã sử dụng

  @Column({ type: 'bigint', nullable: true })
  remainingValue: number; // Số lượng tài nguyên còn lại

  @Column({ length: 50, nullable: true })
  usageUnit: string; // Đơn vị của tài nguyên
}
