import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsNumber, IsOptional, IsPositive } from 'class-validator';
import { Type } from 'class-transformer';
import { BillingCycle, UsageUnit } from '@modules/subscription/enums';

export class UpdatePlanPricingDto {
  @ApiProperty({
    description: 'Chu kỳ thanh toán',
    enum: BillingCycle,
    example: BillingCycle.MONTHLY,
    required: false
  })
  @IsOptional()
  @IsEnum(BillingCycle)
  billingCycle?: BillingCycle;

  @ApiProperty({
    description: 'Giá của gói dịch vụ',
    example: 99.99,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  price?: number;

  @ApiProperty({
    description: 'Giới hạn sử dụng',
    example: 1000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  usageLimit?: number;

  @ApiProperty({
    description: 'Đơn vị sử dụng',
    enum: UsageUnit,
    example: UsageUnit.REQUEST,
    required: false
  })
  @IsOptional()
  @IsEnum(UsageUnit)
  usageUnit?: UsageUnit;

  @ApiProperty({
    description: 'Trạng thái kích hoạt',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isActive?: boolean;

  @ApiProperty({
    description: 'Danh sách ID của các vai trò được phép sử dụng gói này',
    type: [Number],
    example: [1, 2, 3],
    required: false
  })
  @IsOptional()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  roleIds?: number[];
}
