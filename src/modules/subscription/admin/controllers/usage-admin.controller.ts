import { Controller, UseGuards } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>A<PERSON>, ApiTags } from '@nestjs/swagger';
import { UsageAdminService } from '../services/usage-admin.service';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { RolesGuard } from '@modules/auth/guards/roles.guard';
import { Roles } from '@modules/auth/decorators/roles.decorator';

@ApiTags('Subscription - Admin Usage')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard, RolesGuard)
@Roles('ADMIN')
@Controller('admin/subscription/usage')
export class UsageAdminController {
  constructor(private readonly usageAdminService: UsageAdminService) {}
}
