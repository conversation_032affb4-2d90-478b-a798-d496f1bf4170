import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum } from 'class-validator';
import { QueryDto } from '@/common/dto/query.dto';

/**
 * Enum cho các trường có thể sắp xếp
 */
export enum TagSortField {
  ID = 'id',
  NAME = 'name',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * Enum cho hướng sắp xếp
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}

/**
 * DTO cho query parameters khi lấy danh sách tag của user
 */
export class TagQueryDto extends QueryDto {
  /**
   * Tìm kiếm tổng hợp theo tên hoặc ID tag (override từ QueryDto)
   * @example "VIP" hoặc "123"
   */
  @ApiProperty({
    description: 'Tìm kiếm tổng hợp theo tên hoặc ID tag',
    example: 'VIP',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Từ khóa tìm kiếm phải là chuỗi' })
  declare search?: string;

  /**
   * Tìm kiếm theo tên tag (deprecated - sử dụng search thay thế)
   * @example "VIP"
   * @deprecated Sử dụng search thay thế
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tên tag (deprecated - sử dụng search thay thế)',
    example: 'VIP',
    required: false,
    deprecated: true,
  })
  @IsOptional()
  @IsString({ message: 'Tên phải là chuỗi' })
  name?: string;

  /**
   * Trường cần sắp xếp
   * @example "createdAt"
   */
  @ApiProperty({
    description: 'Trường cần sắp xếp',
    enum: TagSortField,
    example: TagSortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(TagSortField, { message: 'Trường sắp xếp không hợp lệ' })
  sortBy?: TagSortField = TagSortField.CREATED_AT;

  /**
   * Hướng sắp xếp
   * @example "DESC"
   */
  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection, { message: 'Hướng sắp xếp không hợp lệ' })
  sortDirection?: SortDirection = SortDirection.DESC;
}
