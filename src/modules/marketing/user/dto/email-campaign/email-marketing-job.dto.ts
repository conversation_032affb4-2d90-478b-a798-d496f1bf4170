import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho job email marketing gửi vào queue (single email)
 */
export interface EmailMarketingJobDto {
  /**
   * ID của campaign
   */
  campaignId: number;

  /**
   * ID của audience nhận email
   */
  audienceId: number;

  /**
   * Email người nhận
   */
  email: string;

  /**
   * Tiêu đề email (có thể chứa {{variable}})
   */
  subject: string;

  /**
   * Nội dung HTML (có thể chứa {{variable}})
   */
  content: string;

  /**
   * Dữ liệu để thay thế variables
   */
  customFields: Record<string, any>;

  /**
   * Cấu hình SMTP server (optional)
   */
  server?: {
    host?: string;
    port?: number;
    secure?: boolean;
    user?: string;
    password?: string;
    from?: string;
  };

  /**
   * ID tracking duy nhất
   */
  trackingId: string;

  /**
   * Timestamp tạo job
   */
  createdAt: number;
}

/**
 * DTO cho recipient trong batch email job
 */
export interface EmailRecipientDto {
  /**
   * ID của audience nhận email
   */
  audienceId: number;

  /**
   * Email người nhận
   */
  email: string;
}

/**
 * DTO cho batch email marketing job (chứa nhiều email)
 */
export interface BatchEmailMarketingJobDto {
  /**
   * ID của campaign
   */
  campaignId: number;

  /**
   * ID của template email (worker sẽ lấy subject/content từ template)
   */
  templateId: number;

  /**
   * Template variables áp dụng cho tất cả recipients (worker sẽ kết hợp với custom fields của từng audience)
   */
  templateVariables: Record<string, any>;

  /**
   * Danh sách người nhận
   */
  recipients: EmailRecipientDto[];

  /**
   * Cấu hình SMTP server (optional)
   */
  server?: {
    host?: string;
    port?: number;
    secure?: boolean;
    user?: string;
    password?: string;
    from?: string;
  };

  /**
   * Timestamp tạo job
   */
  createdAt: number;
}

/**
 * DTO cho phản hồi tạo email campaign
 */
export class CreateEmailCampaignResponseDto {
  /**
   * ID của campaign đã tạo
   * @example 1
   */
  @ApiProperty({
    description: 'ID của campaign đã tạo',
    example: 1,
  })
  campaignId: number;

  /**
   * Số lượng job đã tạo
   * @example 150
   */
  @ApiProperty({
    description: 'Số lượng job đã tạo',
    example: 150,
  })
  jobCount: number;

  /**
   * Danh sách ID của các job đã tạo
   * @example ["job_1", "job_2", "job_3"]
   */
  @ApiProperty({
    description: 'Danh sách ID của các job đã tạo',
    example: ['job_1', 'job_2', 'job_3'],
    type: [String],
  })
  jobIds: string[];

  /**
   * Thời gian dự kiến gửi (Unix timestamp)
   * @example 1703980800
   */
  @ApiProperty({
    description: 'Thời gian dự kiến gửi (Unix timestamp)',
    example: 1703980800,
    required: false,
  })
  scheduledAt?: number;

  /**
   * Trạng thái campaign
   * @example "SCHEDULED"
   */
  @ApiProperty({
    description: 'Trạng thái campaign',
    example: 'SCHEDULED',
  })
  status: string;
}
