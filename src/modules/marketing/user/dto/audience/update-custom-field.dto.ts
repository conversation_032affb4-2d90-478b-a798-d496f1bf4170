import { IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho việc cập nhật giá trị trường tùy chỉnh
 */
export class UpdateCustomFieldDto {
  /**
   * <PERSON><PERSON><PERSON> trị mới của trường tùy chỉnh
   * @example "Hồ Chí Minh, Việt Nam"
   */
  @ApiProperty({
    description: 'Giá trị mới của trường tùy chỉnh',
    example: '<PERSON><PERSON>, Việt Nam',
  })
  @IsNotEmpty({ message: '<PERSON>i<PERSON> trị trường không được để trống' })
  fieldValue: any;
}
