# API Marketing Overview

## Tổng quan

Module này cung cấp 2 API chính để lấy thông tin tổng quan về marketing và danh sách templates gần đây.

## Endpoints

### 1. Marketing Overview

```
GET /v1/user/marketing/overview
```

#### Authentication
Yêu cầu JWT token của user trong header:
```
Authorization: Bearer <jwt_token>
```

#### Response
```typescript
{
  "success": true,
  "message": "Thông tin overview marketing",
  "result": {
    "totalTemplates": 25,        // Tổng số templates
    "openRate": 24.5,           // Tỷ lệ mở email (%)
    "clickRate": 3.2,           // Tỷ lệ click email (%)
    "totalEmailsSent": 1250     // Tổng số email đã gửi
  }
}
```

#### Cách tính toán metrics:

- **totalTemplates**: Đếm từ bảng `user_template_email` theo `userId`
- **totalEmailsSent**: Đ<PERSON><PERSON> từ `user_campaign_history` với status `SENT`, `DELIVERED`, `OPENED`, `CLICKED`
- **openRate**: `(số email OPENED + CLICKED) / tổng email đã gửi * 100`
- **clickRate**: `số email CLICKED / tổng email đã gửi * 100`

### 2. Recent Templates

```
GET /v1/user/marketing/overview/recent-templates
```

#### Authentication
Yêu cầu JWT token của user trong header:
```
Authorization: Bearer <jwt_token>
```

#### Response
```typescript
{
  "success": true,
  "message": "Danh sách templates gần đây",
  "result": {
    "templates": [
      {
        "id": 1,
        "name": "Welcome Email Template",
        "subject": "Chào mừng bạn đến với dịch vụ của chúng tôi",
        "status": "ACTIVE",
        "createdAt": 1619171200
      },
      {
        "id": 2,
        "name": "Newsletter Template",
        "subject": "Tin tức mới nhất từ chúng tôi",
        "status": "DRAFT",
        "createdAt": 1619171100
      }
      // ... tối đa 5 templates
    ]
  }
}
```

#### Template Status:
- `DRAFT`: Bản nháp
- `ACTIVE`: Đang hoạt động
- `INACTIVE`: Không hoạt động

## Ví dụ sử dụng

### 1. Lấy thông tin overview

```bash
curl -X GET /v1/user/marketing/overview \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json"
```

### 2. Lấy templates gần đây

```bash
curl -X GET /v1/user/marketing/overview/recent-templates \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json"
```

## Cấu trúc dữ liệu

### Bảng liên quan:

1. **user_template_email**: Lưu thông tin templates
   - `id`, `user_id`, `name`, `subject`, `status`, `created_at`

2. **user_campaigns**: Lưu thông tin campaigns
   - `id`, `user_id`, `title`, `status`, `created_at`

3. **user_campaign_history**: Lưu lịch sử gửi email
   - `id`, `campaign_id`, `audience_id`, `status`, `sent_at`

### Status trong campaign_history:
- `PENDING`: Đang chờ gửi
- `SENT`: Đã gửi
- `DELIVERED`: Đã giao
- `FAILED`: Thất bại
- `OPENED`: Đã mở
- `CLICKED`: Đã click

## Lưu ý

1. API chỉ trả về dữ liệu của user hiện tại (dựa trên JWT token)
2. Metrics được tính toán real-time từ database
3. Recent templates được sắp xếp theo `created_at` giảm dần
4. Tỷ lệ được làm tròn đến 1 chữ số thập phân
