# Zalo Webhook Controller Documentation

## Tổng quan

`ZaloWebhookController` là controller xử lý các webhook từ Zalo Official Account. Controller n<PERSON><PERSON> nhận và xử lý các sự kiện từ Zalo như tin nhắn, follow/unfollow, và các tương tác khác từ người dùng.

## Endpoint

### POST /marketing/zalo/webhook

**Mô tả**: Endpoint để nhận và xử lý các sự kiện webhook từ Zalo Official Account

**Authentication**: Không yêu cầu JWT (được gọi từ Zalo servers)

**Headers**:
- `X-ZEvent-Signature`: Chữ ký MAC để xác thực webhook từ Zalo (required)
- `X-ZEvent-Timestamp`: Timestamp của sự kiện webhook (required)

## Request Body

```json
{
  "event_name": "user_send_text",
  "timestamp": "*************",
  "oa_id": "**********",
  "data": {
    "sender": {
      "id": "user_id_123"
    },
    "recipient": {
      "id": "oa_id_456"
    },
    "message": {
      "text": "Hello from user"
    }
  },
  "app_id": "your_app_id",
  "mac": "signature_hash"
}
```

## Response

### Success Response (200)

```json
{
  "code": 200,
  "message": "Xử lý webhook Zalo thành công",
  "result": {
    "success": true,
    "message": "Webhook processed successfully",
    "data": {
      "processed": true,
      "event": "user_send_text"
    }
  }
}
```

### Error Response

```json
{
  "code": 200,
  "message": "Lỗi xử lý webhook Zalo",
  "result": {
    "success": false,
    "message": "Lỗi xử lý webhook: Invalid signature"
  }
}
```

## Các loại sự kiện được hỗ trợ

### 1. user_send_text
Người dùng gửi tin nhắn text đến Official Account

### 2. user_send_image
Người dùng gửi hình ảnh đến Official Account

### 3. user_send_file
Người dùng gửi file đến Official Account

### 4. user_follow
Người dùng follow Official Account

### 5. user_unfollow
Người dùng unfollow Official Account

### 6. user_send_sticker
Người dùng gửi sticker đến Official Account

### 7. user_send_location
Người dùng gửi vị trí đến Official Account

## Cấu trúc Code

### Controller Structure

```typescript
@ApiTags(SWAGGER_API_TAGS.ZALO)
@Controller('marketing/zalo/webhook')
export class ZaloWebhookController {
  // Main webhook handler
  @Post()
  async handleWebhook(
    @Headers('X-ZEvent-Signature') signature: string,
    @Headers('X-ZEvent-Timestamp') timestamp: string,
    @Body() webhookData: ZaloWebhookDataDto,
  ): Promise<ApiResponseDto<ZaloWebhookResponseDto>>

  // Event processing methods (all with empty logic)
  private async processWebhookEvent(webhookData: ZaloWebhookDataDto)
  private async handleUserSendText(oaId: string, data: any)
  private async handleUserSendImage(oaId: string, data: any)
  private async handleUserSendFile(oaId: string, data: any)
  private async handleUserFollow(oaId: string, data: any)
  private async handleUserUnfollow(oaId: string, data: any)
  private async handleUserSendSticker(oaId: string, data: any)
  private async handleUserSendLocation(oaId: string, data: any)
  private verifyWebhookSignature(signature: string, timestamp: string, data: any)
}
```

### DTOs

#### ZaloWebhookDataDto
```typescript
export class ZaloWebhookDataDto {
  event_name: string;
  timestamp: string;
  oa_id: string;
  data: any;
  app_id: string;
  mac: string;
}
```

#### ZaloWebhookResponseDto
```typescript
export class ZaloWebhookResponseDto {
  success: boolean;
  message: string;
  data?: any;
}
```

## Trạng thái hiện tại

### ✅ Đã hoàn thành:
- Controller structure với đầy đủ endpoints
- Swagger documentation
- Error handling cơ bản
- Logging cho debugging
- Event routing dựa trên event_name
- DTOs cho request/response

### 🔄 TODO (Logic rỗng):
- **Signature verification**: Xác thực chữ ký webhook từ Zalo
- **Event processing**: Xử lý logic cho từng loại sự kiện
- **Database operations**: Lưu trữ tin nhắn, cập nhật follower status
- **Automation triggers**: Kích hoạt automation dựa trên sự kiện
- **Agent integration**: Tích hợp với AI agent để phản hồi tự động
- **Error handling**: Xử lý lỗi chi tiết cho từng trường hợp

## Cách triển khai logic

### 1. Signature Verification
```typescript
private verifyWebhookSignature(signature: string, timestamp: string, data: any): boolean {
  // Implement HMAC-SHA256 verification using Zalo webhook secret
  const secret = this.configService.get('ZALO_WEBHOOK_SECRET');
  const payload = timestamp + JSON.stringify(data);
  const expectedSignature = crypto.createHmac('sha256', secret).update(payload).digest('hex');
  return signature === expectedSignature;
}
```

### 2. Event Processing Example
```typescript
private async handleUserSendText(oaId: string, data: any): Promise<any> {
  // 1. Save message to database
  // 2. Update user interaction history
  // 3. Trigger automation rules
  // 4. Send to AI agent for processing
  // 5. Send auto-reply if configured
}
```

### 3. Database Integration
```typescript
// Inject repositories
constructor(
  private readonly zaloMessageRepository: ZaloMessageRepository,
  private readonly zaloFollowerRepository: ZaloFollowerRepository,
  private readonly zaloAutomationService: ZaloAutomationService,
) {}
```

## Security Considerations

1. **Signature Verification**: Luôn xác thực chữ ký webhook
2. **Rate Limiting**: Implement rate limiting để tránh spam
3. **Input Validation**: Validate tất cả input data
4. **Error Logging**: Log chi tiết để debug và monitor

## Testing

### Webhook Testing với ngrok
```bash
# Install ngrok
npm install -g ngrok

# Expose local server
ngrok http 3000

# Configure webhook URL in Zalo Developer Console
# https://your-ngrok-url.ngrok.io/marketing/zalo/webhook
```

### Manual Testing
```bash
curl -X POST http://localhost:3000/marketing/zalo/webhook \
  -H "Content-Type: application/json" \
  -H "X-ZEvent-Signature: test_signature" \
  -H "X-ZEvent-Timestamp: *************" \
  -d '{
    "event_name": "user_send_text",
    "timestamp": "*************",
    "oa_id": "**********",
    "data": {
      "sender": {"id": "user_123"},
      "message": {"text": "Hello"}
    },
    "app_id": "test_app",
    "mac": "test_mac"
  }'
```

## Monitoring & Logging

Controller sử dụng NestJS Logger để ghi log:
- Webhook events received
- Processing results
- Errors and exceptions
- Debug information

Logs có thể được monitor qua:
- Application logs
- Error tracking services
- Webhook delivery status in Zalo Developer Console

## Next Steps

1. Implement signature verification
2. Add database operations for each event type
3. Integrate with existing Zalo services
4. Add automation rule processing
5. Implement AI agent integration
6. Add comprehensive error handling
7. Add monitoring and alerting
