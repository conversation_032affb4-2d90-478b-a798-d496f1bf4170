### Test Zalo Overview APIs
### Đây là file test cho các API overview của Zalo

### Variables
@baseUrl = http://localhost:3000/api
@token = YOUR_JWT_TOKEN_HERE
@oaId = *********
@adsAccountId = ads_account_123

### 1. Test Zalo Official Account Overview - Basic
GET {{baseUrl}}/marketing/zalo/{{oaId}}/statistics
Authorization: Bearer {{token}}
Content-Type: application/json

### 2. Test Zalo Official Account Overview - With Date Range
GET {{baseUrl}}/marketing/zalo/{{oaId}}/statistics?startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer {{token}}
Content-Type: application/json

### 3. Test Zalo Official Account Overview - Last 7 days
GET {{baseUrl}}/marketing/zalo/{{oaId}}/statistics?startDate=2024-01-25&endDate=2024-01-31
Authorization: Bearer {{token}}
Content-Type: application/json

### 4. Test Zalo Ads Overview - Basic
GET {{baseUrl}}/marketing/zalo/ads/overview
Authorization: Bearer {{token}}
Content-Type: application/json

### 5. Test Zalo Ads Overview - With Date Range
GET {{baseUrl}}/marketing/zalo/ads/overview?startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer {{token}}
Content-Type: application/json

### 6. Test Zalo Ads Overview - Specific Ads Account
GET {{baseUrl}}/marketing/zalo/ads/overview?adsAccountId={{adsAccountId}}
Authorization: Bearer {{token}}
Content-Type: application/json

### 7. Test Zalo Ads Overview - Specific Ads Account with Date Range
GET {{baseUrl}}/marketing/zalo/ads/overview?adsAccountId={{adsAccountId}}&startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer {{token}}
Content-Type: application/json

### 8. Test Error Cases - Invalid OA ID
GET {{baseUrl}}/marketing/zalo/invalid_oa_id/statistics
Authorization: Bearer {{token}}
Content-Type: application/json

### 9. Test Error Cases - Invalid Date Format
GET {{baseUrl}}/marketing/zalo/{{oaId}}/statistics?startDate=invalid-date&endDate=2024-01-31
Authorization: Bearer {{token}}
Content-Type: application/json

### 10. Test Error Cases - Unauthorized (No Token)
GET {{baseUrl}}/marketing/zalo/{{oaId}}/statistics
Content-Type: application/json

### Expected Response Format for OA Statistics:
# {
#   "success": true,
#   "message": "Lấy thống kê tích hợp Zalo thành công",
#   "data": {
#     "totalOfficialAccounts": 3,
#     "totalFollowers": 1250,
#     "totalCampaigns": 15,
#     "totalMessagesSent": 5420,
#     "totalZnsSent": 1200,
#     "interactionRate": 12.5,
#     "followerGrowth": [
#       {
#         "date": "2024-01-01",
#         "count": 25
#       }
#     ],
#     "messageStats": {
#       "sent": 5420,
#       "delivered": 5380,
#       "read": 4200,
#       "failed": 40
#     },
#     "znsStats": {
#       "sent": 1200,
#       "delivered": 1180,
#       "read": 950,
#       "failed": 20
#     }
#   }
# }

### Expected Response Format for Ads Overview:
# {
#   "success": true,
#   "message": "Lấy thống kê tổng quan Zalo Ads thành công",
#   "data": {
#     "totalAdsAccounts": 2,
#     "totalAdsCampaigns": 8,
#     "totalSpent": ********,
#     "totalRevenue": ********,
#     "totalImpressions": 1250000,
#     "totalClicks": 12500,
#     "roas": 3.5,
#     "ctr": 1.0,
#     "avgCpc": 1200,
#     "avgCpm": 12000
#   }
# }
