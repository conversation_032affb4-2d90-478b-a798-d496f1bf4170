import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository } from 'typeorm';
import { ZaloAdsCampaign } from '../entities/zalo-ads-campaign.entity';

/**
 * Repository cho ZaloAdsCampaign
 */
@Injectable()
export class ZaloAdsCampaignRepository {
  constructor(
    @InjectRepository(ZaloAdsCampaign)
    private readonly repository: Repository<ZaloAdsCampaign>,
  ) {}

  /**
   * Tìm kiếm nhiều Ads Campaign
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách Ads Campaign
   */
  async find(options?: FindManyOptions<ZaloAdsCampaign>): Promise<ZaloAdsCampaign[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một Ads Campaign
   * @param options Tùy chọn tìm kiếm
   * @returns Ads Campaign hoặc null
   */
  async findOne(options?: FindOneOptions<ZaloAdsCampaign>): Promise<ZaloAdsCampaign | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Tìm Ads Campaign theo ID
   * @param id ID của Ads Campaign
   * @returns Ads Campaign hoặc null
   */
  async findById(id: number): Promise<ZaloAdsCampaign | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Tìm tất cả Ads Campaign của một người dùng
   * @param userId ID của người dùng
   * @returns Danh sách Ads Campaign
   */
  async findByUserId(userId: number): Promise<ZaloAdsCampaign[]> {
    return this.repository.find({ where: { userId } });
  }

  /**
   * Tìm tất cả Ads Campaign của một tài khoản Ads
   * @param adsAccountId ID của tài khoản Ads
   * @returns Danh sách Ads Campaign
   */
  async findByAdsAccountId(adsAccountId: string): Promise<ZaloAdsCampaign[]> {
    return this.repository.find({ where: { adsAccountId } });
  }

  /**
   * Tạo mới Ads Campaign
   * @param data Dữ liệu Ads Campaign
   * @returns Ads Campaign đã tạo
   */
  async create(data: Partial<ZaloAdsCampaign>): Promise<ZaloAdsCampaign> {
    const adsCampaign = this.repository.create(data);
    return this.repository.save(adsCampaign);
  }

  /**
   * Cập nhật Ads Campaign
   * @param id ID của Ads Campaign
   * @param data Dữ liệu cập nhật
   * @returns Ads Campaign đã cập nhật
   */
  async update(id: number, data: Partial<ZaloAdsCampaign>): Promise<ZaloAdsCampaign | null> {
    await this.repository.update(id, data);
    return this.findById(id);
  }

  /**
   * Xóa Ads Campaign
   * @param id ID của Ads Campaign
   * @returns true nếu xóa thành công
   */
  async delete(id: number): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }

  /**
   * Đếm số lượng Ads Campaign
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng Ads Campaign
   */
  async count(options?: FindManyOptions<ZaloAdsCampaign>): Promise<number> {
    return this.repository.count(options);
  }
}
