import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, <PERSON>ToOne, PrimaryGeneratedColumn } from 'typeorm';
import { User } from '@/modules/user/entities/user.entity';

/**
 * Entity đại diện cho bảng user_segments trong cơ sở dữ liệu
 * Phân khúc khách hàng của người dùng
 */
@Entity('user_segments')
export class UserSegment {
  /**
   * ID của segment
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', comment: 'Mã khách hàng' })
  userId: number;

  /**
   * <PERSON>uan hệ với bảng User
   */
  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  /**
   * Tên segment
   */
  @Column({ name: 'name', length: 255, nullable: true, comment: '<PERSON><PERSON><PERSON> tập khách hàng' })
  name: string;

  /**
   * <PERSON><PERSON> tả segment
   */
  @Column({ name: 'description', type: 'text', nullable: true, comment: 'Mô tả' })
  description: string;

  /**
   * Điều kiện lọc khách hàng
   */
  @Column({ name: 'criteria', type: 'jsonb', nullable: true, comment: 'Lưu trữ điều kiện lọc khách hàng khi tạo segment' })
  criteria: any;

  /**
   * Số lượng audience trong segment
   */
  @Column({ name: 'audience_count', type: 'int', default: 0, comment: 'Số lượng audience trong segment' })
  audienceCount: number;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true, comment: 'Thời gian tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Thời gian cập nhật' })
  updatedAt: number;
}
