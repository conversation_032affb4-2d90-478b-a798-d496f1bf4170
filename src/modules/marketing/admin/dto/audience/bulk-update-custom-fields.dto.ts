import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayNotEmpty, IsArray, ValidateNested } from 'class-validator';
import { CreateCustomFieldDto } from './create-custom-field.dto';

/**
 * DTO cho việc cập nhật hàng loạt các trường tùy chỉnh của audience (Admin)
 * Sẽ thay thế hoàn toàn các giá trị hiện có
 */
export class BulkUpdateCustomFieldsDto {
  /**
   * Danh sách các trường tùy chỉnh mới
   * @example [{ fieldName: "Địa chỉ", fieldValue: "Hà Nội", fieldType: "TEXT" }, { fieldName: "Tuổi", fieldValue: "30", fieldType: "NUMBER" }]
   */
  @ApiProperty({
    description: 'Danh sách các trường tùy chỉnh mới',
    type: [CreateCustomFieldDto],
    example: [
      { fieldName: 'Địa chỉ', fieldValue: 'H<PERSON> Nội', fieldType: 'TEXT' },
      { fieldName: 'Tuổi', fieldValue: '30', fieldType: 'NUMBER' },
    ],
  })
  @IsArray({ message: 'Danh sách trường tùy chỉnh phải là một mảng' })
  @ArrayNotEmpty({ message: 'Danh sách trường tùy chỉnh không được để trống' })
  @ValidateNested({ each: true })
  @Type(() => CreateCustomFieldDto)
  fields: CreateCustomFieldDto[];
}
