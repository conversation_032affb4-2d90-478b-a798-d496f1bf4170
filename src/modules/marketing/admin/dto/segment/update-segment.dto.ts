import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho cập nhật segment
 */
export class UpdateSegmentDto {
  /**
   * Tên segment
   * @example "Khách hàng VIP"
   */
  @ApiProperty({
    description: 'Tên segment',
    example: 'Khách hàng VIP',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên segment phải là chuỗi' })
  @MaxLength(255, { message: 'Tên segment không được vượt quá 255 ký tự' })
  name?: string;

  /**
   * Mô tả segment
   * @example "Khách hàng có tổng chi tiêu trên 10 triệu"
   */
  @ApiProperty({
    description: 'Mô tả segment',
    example: 'Khách hàng có tổng chi tiêu trên 10 triệu',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '<PERSON><PERSON> tả segment phải là chuỗi' })
  description?: string;

  /**
   * Ti<PERSON>u chí lọc khách hàng
   */
  @ApiProperty({
    description: 'Tiêu chí lọc khách hàng',
    example: {
      conditions: [
        { field: 'totalSpent', operator: 'greaterThan', value: 10000000 }
      ],
      operator: 'AND'
    },
    required: false,
  })
  @IsOptional()
  criteria?: any;
}
