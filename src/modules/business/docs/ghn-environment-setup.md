# Hướng dẫn cấu hình Environment Variables cho GHN

## 🔧 Cấu hình cơ bản

### 1. Thêm vào file `.env`

```bash
# GHN Shipping Configuration
# IMPORTANT: Get real token and shop ID from: https://sso.ghn.vn/ > Settings > API
# Replace with your actual GHN token and shop ID
GHN_TOKEN=your_real_ghn_token_here
GHN_SHOP_ID=your_real_shop_id_here
GHN_BASE_URL=https://dev-online-gateway.ghn.vn
GHN_TIMEOUT=30000
GHN_TEST_MODE=true
```

### 2. Mô tả các biến môi trường

| Biến | Bắt buộc | Mô tả | Giá trị mặc định |
|------|----------|-------|------------------|
| `GHN_TOKEN` | ✅ | Token API từ GHN | `your_real_ghn_token_here` |
| `GHN_SHOP_ID` | ✅ | Shop ID từ GHN | `your_real_shop_id_here` |
| `GHN_BASE_URL` | ❌ | URL API GHN | `https://dev-online-gateway.ghn.vn` |
| `GHN_TIMEOUT` | ❌ | Timeout (ms) | `30000` |
| `GHN_TEST_MODE` | ❌ | Chế độ test | `true` |

## 🔑 Lấy Token và Shop ID từ GHN

### Bước 1: Đăng ký tài khoản GHN

1. Truy cập [https://sso.ghn.vn/](https://sso.ghn.vn/)
2. Đăng ký tài khoản doanh nghiệp
3. Xác thực thông tin và tài liệu

### Bước 2: Tạo cửa hàng

1. Đăng nhập vào tài khoản GHN
2. Vào **Quản lý cửa hàng** > **Tạo cửa hàng**
3. Điền thông tin cửa hàng và địa chỉ lấy hàng
4. Chờ GHN duyệt cửa hàng

### Bước 3: Lấy thông tin API

1. Sau khi cửa hàng được duyệt
2. Vào **Cài đặt** > **Thông tin API**
3. Copy **Token** và **Shop ID**
4. Cập nhật vào file `.env`

## 🌍 Cấu hình theo môi trường

### Development (.env.development)

```bash
# GHN Development Configuration
GHN_TOKEN=your_real_ghn_token_here
GHN_SHOP_ID=your_real_shop_id_here
GHN_BASE_URL=https://dev-online-gateway.ghn.vn
GHN_TIMEOUT=30000
GHN_TEST_MODE=true
```

### Staging (.env.staging)

```bash
# GHN Staging Configuration
GHN_TOKEN=your_staging_token_here
GHN_SHOP_ID=your_staging_shop_id_here
GHN_BASE_URL=https://dev-online-gateway.ghn.vn
GHN_TIMEOUT=30000
GHN_TEST_MODE=true
```

### Production (.env.production)

```bash
# GHN Production Configuration
GHN_TOKEN=your_production_token_here
GHN_SHOP_ID=your_production_shop_id_here
GHN_BASE_URL=https://online-gateway.ghn.vn
GHN_TIMEOUT=30000
GHN_TEST_MODE=false
```

## ⚠️ Lưu ý bảo mật

### 1. Không commit thông tin nhạy cảm

Thêm vào `.gitignore`:

```gitignore
# Environment files
.env
.env.local
.env.development
.env.staging
.env.production

# Backup files
.env.backup
.env.*.backup
```

### 2. Sử dụng Secret Management

**Production environments:**
- AWS Secrets Manager
- Azure Key Vault
- Google Secret Manager
- Kubernetes Secrets

**Example với Kubernetes:**

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: ghn-secrets
type: Opaque
data:
  GHN_TOKEN: <base64-encoded-token>
  GHN_SHOP_ID: <base64-encoded-shop-id>
```

### 3. Rotate credentials định kỳ

- Thay đổi token GHN định kỳ (3-6 tháng)
- Update environment variables
- Restart services
- Monitor logs for authentication errors

## 🔍 Validation và Debugging

### 1. Kiểm tra cấu hình

Service sẽ tự động log trạng thái cấu hình:

```typescript
// Log khi khởi tạo service
this.logger.log('Cấu hình GHN hợp lệ', {
  baseUrl: config.baseUrl,
  isTestMode: config.isTestMode,
  hasToken: !!config.token,
  hasShopId: !!config.shopId,
  tokenSource: token ? 'environment' : 'default',
  shopIdSource: shopId ? 'environment' : 'default'
});
```

### 2. Cảnh báo khi sử dụng giá trị mặc định

```bash
[WARN] GHN_TOKEN không được cấu hình, sử dụng giá trị mặc định
[WARN] Đang sử dụng token GHN mặc định - cần cập nhật token thực từ GHN
```

### 3. Debug mode

Enable debug logging:

```bash
LOG_LEVEL=debug
```

## 🐳 Docker Configuration

### docker-compose.yml

```yaml
version: '3.8'
services:
  app:
    build: .
    environment:
      - GHN_TOKEN=${GHN_TOKEN}
      - GHN_SHOP_ID=${GHN_SHOP_ID}
      - GHN_BASE_URL=${GHN_BASE_URL}
      - GHN_TIMEOUT=${GHN_TIMEOUT}
      - GHN_TEST_MODE=${GHN_TEST_MODE}
    env_file:
      - .env
```

### Dockerfile

```dockerfile
# Environment variables can be set at build time
ARG GHN_TOKEN
ARG GHN_SHOP_ID

ENV GHN_TOKEN=${GHN_TOKEN}
ENV GHN_SHOP_ID=${GHN_SHOP_ID}
```

## 🚨 Troubleshooting

### 1. Token không hợp lệ

```bash
Error: GHN_INVALID_TOKEN - Token GHN không hợp lệ
```

**Giải pháp:**
- Kiểm tra `GHN_TOKEN` trong `.env`
- Verify token trên GHN dashboard
- Đảm bảo token chưa hết hạn

### 2. Shop ID sai

```bash
Error: GHN_INVALID_SHOP_ID - Shop ID GHN không hợp lệ
```

**Giải pháp:**
- Kiểm tra `GHN_SHOP_ID`
- Đảm bảo shop đã được kích hoạt
- Liên hệ GHN support

### 3. Môi trường sai

```bash
Error: Không thể tạo đơn hàng
```

**Giải pháp:**
- Kiểm tra `GHN_TEST_MODE` setting
- Đảm bảo sử dụng đúng `GHN_BASE_URL`
- Verify token và shop ID cho môi trường tương ứng

### 4. Network issues

```bash
Error: GHN_NETWORK_ERROR - Lỗi kết nối mạng với GHN
```

**Giải pháp:**
- Kiểm tra internet connection
- Verify GHN API status
- Kiểm tra firewall settings

## 📊 Monitoring

### 1. Health Check

```typescript
// Kiểm tra cấu hình định kỳ
async healthCheck(): Promise<boolean> {
  try {
    const config = this.ghnService.getConfig();
    return config.token !== GHN_TEST_CONFIG.TOKEN;
  } catch (error) {
    return false;
  }
}
```

### 2. Alerts

Thiết lập alerts cho:
- Token sắp hết hạn
- API rate limit exceeded
- Connection failures
- Invalid configuration

### 3. Metrics

Track metrics:
- API response times
- Success/failure rates
- Token usage
- Configuration changes

## 📞 Support

### GHN Support
- Hotline: 1900 636 677
- Email: <EMAIL>
- Website: https://ghn.vn

### Documentation
- API Documentation: https://api.ghn.vn/home/<USER>/detail
- Developer Portal: https://ghn.vn/pages/dev
