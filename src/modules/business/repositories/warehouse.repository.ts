import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import {
  Warehouse,
  PhysicalWarehouse, // From admin
  VirtualWarehouse, // From admin
} from '@modules/business/entities';
import { QueryWarehouseDto as QueryWarehouseDtoUser } from '@modules/business/user/dto/warehouse'; // Aliased DTO
import { QueryWarehouseDto as QueryWarehouseDtoAdmin } from '@modules/business/admin/dto/warehouse/query-warehouse.dto'; // Aliased DTO
import { PaginatedResult } from '@common/response'; // User's path is simpler
import { WarehouseTypeEnum } from '@modules/business/enums'; // From admin

/**
 * Repository xử lý truy vấn dữ liệu cho entity Warehouse,
 * kết hợp chức năng từ cả user và admin context.
 */
@Injectable()
export class WarehouseRepository extends Repository<Warehouse> {
  private readonly logger = new Logger(WarehouseRepository.name);

  constructor(private dataSource: DataSource) { // Consistent constructor
    super(Warehouse, dataSource.createEntityManager());
  }

  // --- Base Query Builders ---

  /**
   * Tạo query builder cơ bản cho warehouse (User context version)
   * @returns Query builder
   */
  private createBaseQuery_user(): SelectQueryBuilder<Warehouse> {
    return this.createQueryBuilder('warehouse');
  }

  /**
   * Tạo query builder cơ bản cho warehouse (Admin context version)
   * @returns SelectQueryBuilder<Warehouse>
   */
  private createBaseQuery_admin(): SelectQueryBuilder<Warehouse> {
    this.logger.log('(Admin) Tạo query builder cơ bản cho warehouse');
    return this.createQueryBuilder('warehouse');
  }

  // --- Methods with similar purpose but different names (kept distinct) or suffixed ---

  /**
   * Tìm kho theo ID (User context method)
   * @param warehouseId ID của kho
   * @returns Thông tin kho hoặc null nếu không tìm thấy
   */
  async findById_user(warehouseId: number): Promise<Warehouse | null> {
    try {
      return await this.createBaseQuery_user()
        .where('warehouse.warehouseId = :warehouseId', { warehouseId })
        .getOne();
    } catch (error) {
      this.logger.error(`(User) Lỗi khi tìm kho theo ID ${warehouseId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm kho theo ID ${warehouseId}: ${error.message}`);
    }
  }

  /**
   * Tìm kiếm kho theo ID (Admin context method)
   * @param warehouseId ID của kho
   * @returns Kho
   */
  async findByWarehouseId_admin(warehouseId: number): Promise<Warehouse | null> {
    this.logger.log(`(Admin) Tìm kiếm kho với ID: ${warehouseId}`);
    const qb = this.createBaseQuery_admin()
      .where('warehouse.warehouseId = :warehouseId', { warehouseId });

    const sqlQuery = qb.getQuery();
    const params = qb.getParameters();
    this.logger.log(`(Admin) Câu SQL để lấy thông tin kho: ${sqlQuery}`);
    this.logger.log(`(Admin) Tham số truy vấn: ${JSON.stringify(params)}`);

    try {
      const warehouse = await qb.getOne();
      if (warehouse) {
        this.logger.log(`(Admin) Đã tìm thấy kho với ID: ${warehouseId}`);
        this.logger.log(`(Admin) Thông tin kho: ${JSON.stringify(warehouse)}`);
      } else {
        this.logger.log(`(Admin) Không tìm thấy kho với ID: ${warehouseId}`);
      }
      return warehouse;
    } catch (error) {
      this.logger.error(`(Admin) Lỗi khi tìm kiếm kho: ${error.message}`);
      throw error;
    }
  }

  // --- User specific or unique methods ---

  /**
   * Tìm kho theo tên (User context method)
   * @param name Tên kho
   * @returns Thông tin kho hoặc null nếu không tìm thấy
   */
  async findByName(name: string): Promise<Warehouse | null> {
    try {
      return await this.createBaseQuery_user()
        .where('warehouse.name = :name', { name })
        .getOne();
    } catch (error) {
      this.logger.error(`(User) Lỗi khi tìm kho theo tên ${name}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm kho theo tên ${name}: ${error.message}`);
    }
  }

  /**
   * Tìm kiếm kho với các điều kiện lọc và phân trang (User context method)
   * @param queryDto DTO chứa các tham số truy vấn (User DTO)
   * @returns Danh sách kho với phân trang
   */
  async findAll(queryDto: QueryWarehouseDtoUser): Promise<PaginatedResult<Warehouse>> {
    try {
      const {
        page = 1,
        limit = 10,
        offset = (page - 1) * limit,
        search,
        type,
        sortBy = 'warehouseId',
        sortDirection = 'ASC',
      } = queryDto;

      const queryBuilder = this.createBaseQuery_user();

      if (search) {
        queryBuilder.andWhere(
          '(warehouse.name ILIKE :search OR warehouse.description ILIKE :search)',
          { search: `%${search}%` }
        );
      }
      if (type) {
        queryBuilder.andWhere('warehouse.type = :type', { type });
      }

      const total = await queryBuilder.getCount();

      queryBuilder
        .orderBy(`warehouse.${sortBy}`, sortDirection as 'ASC' | 'DESC')
        .skip(offset)
        .take(limit);

      const items = await queryBuilder.getMany();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`(User) Lỗi khi tìm kiếm kho: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm kiếm kho: ${error.message}`);
    }
  }

  // --- Admin specific or unique methods ---

  /**
   * Tìm kiếm danh sách kho với phân trang (Admin context method)
   * @param queryDto DTO truy vấn (Admin DTO)
   * @returns Danh sách kho với phân trang
   */
  async findAllWithPagination(
    queryDto: QueryWarehouseDtoAdmin
  ): Promise<[Warehouse[], number]> { // Return type [Warehouse[], number] as in original
    this.logger.log('(Admin) Tìm kiếm danh sách kho với phân trang');
    const { page, limit, type, search, sortBy, sortDirection } = queryDto;
    const skip = (page - 1) * limit;

    const qb = this.createBaseQuery_admin();

    if (type) {
      qb.andWhere('warehouse.type = :type', { type });
    }
    if (search) {
      qb.andWhere(
        '(warehouse.name ILIKE :search OR warehouse.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    if (sortBy) {
      if (sortBy === 'createdAt' || sortBy === 'updatedAt') {
        qb.orderBy('warehouse.warehouseId', sortDirection);
      } else {
        qb.orderBy(`warehouse.${sortBy}`, sortDirection);
      }
    } else {
      qb.orderBy('warehouse.warehouseId', 'DESC');
    }

    qb.skip(skip).take(limit);
    return qb.getManyAndCount();
  }

  /**
   * Tìm kiếm kho theo loại (Admin context method)
   * @param type Loại kho
   * @returns Danh sách kho
   */
  async findByType(type: WarehouseTypeEnum): Promise<Warehouse[]> {
    this.logger.log(`(Admin) Tìm kiếm kho theo loại: ${type}`);
    const qb = this.createBaseQuery_admin()
      .where('warehouse.type = :type', { type });
    return qb.getMany();
  }

  /**
   * Tìm kiếm thông tin kho vật lý theo ID kho (Admin context method)
   * @param warehouseId ID của kho
   * @returns Thông tin kho vật lý
   */
  async findPhysicalWarehouseByWarehouseId(warehouseId: number): Promise<PhysicalWarehouse | null> {
    this.logger.log(`(Admin) Tìm kiếm thông tin kho vật lý với warehouseId: ${warehouseId}`);
    try {
      const result = await this.dataSource
        .createQueryBuilder()
        .select('pw')
        .from(PhysicalWarehouse, 'pw')
        .where('pw.warehouseId = :warehouseId', { warehouseId })
        .getOne();
      if (result) {
        this.logger.log(`(Admin) Đã tìm thấy thông tin kho vật lý với warehouseId: ${warehouseId}`);
      } else {
        this.logger.warn(`(Admin) Không tìm thấy thông tin kho vật lý với warehouseId: ${warehouseId}`);
      }
      return result;
    } catch (error) {
      this.logger.error(`(Admin) Lỗi khi tìm kiếm thông tin kho vật lý: ${error.message}`, error.stack);
      return null; // Or re-throw, original admin returned null
    }
  }

  /**
   * Tìm kiếm thông tin kho ảo theo ID kho (Admin context method)
   * @param warehouseId ID của kho
   * @returns Thông tin kho ảo
   */
  async findVirtualWarehouseByWarehouseId(warehouseId: number): Promise<VirtualWarehouse | null> {
    this.logger.log(`(Admin) Tìm kiếm thông tin kho ảo với warehouseId: ${warehouseId}`);
    try {
      const result = await this.dataSource
        .createQueryBuilder()
        .select('vw')
        .from(VirtualWarehouse, 'vw')
        .where('vw.warehouseId = :warehouseId', { warehouseId })
        .getOne();
      if (result) {
        this.logger.log(`(Admin) Đã tìm thấy thông tin kho ảo với warehouseId: ${warehouseId}`);
      } else {
        this.logger.warn(`(Admin) Không tìm thấy thông tin kho ảo với warehouseId: ${warehouseId}`);
      }
      return result;
    } catch (error) {
      this.logger.error(`(Admin) Lỗi khi tìm kiếm thông tin kho ảo: ${error.message}`, error.stack);
      return null; // Or re-throw, original admin returned null
    }
  }
}