import { Injectable, Logger } from '@nestjs/common';
import { UserOrder } from '@modules/business/entities';
import { UserProduct } from '@modules/business/entities';
import { UserConvertCustomer } from '@modules/business/entities';
import { IGHTKCreateOrderRequest, IGHTKProduct, IGHTKOrder } from '@modules/business/interfaces';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '../../exceptions';

/**
 * Helper để tích hợp GHTK với hệ thống đơn hàng
 */
@Injectable()
export class GHTKIntegrationHelper {
  private readonly logger = new Logger(GHTKIntegrationHelper.name);

  constructor() {}

  /**
   * Chuyển đổi đơn hàng hệ thống sang format GHTK
   * @param order Đơn hàng hệ thống
   * @param customer Thông tin khách hàng
   * @param products Danh sách sản phẩm
   * @param pickupInfo Thông tin địa chỉ lấy hàng
   * @returns Request data cho GHTK API
   */
  convertOrderToGHTKFormat(
    order: UserOrder,
    customer: UserConvertCustomer,
    products: UserProduct[],
    pickupInfo: {
      name: string;
      address: string;
      province: string;
      district: string;
      ward: string;
      tel: string;
    }
  ): IGHTKCreateOrderRequest {
    try {
      // Chuyển đổi sản phẩm
      const ghtkProducts: IGHTKProduct[] = this.convertProductsToGHTKFormat(order, products);

      // Chuyển đổi thông tin đơn hàng
      const ghtkOrder: IGHTKOrder = this.convertOrderInfoToGHTKFormat(
        order,
        customer,
        pickupInfo
      );

      return {
        products: ghtkProducts,
        order: ghtkOrder
      };
    } catch (error) {
      this.logger.error('Lỗi khi chuyển đổi đơn hàng sang format GHTK:', error);
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_CONFIG_ERROR,
        `Lỗi khi chuyển đổi đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Chuyển đổi danh sách sản phẩm sang format GHTK
   */
  private convertProductsToGHTKFormat(order: UserOrder, products: UserProduct[]): IGHTKProduct[] {
    const productInfo = order.productInfo as any;
    const orderProducts = productInfo?.products || [];

    if (!Array.isArray(orderProducts)) {
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_CONFIG_ERROR,
        'Thông tin sản phẩm trong đơn hàng không hợp lệ'
      );
    }

    return orderProducts.map((orderProduct: any) => {
      const product = products.find(p => p.id === orderProduct.productId);

      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${orderProduct.productId}`
        );
      }

      // Lấy thông tin cân nặng từ shipmentConfig
      const shipmentConfig = product.shipmentConfig as any;
      const weight = shipmentConfig?.weightGram
        ? shipmentConfig.weightGram / 1000 // Chuyển từ gram sang kg
        : 0.5; // Mặc định 0.5kg nếu không có thông tin

      return {
        name: orderProduct.name || product.name,
        price: orderProduct.unitPrice || 0,
        weight: weight,
        quantity: orderProduct.quantity || 1,
        product_code: product.id.toString() // Sử dụng ID sản phẩm làm product_code
      };
    });
  }

  /**
   * Chuyển đổi thông tin đơn hàng sang format GHTK
   */
  private convertOrderInfoToGHTKFormat(
    order: UserOrder,
    customer: UserConvertCustomer,
    pickupInfo: any
  ): IGHTKOrder {
    this.logger.log('Converting order info to GHTK format:', {
      orderId: order.id,
      customerId: customer.id,
      customerName: customer.name,
      customerPhone: customer.phone,
      customerAddress: customer.address
    });

    // Parse địa chỉ giao hàng từ logisticInfo
    const logisticInfo = order.logisticInfo as any;
    let finalAddress;

    // Ưu tiên sử dụng addressDetails nếu có (từ user_addresses table)
    if (logisticInfo?.addressDetails) {
      this.logger.log('Using addressDetails from logisticInfo:', logisticInfo.addressDetails);

      // Parse street address từ full address để lấy phần ngắn gọn
      const streetAddress = this.extractStreetFromFullAddress(logisticInfo.addressDetails.street || '');

      finalAddress = {
        address: streetAddress,
        ward: this.normalizeAddressPart(logisticInfo.addressDetails.ward || ''),
        district: this.normalizeAddressPart(logisticInfo.addressDetails.district || ''),
        province: this.normalizeAddressPart(logisticInfo.addressDetails.province || ''),
        hamlet: logisticInfo.addressDetails.hamlet || 'Khác'
      };
    } else {
      // Fallback: Parse từ deliveryAddress string
      let deliveryAddress;
      try {
        deliveryAddress = this.parseDeliveryAddress(
          (logisticInfo?.deliveryAddress as string) || customer.address || ''
        );
      } catch (error) {
        this.logger.warn('Failed to parse delivery address, using customer info:', error.message);
        // Fallback to customer info if parsing fails
        deliveryAddress = {
          address: customer.address || '',
          ward: '',
          district: '',
          province: '',
          hamlet: 'Khác'
        };
      }

      // Parse street address từ full address nếu cần
      const streetAddress = deliveryAddress.address
        ? this.extractStreetFromFullAddress(deliveryAddress.address)
        : this.extractStreetFromFullAddress(customer.address || '');

      finalAddress = {
        address: streetAddress,
        ward: this.normalizeAddressPart(deliveryAddress.ward || ''),
        district: this.normalizeAddressPart(deliveryAddress.district || ''),
        province: this.normalizeAddressPart(deliveryAddress.province || ''),
        hamlet: deliveryAddress.hamlet || 'Khác'
      };
    }

    // Validate địa chỉ trước khi gửi đến GHTK
    this.validateGHTKAddress(finalAddress);

    this.logger.log('Final delivery address for GHTK:', finalAddress);

    // Tính tổng giá trị đơn hàng
    const orderValue = this.calculateOrderValue(order);

    // Parse bill info
    const billInfo = order.billInfo as any;
    const pickMoney = typeof billInfo?.total === 'number' ? billInfo.total : 0;

    // Parse shipping note
    const shippingNote = typeof logisticInfo?.shippingNote === 'string' ? logisticInfo.shippingNote : '';

    const ghtkOrder = {
      id: `ORDER_${order.id}_${Date.now()}`, // Tạo ID unique cho GHTK

      // Thông tin lấy hàng
      pick_name: pickupInfo.name,
      pick_address: pickupInfo.address,
      pick_province: pickupInfo.province,
      pick_district: pickupInfo.district,
      pick_ward: pickupInfo.ward,
      pick_tel: pickupInfo.tel,

      // Thông tin giao hàng
      name: customer.name,
      address: finalAddress.address,
      province: finalAddress.province,
      district: finalAddress.district,
      ward: finalAddress.ward,
      hamlet: finalAddress.hamlet,
      tel: customer.phone,

      // Thông tin đơn hàng
      value: orderValue,
      pick_money: pickMoney, // COD amount
      note: shippingNote,
      transport: 'road', // Mặc định đường bộ
      pick_option: 'cod', // Mặc định thu hộ
      deliver_option: 'none', // Không sử dụng xTeam
      is_freeship: '0' // Mặc định không freeship
    };

    this.logger.log('GHTK order data to be sent:', ghtkOrder);

    return ghtkOrder;
  }

  /**
   * Chuẩn hóa tên địa chỉ theo format GHTK
   */
  private normalizeAddressPart(addressPart: string): string {
    if (!addressPart) return '';

    const normalized = addressPart.trim();

    // Mapping các tên tỉnh/thành phố theo GHTK
    const provinceMapping: { [key: string]: string } = {
      'Thành phố Huế': 'Thừa Thiên Huế',
      'TP Huế': 'Thừa Thiên Huế',
      'Huế': 'Thừa Thiên Huế',
      'Thành phố Hồ Chí Minh': 'Hồ Chí Minh',
      'TP.HCM': 'Hồ Chí Minh',
      'TP HCM': 'Hồ Chí Minh',
      'Hồ Chí Minh': 'Hồ Chí Minh',
      'Thành phố Hà Nội': 'Hà Nội',
      'TP Hà Nội': 'Hà Nội',
      'Thành phố Đà Nẵng': 'Đà Nẵng',
      'TP Đà Nẵng': 'Đà Nẵng'
    };

    // Mapping các quận/huyện đặc biệt cho Huế
    const districtMapping: { [key: string]: string } = {
      'Quận Phú Xuân': 'Thành phố Huế',
      'Quận Thuận Hóa': 'Thành phố Huế',
      'Huyện Phong Điền': 'Phong Điền',
      'Huyện Quảng Điền': 'Quảng Điền',
      'Huyện Phú Vang': 'Phú Vang'
    };

    // Kiểm tra mapping tỉnh/thành phố
    if (provinceMapping[normalized]) {
      return provinceMapping[normalized];
    }

    // Kiểm tra mapping quận/huyện
    if (districtMapping[normalized]) {
      return districtMapping[normalized];
    }

    return normalized;
  }

  /**
   * Trích xuất địa chỉ street từ full address
   */
  private extractStreetFromFullAddress(fullAddress: string): string {
    if (!fullAddress) return '';

    // Tách địa chỉ theo dấu phẩy
    const parts = fullAddress.split(',').map(part => part.trim());

    // Loại bỏ các phần có chứa từ khóa địa danh
    const locationKeywords = [
      'việt nam', 'vietnam',
      'thành phố', 'tp.', 'tp ',
      'tỉnh', 'quận', 'huyện', 'phường', 'xã',
      'hồ chí minh', 'hà nội', 'đà nẵng', 'huế',
      'thừa thiên huế'
    ];

    const streetParts = parts.filter(part => {
      const lowerPart = part.toLowerCase();
      return !locationKeywords.some(keyword => lowerPart.includes(keyword));
    });

    // Lấy tối đa 2 phần đầu làm street address
    const streetAddress = streetParts.slice(0, 2).join(', ').trim();

    // Nếu không có street address hợp lệ, lấy phần đầu tiên
    if (!streetAddress) {
      return parts[0] || '';
    }

    this.logger.log('Extracted street address:', {
      fullAddress,
      parts,
      streetParts,
      streetAddress
    });

    return streetAddress;
  }

  /**
   * Validate địa chỉ GHTK
   */
  private validateGHTKAddress(address: any): void {
    const errors: string[] = [];

    // Kiểm tra các trường bắt buộc
    if (!address.address || address.address.trim() === '') {
      errors.push('Địa chỉ chi tiết (số nhà, đường) không được để trống');
    }

    if (!address.province || address.province.trim() === '') {
      errors.push('Tỉnh/Thành phố không được để trống');
    }

    if (!address.district || address.district.trim() === '') {
      errors.push('Quận/Huyện không được để trống');
    }

    // Kiểm tra độ dài địa chỉ
    if (address.address && address.address.length > 200) {
      errors.push('Địa chỉ chi tiết quá dài (tối đa 200 ký tự)');
    }

    // Kiểm tra ký tự đặc biệt
    const invalidChars = /[<>\"'&]/;
    if (address.address && invalidChars.test(address.address)) {
      errors.push('Địa chỉ chứa ký tự không hợp lệ');
    }

    // Ward không bắt buộc với GHTK nhưng nên có
    if (!address.ward || address.ward.trim() === '') {
      this.logger.warn('Phường/Xã không có, GHTK có thể từ chối đơn hàng');
    }

    // Log thông tin chi tiết để debug
    this.logger.log('GHTK Address validation details:', {
      address: address.address,
      addressLength: address.address ? address.address.length : 0,
      province: address.province,
      district: address.district,
      ward: address.ward,
      hamlet: address.hamlet
    });

    if (errors.length > 0) {
      const errorMessage = `Địa chỉ giao hàng không hợp lệ: ${errors.join(', ')}`;
      this.logger.error(errorMessage, { address });
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_INVALID_ADDRESS,
        errorMessage
      );
    }
  }

  /**
   * Parse địa chỉ giao hàng từ string
   */
  private parseDeliveryAddress(addressString: string): {
    address: string;
    province: string;
    district: string;
    ward: string;
    hamlet?: string;
  } {
    this.logger.log('Parsing delivery address:', { addressString });

    // Tách địa chỉ theo dấu phẩy
    const parts = addressString.split(',').map(part => part.trim());

    this.logger.log('Address parts after split:', { parts, length: parts.length });

    if (parts.length < 3) {
      this.logger.error('Insufficient address parts:', { parts, addressString });
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_INVALID_ADDRESS,
        'Địa chỉ giao hàng không đủ thông tin (cần ít nhất: địa chỉ, quận/huyện, tỉnh/thành)'
      );
    }

    // Xử lý địa chỉ thông minh hơn
    let address = '';
    let ward = '';
    let district = '';
    let province = '';

    if (parts.length >= 6) {
      // Format: "FHV8+9F6, Tản Đà, Hương Sơn, Huế, Thành phố Huế, Việt Nam"
      // Lấy 2 phần đầu làm address
      address = `${parts[0]}, ${parts[1]}`.trim();
      ward = parts[2] || '';
      district = parts[3] || '';
      province = parts[4] || '';
    } else if (parts.length >= 4) {
      // Format: "Địa chỉ, Phường/Xã, Quận/Huyện, Tỉnh/Thành"
      address = parts[0] || '';
      ward = parts[1] || '';
      district = parts[2] || '';
      province = parts[3] || '';
    } else {
      // Format ngắn: "Địa chỉ, Quận/Huyện, Tỉnh/Thành"
      address = parts[0] || '';
      ward = '';
      district = parts[1] || '';
      province = parts[2] || '';
    }

    const result = {
      address: address.trim(),
      ward: ward.trim(),
      district: district.trim(),
      province: province.trim(),
      hamlet: 'Khác'
    };

    this.logger.log('Parsed address result:', result);

    return result;
  }

  /**
   * Tính tổng giá trị đơn hàng
   */
  private calculateOrderValue(order: UserOrder): number {
    const productInfo = order.productInfo as any;
    const products = productInfo?.products || [];

    if (!Array.isArray(products)) {
      return 0;
    }

    return products.reduce((total: number, product: any) => {
      return total + (product.totalPrice || 0);
    }, 0);
  }





  /**
   * Tính tổng trọng lượng đơn hàng
   */
  private calculateTotalWeight(order: UserOrder, products: UserProduct[]): number {
    const productInfo = order.productInfo as any;
    const orderProducts = productInfo?.products || [];

    if (!Array.isArray(orderProducts)) {
      return 0.5; // Default weight
    }

    return orderProducts.reduce((totalWeight: number, orderProduct: any) => {
      const product = products.find(p => p.id === orderProduct.productId);

      if (!product) return totalWeight;

      const shipmentConfig = product.shipmentConfig as any;
      const productWeight = shipmentConfig?.weightGram
        ? shipmentConfig.weightGram / 1000 // Chuyển từ gram sang kg
        : 0.5; // Mặc định 0.5kg

      return totalWeight + (productWeight * (orderProduct.quantity || 1));
    }, 0);
  }
}
