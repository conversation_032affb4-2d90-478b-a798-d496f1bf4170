import { Injectable, Logger } from '@nestjs/common';
import {
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { ValidationHelper } from '../../../helpers/validation.helper';
import { CreateProductProcessor } from './create-product.processor';
import { CreateProductResult } from './create-product-orchestrator';
import { DigitalProductCreateDto } from '../../../dto/request/create/digital-product-create.dto';
import { PriceTypeEnum } from '@modules/business/enums';
import { DigitalClassificationHandler } from './digital-classification.handler';

/**
 * Processor chuyên xử lý logic tạo sản phẩm số
 * Bóc tách từ UserProductService để tối ưu hóa và dễ maintain
 */
@Injectable()
export class DigitalProductProcessor {
  private readonly logger = new Logger(DigitalProductProcessor.name);

  constructor(
    private readonly createProcessor: CreateProductProcessor,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
    private readonly validationHelper: ValidationHelper,
    private readonly digitalClassificationHandler: DigitalClassificationHandler,
  ) {}

  /**
   * Tạo sản phẩm số hoàn chỉnh
   */
  async createDigitalProduct(
    dto: DigitalProductCreateDto,
    userId: number,
  ): Promise<CreateProductResult> {
    this.logger.log(`Creating DIGITAL product: ${dto.name}`);

    // BƯỚC 1: Validate dữ liệu đầu vào cho sản phẩm số
    await this.validateDigitalProductData(dto);

    // BƯỚC 2: Xử lý classifications metadata và custom fields
    const classificationsMetadata = await this.processDigitalClassificationsMetadata(dto);
    const { metadata } = await this.createProcessor.processCustomFields(dto, classificationsMetadata);

    // BƯỚC 3: Tạo sản phẩm cơ bản với shipment config = 0
    const product = await this.createProcessor.createBaseProduct(dto, userId, metadata);
    product.shipmentConfig = { widthCm: 0, heightCm: 0, lengthCm: 0, weightGram: 0 };

    // BƯỚC 4: Lưu sản phẩm để có ID
    const savedProduct = await this.createProcessor.saveProduct(product);

    // BƯỚC 5: Xử lý hình ảnh sản phẩm chính
    const { imageEntries, imagesUploadUrls } = await this.createProcessor.processProductImages(dto, Date.now());

    // BƯỚC 6: Cập nhật sản phẩm với thông tin hình ảnh
    // savedProduct.images = imageEntries;
    await this.createProcessor.saveProduct(savedProduct);

    // BƯỚC 7: Tạo advanced info (classifications)
    const advancedInfo = await this.createAdvancedInfo(savedProduct.id, dto.productType, dto);

    // BƯỚC 8: Cập nhật detail_id để liên kết với advanced info
    if (advancedInfo?.id) {
      savedProduct.detail_id = advancedInfo.id;
      await this.createProcessor.saveProduct(savedProduct);
    }

    // BƯỚC 10: Xử lý classifications
    const classificationResult = await this.digitalClassificationHandler.processClassifications(
      savedProduct.id,
      dto.classifications || [],
      userId
    );
    const classifications = classificationResult.classifications;
    const classificationUploadUrls = classificationResult.uploadUrls;

    // BƯỚC 11: Lấy sản phẩm cuối cùng
    const finalProduct = await this.createProcessor.getProductById(savedProduct.id);

    // BƯỚC 12: Tạo object chứa upload URLs cho cả sản phẩm chính và classifications
    const uploadUrls: any = {
      productId: finalProduct.id.toString(),
      imagesUploadUrls: imagesUploadUrls || []
    };

    // Thêm upload URLs cho hình ảnh classifications nếu có
    if (classificationUploadUrls && classificationUploadUrls.length > 0) {
      uploadUrls.classificationUploadUrls = classificationUploadUrls;
    }

    const hasUploadUrls = (imagesUploadUrls.length > 0 || classificationUploadUrls.length > 0);

    return {
      product: finalProduct,
      uploadUrls: hasUploadUrls ? uploadUrls : null,
      additionalInfo: {
        advancedInfo,
        classifications
      }
    };
  }

  /**
   * Validate dữ liệu đầu vào cho sản phẩm số
   */
  private async validateDigitalProductData(dto: DigitalProductCreateDto): Promise<void> {
    // Kiểm tra classifications có tồn tại không (bắt buộc cho sản phẩm số)
    if (!dto.classifications || dto.classifications.length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Classifications are required for digital products',
      );
    }

    // Kiểm tra price có tồn tại không (bắt buộc cho sản phẩm số)
    if (!dto.price) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Price is required for digital products',
      );
    }

    // Validate giá sản phẩm theo business rules (digital products luôn có giá cố định)
    this.validationHelper.validateProductPrice(dto.price, PriceTypeEnum.HAS_PRICE, dto.productType);

    // Validate digital fulfillment flow
    if (!dto.digitalFulfillmentFlow) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Digital fulfillment flow is required for digital products',
      );
    }

    // Validate digital output
    if (!dto.digitalOutput) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Digital output is required for digital products',
      );
    }

    // Validate từng classification
    for (const classification of dto.classifications) {
      if (classification.minQuantityPerPurchase > classification.maxQuantityPerPurchase) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
          `Classification "${classification.name}": minimum quantity cannot be greater than maximum quantity`,
        );
      }
    }

    this.logger.log(`Validated digital product data for: ${dto.name}`);
  }

  /**
   * Xử lý classifications metadata cho sản phẩm số
   */
  private async processDigitalClassificationsMetadata(dto: DigitalProductCreateDto): Promise<any> {
    this.logger.log(`Processing classifications metadata for digital product: ${dto.name}`);

    // Xử lý classifications thành metadata format
    return {
      classifications: dto.classifications || [],
      digitalFulfillmentFlow: dto.digitalFulfillmentFlow,
      digitalOutput: dto.digitalOutput,
      purchaseCount: dto.purchaseCount || 0,
    };
  }

  /**
   * Tạo advanced info cho sản phẩm số và lưu vào database
   */
  private async createAdvancedInfo(
    productId: number,
    productType: string,
    dto: DigitalProductCreateDto,
  ): Promise<any> {
    this.logger.log(`Creating advanced info for digital product: ${productId}`);

    try {
      // Tạo advanced info entity với đúng cấu trúc ProductAdvancedInfo
      const advancedInfoData: Partial<any> = {
        productId,
        productType: productType as any, // Cast to enum
        purchaseCount: dto.purchaseCount || 0,
        digitalFulfillmentFlow: dto.digitalFulfillmentFlow,
        digitalOutput: dto.digitalOutput,
        // Các trường khác không set cho DIGITAL product (để undefined)
        images: [],
        combo: { info: [] },
      };

      // Lưu vào database thông qua repository method
      const savedAdvancedInfo = await this.productAdvancedInfoRepository.createOrUpdate(advancedInfoData);

      this.logger.log(`Created advanced info with ID: ${savedAdvancedInfo.id} for product: ${productId}`);
      return savedAdvancedInfo;
    } catch (error) {
      this.logger.error(`Failed to create advanced info for product ${productId}: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Failed to create advanced info: ${error.message}`,
      );
    }
  }




}
