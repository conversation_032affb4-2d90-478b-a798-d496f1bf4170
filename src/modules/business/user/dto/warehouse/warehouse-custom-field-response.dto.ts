import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho phản hồi thông tin trường tùy chỉnh của kho
 */
export class WarehouseCustomFieldResponseDto {
  /**
   * ID của kho
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID của kho',
    example: 1,
  })
  warehouseId: number;

  /**
   * ID của trường tùy chỉnh
   * @example 2
   */
  @Expose()
  @ApiProperty({
    description: 'ID của trường tùy chỉnh',
    example: 2,
  })
  fieldId: number;

  /**
   * Giá trị của trường tùy chỉnh
   * @example { "value": "Giá trị mẫu" }
   */
  @Expose()
  @ApiProperty({
    description: 'Giá trị của trường tùy chỉnh',
    example: { value: 'Giá trị mẫu' },
  })
  value: any;
}
