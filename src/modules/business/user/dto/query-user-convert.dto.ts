import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp chuyển đổi khách hàng
 */
export enum UserConvertSortField {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  CONVERSION_TYPE = 'conversionType',
  SOURCE = 'source',
}

/**
 * DTO cho các tham số truy vấn danh sách chuyển đổi khách hàng
 */
export class QueryUserConvertDto extends QueryDto {
  /**
   * Override từ khóa tìm kiếm từ QueryDto để tìm trong ghi chú
   */
  @ApiProperty({
    description: 'Từ khóa tìm kiếm trong ghi chú chuyển đổi',
    example: 'khách hàng tiềm năng',
    required: false,
  })
  @IsOptional()
  @IsString()
  declare search?: string;

  @ApiProperty({
    description: 'ID khách hàng được chuyển đổi',
    example: 101,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  convertCustomerId?: number;

  @ApiProperty({
    description: 'Loại chuyển đổi',
    example: 'online',
    required: false,
  })
  @IsOptional()
  @IsString()
  conversionType?: string;

  @ApiProperty({
    description: 'Nguồn gốc chuyển đổi',
    example: 'website',
    required: false,
  })
  @IsOptional()
  @IsString()
  source?: string;



  /**
   * Override trường sắp xếp từ QueryDto để sử dụng enum cụ thể
   */
  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: UserConvertSortField,
    default: UserConvertSortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserConvertSortField)
  declare sortBy?: UserConvertSortField;

  constructor() {
    super();
    this.sortBy = UserConvertSortField.CREATED_AT;
    this.sortDirection = SortDirection.DESC;
  }
}
