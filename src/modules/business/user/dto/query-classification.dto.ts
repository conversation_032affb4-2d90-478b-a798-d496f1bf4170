import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp phân loại
 */
export enum ClassificationSortField {
  ID = 'id',
  TYPE = 'type',
}

/**
 * DTO cho truy vấn phân loại sản phẩm
 */
export class QueryClassificationDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'ID sản phẩm để lọc',
    example: 123,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  productId?: number;

  @ApiPropertyOptional({
    description: 'Loại phân loại để lọc',
    example: 'Màu sắc',
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({
    description: 'Trường sắp xếp',
    enum: ClassificationSortField,
    default: ClassificationSortField.ID,
  })
  @IsOptional()
  @IsEnum(ClassificationSortField)
  sortBy?: ClassificationSortField = ClassificationSortField.ID;
}
