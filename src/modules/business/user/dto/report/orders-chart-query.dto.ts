import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum, IsDateString, Validate } from 'class-validator';
import { DateRangeValidator } from './report-overview-query.dto';
import { ChartGroupByEnum } from './sales-chart-query.dto';
import { ShippingStatusEnum } from '@modules/business/enums';

/**
 * DTO cho query parameters của API biểu đồ đơn hàng
 */
export class OrdersChartQueryDto {
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> bắt đầu (YYYY-MM-DD)',
    example: '2024-01-01',
    type: String,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Ng<PERSON>y bắt đầu phải có định dạng YYYY-MM-DD' })
  startDate?: string;

  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> kết thúc (YYYY-MM-DD)',
    example: '2024-12-31',
    type: String,
  })
  @IsOptional()
  @IsDateString({}, { message: '<PERSON><PERSON><PERSON> kết thúc phải có định dạng YYYY-MM-DD' })
  @Validate(DateRangeValidator)
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Cách nhóm dữ liệu theo thời gian',
    enum: ChartGroupByEnum,
    example: ChartGroupByEnum.MONTH,
    default: ChartGroupByEnum.MONTH,
  })
  @IsOptional()
  @IsEnum(ChartGroupByEnum, { message: 'Cách nhóm dữ liệu không hợp lệ' })
  groupBy?: ChartGroupByEnum = ChartGroupByEnum.MONTH;

  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái vận chuyển',
    enum: ShippingStatusEnum,
    example: ShippingStatusEnum.DELIVERED,
  })
  @IsOptional()
  @IsEnum(ShippingStatusEnum, { message: 'Trạng thái vận chuyển không hợp lệ' })
  status?: ShippingStatusEnum;
}
