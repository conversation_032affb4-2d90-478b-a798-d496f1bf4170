import { Test, TestingModule } from '@nestjs/testing';
import { UserVirtualWarehouseService } from '../../services/user-virtual-warehouse.service';
import { VirtualWarehouseRepository, WarehouseRepository } from '@modules/business/repositories';
import { ValidationHelper } from '../../helpers/validation.helper';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { CreateVirtualWarehouseDto, UpdateVirtualWarehouseDto, QueryVirtualWarehouseDto } from '../../dto/warehouse';
import { VirtualWarehouse, Warehouse } from '@modules/business/entities';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { plainToInstance } from 'class-transformer';
import { VirtualWarehouseResponseDto } from '../../dto/warehouse/virtual-warehouse-response.dto';

describe('UserVirtualWarehouseService', () => {
  let service: UserVirtualWarehouseService;
  let virtualWarehouseRepository: VirtualWarehouseRepository;
  let warehouseRepository: WarehouseRepository;
  let validationHelper: ValidationHelper;

  // Mock data
  const mockVirtualWarehouses: VirtualWarehouse[] = [
    {
      warehouseId: 1,
      associatedSystem: 'SAP ERP',
      purpose: 'Quản lý hàng hóa trực tuyến',
    },
    {
      warehouseId: 2,
      associatedSystem: 'Oracle ERP',
      purpose: 'Quản lý hàng hóa offline',
    },
  ];

  const mockWarehouses: Warehouse[] = [
    {
      warehouseId: 1,
      name: 'Kho ảo 1',
      description: 'Mô tả kho ảo 1',
      type: WarehouseTypeEnum.VIRTUAL,
    },
    {
      warehouseId: 2,
      name: 'Kho ảo 2',
      description: 'Mô tả kho ảo 2',
      type: WarehouseTypeEnum.VIRTUAL,
    },
  ];

  const mockVirtualWarehouseWithDetails = {
    warehouseId: 1,
    associatedSystem: 'SAP ERP',
    purpose: 'Quản lý hàng hóa trực tuyến',
    name: 'Kho ảo 1',
    description: 'Mô tả kho ảo 1',
    type: WarehouseTypeEnum.VIRTUAL,
    customFields: [
      {
        warehouseId: 1,
        fieldId: 1,
        value: { value: 'Giá trị 1' },
      },
    ],
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserVirtualWarehouseService,
        {
          provide: VirtualWarehouseRepository,
          useValue: {
            createVirtualWarehouse: jest.fn(),
            findByWarehouseId: jest.fn(),
            findByWarehouseIdWithDetails: jest.fn(),
            findAll: jest.fn(),
            updateVirtualWarehouse: jest.fn(),
            deleteVirtualWarehouse: jest.fn(),
          },
        },
        {
          provide: WarehouseRepository,
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            findById: jest.fn(),
          },
        },
        {
          provide: ValidationHelper,
          useValue: {
            validateCreateVirtualWarehouse: jest.fn(),
            validateWarehouseExists: jest.fn(),
            validateUpdateVirtualWarehouse: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UserVirtualWarehouseService>(UserVirtualWarehouseService);
    virtualWarehouseRepository = module.get<VirtualWarehouseRepository>(VirtualWarehouseRepository);
    warehouseRepository = module.get<WarehouseRepository>(WarehouseRepository);
    validationHelper = module.get<ValidationHelper>(ValidationHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createVirtualWarehouse', () => {
    it('nên tạo kho ảo mới thành công', async () => {
      // Arrange
      const createDto: CreateVirtualWarehouseDto = {
        name: 'Kho ảo mới',
        description: 'Mô tả kho ảo mới',
        associatedSystem: 'Microsoft Dynamics',
        purpose: 'Quản lý hàng hóa mới',
      };
      const newWarehouse = {
        warehouseId: 3,
        name: 'Kho ảo mới',
        description: 'Mô tả kho ảo mới',
        type: WarehouseTypeEnum.VIRTUAL,
      };
      const newVirtualWarehouse = {
        warehouseId: 3,
        associatedSystem: 'Microsoft Dynamics',
        purpose: 'Quản lý hàng hóa mới',
      };

      jest.spyOn(validationHelper, 'validateCreateVirtualWarehouse').mockResolvedValue(undefined);
      jest.spyOn(warehouseRepository, 'create').mockReturnValue(newWarehouse as Warehouse);
      jest.spyOn(warehouseRepository, 'save').mockResolvedValue(newWarehouse as Warehouse);
      jest.spyOn(virtualWarehouseRepository, 'createVirtualWarehouse').mockResolvedValue(newVirtualWarehouse as VirtualWarehouse);

      // Act
      const result = await service.createVirtualWarehouse(createDto);

      // Assert
      expect(validationHelper.validateCreateVirtualWarehouse).toHaveBeenCalledWith(createDto);
      expect(warehouseRepository.create).toHaveBeenCalledWith({
        name: 'Kho ảo mới',
        description: 'Mô tả kho ảo mới',
        type: WarehouseTypeEnum.VIRTUAL,
      });
      expect(warehouseRepository.save).toHaveBeenCalledWith(newWarehouse);
      expect(virtualWarehouseRepository.createVirtualWarehouse).toHaveBeenCalledWith({
        warehouseId: 3,
        associatedSystem: 'Microsoft Dynamics',
        purpose: 'Quản lý hàng hóa mới',
      });
      expect(result).toBeInstanceOf(VirtualWarehouseResponseDto);
      expect(result.warehouseId).toBe(3);
      expect(result.name).toBe('Kho ảo mới');
      expect(result.description).toBe('Mô tả kho ảo mới');
      expect(result.type).toBe(WarehouseTypeEnum.VIRTUAL);
      expect(result.associatedSystem).toBe('Microsoft Dynamics');
      expect(result.purpose).toBe('Quản lý hàng hóa mới');
    });

    it('nên ném lỗi khi validation thất bại', async () => {
      // Arrange
      const createDto: CreateVirtualWarehouseDto = {
        name: 'Kho ảo mới',
        description: 'Mô tả kho ảo mới',
        associatedSystem: 'Microsoft Dynamics',
        purpose: 'Quản lý hàng hóa mới',
      };
      const error = new AppException(BUSINESS_ERROR_CODES.VIRTUAL_WAREHOUSE_CREATION_FAILED, 'Validation error');

      jest.spyOn(validationHelper, 'validateCreateVirtualWarehouse').mockRejectedValue(error);

      // Act & Assert
      await expect(service.createVirtualWarehouse(createDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateCreateVirtualWarehouse).toHaveBeenCalledWith(createDto);
    });

    it('nên ném lỗi khi tạo warehouse thất bại', async () => {
      // Arrange
      const createDto: CreateVirtualWarehouseDto = {
        name: 'Kho ảo mới',
        description: 'Mô tả kho ảo mới',
        associatedSystem: 'Microsoft Dynamics',
        purpose: 'Quản lý hàng hóa mới',
      };
      const newWarehouse = {
        warehouseId: 3,
        name: 'Kho ảo mới',
        description: 'Mô tả kho ảo mới',
        type: WarehouseTypeEnum.VIRTUAL,
      };

      jest.spyOn(validationHelper, 'validateCreateVirtualWarehouse').mockResolvedValue(undefined);
      jest.spyOn(warehouseRepository, 'create').mockReturnValue(newWarehouse as Warehouse);
      jest.spyOn(warehouseRepository, 'save').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.createVirtualWarehouse(createDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateCreateVirtualWarehouse).toHaveBeenCalledWith(createDto);
      expect(warehouseRepository.create).toHaveBeenCalled();
      expect(warehouseRepository.save).toHaveBeenCalled();
    });
  });

  describe('updateVirtualWarehouse', () => {
    it('nên cập nhật kho ảo thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const updateDto: UpdateVirtualWarehouseDto = {
        associatedSystem: 'SAP ERP Updated',
        purpose: 'Quản lý hàng hóa trực tuyến (đã cập nhật)',
      };
      const existingWarehouse = mockWarehouses[0];
      const existingVirtualWarehouse = mockVirtualWarehouses[0];
      const updatedVirtualWarehouse = {
        ...existingVirtualWarehouse,
        associatedSystem: 'SAP ERP Updated',
        purpose: 'Quản lý hàng hóa trực tuyến (đã cập nhật)',
      };

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(virtualWarehouseRepository, 'findByWarehouseId').mockResolvedValue(existingVirtualWarehouse);
      jest.spyOn(validationHelper, 'validateUpdateVirtualWarehouse').mockResolvedValue(undefined);
      jest.spyOn(virtualWarehouseRepository, 'updateVirtualWarehouse').mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });
      jest.spyOn(virtualWarehouseRepository, 'findByWarehouseIdWithDetails').mockResolvedValue(
        { ...mockVirtualWarehouseWithDetails, ...updatedVirtualWarehouse }
      );

      // Act
      const result = await service.updateVirtualWarehouse(warehouseId, updateDto);

      // Assert
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(virtualWarehouseRepository.findByWarehouseId).toHaveBeenCalledWith(warehouseId);
      expect(validationHelper.validateUpdateVirtualWarehouse).toHaveBeenCalledWith(updateDto, existingVirtualWarehouse);
      expect(virtualWarehouseRepository.updateVirtualWarehouse).toHaveBeenCalledWith(warehouseId, updateDto);
      expect(virtualWarehouseRepository.findByWarehouseIdWithDetails).toHaveBeenCalledWith(warehouseId);
      expect(result).toBeInstanceOf(VirtualWarehouseResponseDto);
      expect(result.warehouseId).toBe(1);
      expect(result.associatedSystem).toBe('SAP ERP Updated');
      expect(result.purpose).toBe('Quản lý hàng hóa trực tuyến (đã cập nhật)');
    });

    it('nên ném lỗi khi kho không tồn tại', async () => {
      // Arrange
      const warehouseId = 999;
      const updateDto: UpdateVirtualWarehouseDto = {
        associatedSystem: 'SAP ERP Updated',
      };
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND, 'Kho không tồn tại');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.updateVirtualWarehouse(warehouseId, updateDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi kho ảo không tồn tại', async () => {
      // Arrange
      const warehouseId = 1;
      const updateDto: UpdateVirtualWarehouseDto = {
        associatedSystem: 'SAP ERP Updated',
      };
      const existingWarehouse = mockWarehouses[0];

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(virtualWarehouseRepository, 'findByWarehouseId').mockResolvedValue(null);

      // Act & Assert
      await expect(service.updateVirtualWarehouse(warehouseId, updateDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(virtualWarehouseRepository.findByWarehouseId).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi validation thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const updateDto: UpdateVirtualWarehouseDto = {
        associatedSystem: 'SAP ERP Updated',
      };
      const existingWarehouse = mockWarehouses[0];
      const existingVirtualWarehouse = mockVirtualWarehouses[0];
      const error = new AppException(BUSINESS_ERROR_CODES.VIRTUAL_WAREHOUSE_UPDATE_FAILED, 'Validation error');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(virtualWarehouseRepository, 'findByWarehouseId').mockResolvedValue(existingVirtualWarehouse);
      jest.spyOn(validationHelper, 'validateUpdateVirtualWarehouse').mockRejectedValue(error);

      // Act & Assert
      await expect(service.updateVirtualWarehouse(warehouseId, updateDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(virtualWarehouseRepository.findByWarehouseId).toHaveBeenCalledWith(warehouseId);
      expect(validationHelper.validateUpdateVirtualWarehouse).toHaveBeenCalledWith(updateDto, existingVirtualWarehouse);
    });
  });

  describe('getVirtualWarehouseById', () => {
    it('nên lấy thông tin kho ảo theo ID thành công', async () => {
      // Arrange
      const warehouseId = 1;

      jest.spyOn(virtualWarehouseRepository, 'findByWarehouseIdWithDetails').mockResolvedValue(mockVirtualWarehouseWithDetails);

      // Act
      const result = await service.getVirtualWarehouseById(warehouseId);

      // Assert
      expect(virtualWarehouseRepository.findByWarehouseIdWithDetails).toHaveBeenCalledWith(warehouseId);
      expect(result).toBeInstanceOf(VirtualWarehouseResponseDto);
      expect(result.warehouseId).toBe(1);
      expect(result.name).toBe('Kho ảo 1');
      expect(result.description).toBe('Mô tả kho ảo 1');
      expect(result.type).toBe(WarehouseTypeEnum.VIRTUAL);
      expect(result.associatedSystem).toBe('SAP ERP');
      expect(result.purpose).toBe('Quản lý hàng hóa trực tuyến');
      expect(result.customFields).toHaveLength(1);
    });

    it('nên ném lỗi khi kho ảo không tồn tại', async () => {
      // Arrange
      const warehouseId = 999;

      jest.spyOn(virtualWarehouseRepository, 'findByWarehouseIdWithDetails').mockResolvedValue(null);

      // Act & Assert
      await expect(service.getVirtualWarehouseById(warehouseId)).rejects.toThrow(AppException);
      expect(virtualWarehouseRepository.findByWarehouseIdWithDetails).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi lấy thông tin kho ảo thất bại', async () => {
      // Arrange
      const warehouseId = 1;

      jest.spyOn(virtualWarehouseRepository, 'findByWarehouseIdWithDetails').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.getVirtualWarehouseById(warehouseId)).rejects.toThrow(AppException);
      expect(virtualWarehouseRepository.findByWarehouseIdWithDetails).toHaveBeenCalledWith(warehouseId);
    });
  });

  describe('getVirtualWarehouses', () => {
    it('nên lấy danh sách kho ảo với phân trang thành công', async () => {
      // Arrange
      const queryDto: QueryVirtualWarehouseDto = {
        page: 1,
        limit: 10,
        search: 'kho',
        sortBy: 'name',
        sortDirection: 'ASC',
      };
      const paginatedResult = {
        items: [mockVirtualWarehouseWithDetails],
        meta: {
          totalItems: 1,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(virtualWarehouseRepository, 'findAll').mockResolvedValue(paginatedResult);

      // Act
      const result = await service.getVirtualWarehouses(queryDto);

      // Assert
      expect(virtualWarehouseRepository.findAll).toHaveBeenCalledWith(queryDto);
      expect(result.items.length).toBe(1);
      expect(result.items[0]).toBeInstanceOf(VirtualWarehouseResponseDto);
      expect(result.items[0].warehouseId).toBe(1);
      expect(result.items[0].name).toBe('Kho ảo 1');
      expect(result.meta.totalItems).toBe(1);
    });

    it('nên ném lỗi khi lấy danh sách kho ảo thất bại', async () => {
      // Arrange
      const queryDto: QueryVirtualWarehouseDto = {
        page: 1,
        limit: 10,
      };

      jest.spyOn(virtualWarehouseRepository, 'findAll').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.getVirtualWarehouses(queryDto)).rejects.toThrow(AppException);
      expect(virtualWarehouseRepository.findAll).toHaveBeenCalledWith(queryDto);
    });
  });

  describe('deleteVirtualWarehouse', () => {
    it('nên xóa kho ảo thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const existingWarehouse = mockWarehouses[0];
      const existingVirtualWarehouse = mockVirtualWarehouses[0];

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(virtualWarehouseRepository, 'findByWarehouseId').mockResolvedValue(existingVirtualWarehouse);
      jest.spyOn(virtualWarehouseRepository, 'deleteVirtualWarehouse').mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });
      jest.spyOn(warehouseRepository, 'delete').mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });

      // Act
      await service.deleteVirtualWarehouse(warehouseId);

      // Assert
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(virtualWarehouseRepository.findByWarehouseId).toHaveBeenCalledWith(warehouseId);
      expect(virtualWarehouseRepository.deleteVirtualWarehouse).toHaveBeenCalledWith(warehouseId);
      expect(warehouseRepository.delete).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi kho không tồn tại', async () => {
      // Arrange
      const warehouseId = 999;
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND, 'Kho không tồn tại');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.deleteVirtualWarehouse(warehouseId)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi kho ảo không tồn tại', async () => {
      // Arrange
      const warehouseId = 1;
      const existingWarehouse = mockWarehouses[0];

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(virtualWarehouseRepository, 'findByWarehouseId').mockResolvedValue(null);

      // Act & Assert
      await expect(service.deleteVirtualWarehouse(warehouseId)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(virtualWarehouseRepository.findByWarehouseId).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi xóa kho ảo thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const existingWarehouse = mockWarehouses[0];
      const existingVirtualWarehouse = mockVirtualWarehouses[0];

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(virtualWarehouseRepository, 'findByWarehouseId').mockResolvedValue(existingVirtualWarehouse);
      jest.spyOn(virtualWarehouseRepository, 'deleteVirtualWarehouse').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.deleteVirtualWarehouse(warehouseId)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(virtualWarehouseRepository.findByWarehouseId).toHaveBeenCalledWith(warehouseId);
      expect(virtualWarehouseRepository.deleteVirtualWarehouse).toHaveBeenCalledWith(warehouseId);
    });
  });
});
