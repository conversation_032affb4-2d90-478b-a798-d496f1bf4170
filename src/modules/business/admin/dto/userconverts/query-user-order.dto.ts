import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional, IsString, Min, IsEnum } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { QueryDto } from '@dto/query.dto';
import { ShippingStatusEnum } from '@modules/business/enums';

/**
 * DTO cho các tham số truy vấn danh sách đơn hàng của người dùng
 */
export class QueryUserOrderDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'ID người dùng sở hữu đơn hàng',
    example: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  userId?: number;

  @ApiPropertyOptional({
    description: 'ID khách hàng đặt đơn',
    example: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  userConvertCustomerId?: number;

  @ApiPropertyOptional({
    description: 'Đơn hàng có yêu cầu vận chuyển hay không',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  hasShipping?: boolean;

  @ApiPropertyOptional({
    description: 'Trạng thái vận chuyển',
    enum: ShippingStatusEnum,
    example: ShippingStatusEnum.PENDING
  })
  @IsOptional()
  @IsEnum(ShippingStatusEnum, { message: 'Trạng thái vận chuyển không hợp lệ' })
  shippingStatus?: ShippingStatusEnum;

  @ApiPropertyOptional({
    description: 'Nguồn đơn hàng',
    example: 'website'
  })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiPropertyOptional({
    description: 'Thời gian tạo từ (timestamp)',
    example: 1625097600000
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createdAtFrom?: number;

  @ApiPropertyOptional({
    description: 'Thời gian tạo đến (timestamp)',
    example: 1625184000000
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createdAtTo?: number;
}
