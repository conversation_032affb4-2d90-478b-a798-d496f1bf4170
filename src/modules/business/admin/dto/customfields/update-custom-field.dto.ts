import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsObject, IsOptional, IsString, ValidateNested, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { CustomFieldConfigDto } from './custom-field-config.dto';
import { CustomFieldTypeEnum } from '@modules/business/enums';

/**
 * DTO cho việc cập nhật trường tùy chỉnh
 */
export class UpdateCustomFieldDto {

  @ApiPropertyOptional({
    description: 'ID cấu hình (phải là unique)',
    example: 'product_color',
  })
  @IsOptional()
  @IsString()
  configId?: string;

  @ApiPropertyOptional({
    description: 'Nhãn hiển thị',
    example: '<PERSON>àu sắc sản phẩm',
  })
  @IsOptional()
  @IsString()
  label?: string;

  @ApiPropertyOptional({
    description: '<PERSON>ại trường',
    example: 'text',
    enum: CustomFieldTypeEnum,
    enumName: 'CustomFieldTypeEnum',
  })
  @IsOptional()
  @IsEnum(CustomFieldTypeEnum, {
    message: `Loại trường phải là một trong các giá trị: ${Object.values(CustomFieldTypeEnum).join(', ')}`
  })
  type?: CustomFieldTypeEnum;

  @ApiPropertyOptional({
    description: 'Trường bắt buộc hay không',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  required?: boolean;

  @ApiPropertyOptional({
    description: 'Cấu hình chi tiết',
    type: () => CustomFieldConfigDto,
  })
  @IsOptional()
  configJson?: any; // Sử dụng any để hỗ trợ nhiều kiểu dữ liệu

  constructor(partial: Partial<UpdateCustomFieldDto>) {
    Object.assign(this, partial);
  }
}
