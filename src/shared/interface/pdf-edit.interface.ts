/**
 * Đ<PERSON><PERSON> nghĩa cấu trúc dữ liệu cho một vị trí cần chỉnh sửa trên PDF.
 */
export interface PdfPosition {
    /**
     * Chỉ số trang (bắt đầu từ 0)
     */
    pageIndex: number;
    
    /**
     * Văn bản cần chèn (tùy chọn nếu chèn chữ ký)
     */
    text?: string;
    
    /**
     * Tọa độ X (theo mm)
     */
    xMm: number;
    
    /**
     * Tọa độ Y (theo mm)
     */
    yMm: number;
    
    /**
     * Kích thước chữ (mặc định: 12)
     */
    size?: number;
    
    /**
     * Chuỗi Base64 của chữ ký tay (nếu có)
     */
    signatureBase64?: string;
    
    /**
     * Chiều rộng chữ ký tay (mm, mặc định 50)
     */
    signatureWidthMm?: number;
    
    /**
     * Chiều cao chữ ký tay (mm, mặc định 20)
     */
    signatureHeightMm?: number;
    
    /**
     * Đ<PERSON> đậm phông chữ (mặc định 400)
     */
    fontWeight?: number;
    
    /**
     * Căn giữa text (mặc định false)
     */
    isCenter?: boolean;
}

/**
 * Định nghĩa cấu trúc dữ liệu cho kết quả chỉnh sửa PDF
 */
export interface PdfEditResult {
    /**
     * Dữ liệu PDF đã được chỉnh sửa dưới dạng Buffer
     */
    pdfBuffer: Buffer;
    
    /**
     * Dữ liệu PDF đã được chỉnh sửa dưới dạng Base64 string
     */
    pdfBase64?: string;
}
