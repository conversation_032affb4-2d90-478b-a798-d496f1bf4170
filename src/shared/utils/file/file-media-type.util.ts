import { AppException, ErrorCode } from '@common/exceptions/app.exception';

/**
 * Enum định nghĩa các loại MIME cho file
 */
export enum FileTypeEnum {
  DOC = 'application/msword',
  DOCX = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  JSON = 'application/json',
  PDF = 'application/pdf',
  PPTX = 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  HTML = 'text/html',
  TXT = 'text/plain',
}

/**
 * Object tiện ích để làm việc với FileTypeEnum
 */
export const FileType = {
  /**
   * Lấy giá trị chuỗi của một loại file
   * @param type Loại file
   * @returns Giá trị MIME tương ứng
   */
  getValue(type: FileTypeEnum): string {
    return type;
  },

  /**
   * Lấy enum FileTypeEnum từ tên loại file hoặc giá trị MIME type
   * @param type Tên loại file (key của enum) hoặc giá trị MIME type (ví dụ: 'application/pdf')
   * @returns Giá trị enum FileTypeEnum tương ứng
   * @throws AppException nếu loại file không tồn tại
   */
  getMimeType(type: string): FileTypeEnum {
    // Kiểm tra nếu là key của enum (ví dụ: 'PDF')
    const mimeTypeFromKey = FileTypeEnum[type as keyof typeof FileTypeEnum];
    if (mimeTypeFromKey) {
      return mimeTypeFromKey;
    }

    // Kiểm tra nếu là giá trị MIME type (ví dụ: 'application/pdf')
    const entries = Object.entries(FileTypeEnum);
    const entry = entries.find(([_, value]) => value === type);

    if (entry) {
      return FileTypeEnum[entry[0] as keyof typeof FileTypeEnum];
    }

    // Nếu không tìm thấy, ném exception
    throw new AppException(
      ErrorCode.FILE_TYPE_NOT_FOUND,
      `Loại tệp '${type}' không được hỗ trợ`
    );
  },
};