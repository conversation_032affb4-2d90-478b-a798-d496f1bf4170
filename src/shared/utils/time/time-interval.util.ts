/**
 * Enum định nghĩa các khoảng thời gian thông dụng (tính bằng milliseconds)
 */
export enum TimeIntervalEnum {
  ONE_MINUTE = 60,
  FIVE_MINUTES = 5 * 60,
  TEN_MINUTES = 10 * 60,
  FIFTEEN_MINUTES = 15 * 60,
  THIRTY_MINUTES = 30 * 60,
  ONE_HOUR = 60 * 60,
  TWO_HOURS = 2 * 60 * 60,
  THREE_HOURS = 3 * 60 * 60,
  SIX_HOURS = 6 * 60 * 60,
  TWELVE_HOURS = 12 * 60 * 60,
  ONE_DAY = 24 * 60 * 60,
  TWO_DAYS = 2 * 24 * 60 * 60,
  THREE_DAYS = 3 * 24 * 60 * 60,
  SEVEN_DAYS = 7 * 24 * 60 * 60,
  THIRTY_DAYS = 30 * 24 * 60 * 60,
  NINETY_DAYS = 90 * 24 * 60 * 60,
  ONE_YEAR = 365 * 24 * 60 * 60,
}

/**
 * Object tiện ích để làm việc với TimeIntervalEnum
 */
export const TimeInterval = {
  /**
   * L<PERSON>y giá trị số của một khoảng thời gian
   * @param key Tên của khoảng thời gian
   * @returns Giá trị milliseconds tương ứng
   */
  getValue(key: keyof typeof TimeIntervalEnum): number {
    return TimeIntervalEnum[key];
  },
};

/**
 * Hàm helper để lấy giá trị số của một TimeIntervalEnum
 */
export function getTimeInterval(interval: TimeIntervalEnum): number {
  return interval;
}