import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BaseSmsProvider } from './base-sms-provider.service';
import { SmsResponse, BulkSmsResponse, MessageStatusResponse, ConnectionTestResponse, MessageStatus } from './sms-provider.interface';

// Importer le client Vonage
let Vonage;
try {
  Vonage = require('@vonage/server-sdk').default;
} catch (e) {
  // Vonage n'est pas installé, nous le gérerons dans le constructeur
}

/**
 * Interface pour la configuration de Vonage
 */
export interface VonageConfig {
  /**
   * Clé API Vonage
   */
  apiKey: string;

  /**
   * Secret API Vonage
   */
  apiSecret: string;

  /**
   * Nom de l'expéditeur par défaut
   */
  from?: string;
}

/**
 * Service d'intégration avec l'API Vonage (anciennement Nexmo)
 */
@Injectable()
export class VonageProvider extends BaseSmsProvider {
  readonly providerName = 'Vonage';
  
  private client: any;
  private readonly apiKey: string;
  private readonly apiSecret: string;
  private readonly defaultFrom: string;

  constructor(private readonly configService: ConfigService) {
    super('VonageProvider');
    
    // Charger la configuration depuis les variables d'environnement ou utiliser des valeurs par défaut
    this.apiKey = this.configService.get<string>('VONAGE_API_KEY') || '';
    this.apiSecret = this.configService.get<string>('VONAGE_API_SECRET') || '';
    this.defaultFrom = this.configService.get<string>('VONAGE_FROM') || 'Vonage';
    
    // Vérifier si Vonage est installé
    if (!Vonage) {
      this.logger.warn('Le package "@vonage/server-sdk" n\'est pas installé. Veuillez l\'installer avec "npm install @vonage/server-sdk"');
    } else {
      // Initialiser le client Vonage
      this.initClient(this.apiKey, this.apiSecret);
    }
  }

  /**
   * Initialise le client Vonage avec les identifiants fournis
   * @param apiKey Clé API Vonage
   * @param apiSecret Secret API Vonage
   */
  private initClient(apiKey: string, apiSecret: string): void {
    if (Vonage && apiKey && apiSecret) {
      try {
        this.client = new Vonage({
          apiKey,
          apiSecret
        });
        this.logger.log('Client Vonage initialisé avec succès');
      } catch (error) {
        this.logger.error(`Erreur lors de l'initialisation du client Vonage: ${error.message}`, error.stack);
      }
    }
  }

  /**
   * Envoie un SMS à un numéro de téléphone via Vonage
   * @param phoneNumber Numéro de téléphone du destinataire
   * @param message Contenu du message
   * @param options Options supplémentaires (from)
   * @returns Promesse contenant l'ID du message ou une erreur
   */
  async sendSms(phoneNumber: string, message: string, options?: any): Promise<SmsResponse> {
    return new Promise((resolve) => {
      try {
        this.logger.debug(`Envoi d'un SMS à ${phoneNumber} via Vonage`);
        
        // Vérifier si le client Vonage est initialisé
        if (!this.client) {
          throw new Error('Le client Vonage n\'est pas initialisé');
        }
        
        const formattedPhoneNumber = this.formatPhoneNumber(phoneNumber);
        const from = options?.from || this.defaultFrom;
        
        // Préparer les options pour l'envoi du SMS
        const smsOptions = {
          type: options?.type || 'text',
          ...(options?.ttl && { ttl: options.ttl }),
          ...(options?.callback && { callback: options.callback }),
          ...(options?.messageClass && { message_class: options.messageClass }),
          ...(options?.clientRef && { client_ref: options.clientRef })
        };
        
        // Envoyer le SMS via Vonage
        this.client.message.sendSms(from, formattedPhoneNumber, message, smsOptions, (err, responseData) => {
          if (err) {
            this.logger.error(`Erreur lors de l'envoi du SMS via Vonage: ${err.message}`, err.stack);
            resolve({
              success: false,
              errorMessage: err.message || 'Erreur inconnue'
            });
            return;
          }
          
          // Vérifier les résultats
          const result = responseData.messages[0];
          
          if (result.status === '0') {
            resolve({
              success: true,
              messageId: result['message-id'],
              rawResponse: responseData
            });
          } else {
            resolve({
              success: false,
              errorCode: result.status,
              errorMessage: result['error-text'] || 'Erreur inconnue',
              rawResponse: responseData
            });
          }
        });
      } catch (error) {
        this.logger.error(`Exception lors de l'envoi du SMS via Vonage: ${error.message}`, error.stack);
        resolve({
          success: false,
          errorMessage: error.message || 'Erreur inconnue'
        });
      }
    });
  }

  /**
   * Vérifie le statut d'un message envoyé via Vonage
   * @param messageId ID du message Vonage à vérifier
   * @returns Promesse contenant le statut du message
   */
  async checkMessageStatus(messageId: string): Promise<MessageStatusResponse> {
    return new Promise((resolve) => {
      try {
        this.logger.debug(`Vérification du statut du message ${messageId} via Vonage`);
        
        // Vérifier si le client Vonage est initialisé
        if (!this.client) {
          throw new Error('Le client Vonage n\'est pas initialisé');
        }
        
        // Récupérer le statut du message depuis Vonage
        this.client.message.search(messageId, (err, result) => {
          if (err) {
            this.logger.error(`Erreur lors de la vérification du statut du message via Vonage: ${err.message}`, err.stack);
            resolve({
              messageId,
              status: MessageStatus.UNKNOWN,
              updatedAt: new Date(),
              details: err.message || 'Erreur inconnue'
            });
            return;
          }
          
          resolve({
            messageId,
            status: this.mapVonageStatus(result.status),
            updatedAt: new Date(result.date_finalized || result.date_received || result.date_submitted || Date.now()),
            details: result['error-text'] || '',
            rawResponse: result
          });
        });
      } catch (error) {
        this.logger.error(`Exception lors de la vérification du statut du message via Vonage: ${error.message}`, error.stack);
        resolve({
          messageId,
          status: MessageStatus.UNKNOWN,
          updatedAt: new Date(),
          details: error.message || 'Erreur inconnue'
        });
      }
    });
  }

  /**
   * Envoie un SMS avec un brandname via Vonage
   * @param phoneNumber Numéro de téléphone du destinataire
   * @param message Contenu du message
   * @param brandname Nom de la marque à utiliser comme expéditeur
   * @param options Options supplémentaires
   * @returns Promesse contenant l'ID du message ou une erreur
   */
  async sendBrandnameSms(phoneNumber: string, message: string, brandname: string, options?: any): Promise<SmsResponse> {
    // Pour Vonage, le brandname est simplement le paramètre "from"
    return this.sendSms(phoneNumber, message, { 
      ...options, 
      from: brandname 
    });
  }

  /**
   * Teste la connexion avec Vonage
   * @param config Configuration de Vonage
   * @returns Promesse indiquant si la connexion est réussie
   */
  async testConnection(config: VonageConfig): Promise<ConnectionTestResponse> {
    return new Promise((resolve) => {
      try {
        this.logger.debug('Test de connexion avec Vonage');
        
        const apiKey = config.apiKey || this.apiKey;
        const apiSecret = config.apiSecret || this.apiSecret;
        
        if (!apiKey || !apiSecret) {
          resolve({
            success: false,
            message: 'Identifiants Vonage manquants'
          });
          return;
        }
        
        // Créer un client temporaire pour le test
        const testClient = new Vonage({
          apiKey,
          apiSecret
        });
        
        // Tester la connexion en récupérant le solde du compte
        testClient.account.checkBalance((err, result) => {
          if (err) {
            this.logger.error(`Erreur lors du test de connexion avec Vonage: ${err.message}`, err.stack);
            resolve({
              success: false,
              message: err.message || 'Erreur inconnue'
            });
            return;
          }
          
          resolve({
            success: true,
            message: 'Connexion réussie',
            details: {
              balance: result.value,
              autoReload: result.autoReload
            }
          });
        });
      } catch (error) {
        this.logger.error(`Exception lors du test de connexion avec Vonage: ${error.message}`, error.stack);
        resolve({
          success: false,
          message: error.message || 'Erreur inconnue'
        });
      }
    });
  }

  /**
   * Convertit un statut Vonage en statut standard
   * @param vonageStatus Statut Vonage
   * @returns Statut standard
   */
  private mapVonageStatus(vonageStatus: string): MessageStatus {
    switch (vonageStatus) {
      case 'submitted':
        return MessageStatus.PENDING;
      case 'delivered':
        return MessageStatus.DELIVERED;
      case 'expired':
        return MessageStatus.EXPIRED;
      case 'failed':
      case 'rejected':
        return MessageStatus.FAILED;
      case 'accepted':
      case 'buffered':
        return MessageStatus.SENDING;
      default:
        return MessageStatus.UNKNOWN;
    }
  }
}
