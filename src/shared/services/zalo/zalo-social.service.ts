import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException, ErrorCode } from '@common/exceptions';
import { ZaloService } from './zalo.service';
import {
  ZaloAccessToken,
  ZaloRefreshTokenResponse,
  ZaloSocialUserInfo,
} from './zalo.interface';

/**
 * Service chuyên biệt cho Zalo Social API
 * Cung cấp các phương thức để tương tác với Zalo Social API V4
 */
@Injectable()
export class ZaloSocialService {
  private readonly logger = new Logger(ZaloSocialService.name);

  constructor(
    private readonly zaloService: ZaloService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Tạo URL xác thực cho Social API với các quyền tùy chỉnh
   * @param appId ID của ứng dụng
   * @param redirectUri URI chuyển hướng
   * @param scope Các quyền cần xin (mặc định: id,name,picture)
   * @param state State parameter để bảo mật
   * @returns URL xác thực
   */
  createAuthUrl(
    appId: string,
    redirectUri: string,
    scope: string = 'id,name,picture',
    state?: string,
  ): string {
    const baseUrl = 'https://oauth.zaloapp.com/v4/permission';
    const params = new URLSearchParams({
      app_id: appId,
      redirect_uri: redirectUri,
      scope,
    });

    if (state) {
      params.append('state', state);
    }

    return `${baseUrl}?${params.toString()}`;
  }

  /**
   * Lấy access token từ authorization code
   * @param appId ID của ứng dụng
   * @param appSecret Secret của ứng dụng
   * @param code Authorization code từ callback
   * @param redirectUri URI chuyển hướng (phải giống với lúc tạo auth URL)
   * @returns Access token và refresh token
   * @throws AppException nếu có lỗi xảy ra
   */
  async getAccessToken(
    appId: string,
    appSecret: string,
    code: string,
    redirectUri: string,
  ): Promise<ZaloAccessToken> {
    return this.zaloService.getSocialAccessToken(appId, appSecret, code, redirectUri);
  }

  /**
   * Làm mới access token từ refresh token
   * @param appId ID của ứng dụng
   * @param refreshToken Refresh token
   * @returns Access token mới
   * @throws AppException nếu có lỗi xảy ra
   */
  async refreshAccessToken(
    appId: string,
    refreshToken: string,
  ): Promise<ZaloRefreshTokenResponse> {
    return this.zaloService.refreshSocialAccessToken(appId, refreshToken);
  }

  /**
   * Lấy thông tin cơ bản của người dùng
   * @param accessToken Access token của người dùng
   * @returns Thông tin cơ bản của người dùng
   * @throws AppException nếu có lỗi xảy ra
   */
  async getUserInfo(accessToken: string): Promise<ZaloSocialUserInfo> {
    return this.zaloService.getSocialUserInfo(accessToken, 'id,name,picture');
  }

  /**
   * Lấy thông tin chi tiết của người dùng
   * @param accessToken Access token của người dùng
   * @returns Thông tin chi tiết của người dùng
   * @throws AppException nếu có lỗi xảy ra
   */
  async getDetailedUserInfo(accessToken: string): Promise<ZaloSocialUserInfo> {
    return this.zaloService.getSocialUserInfo(
      accessToken,
      'id,name,picture,gender,birthday,location',
    );
  }

  /**
   * Kiểm tra tính hợp lệ của access token
   * @param accessToken Access token cần kiểm tra
   * @returns True nếu token hợp lệ, false nếu không
   */
  async validateToken(accessToken: string): Promise<boolean> {
    return this.zaloService.validateAccessToken(accessToken);
  }

  /**
   * Lấy thông tin người dùng với xử lý lỗi an toàn
   * @param accessToken Access token của người dùng
   * @returns Thông tin người dùng hoặc null nếu có lỗi
   */
  async getUserInfoSafely(accessToken: string): Promise<ZaloSocialUserInfo | null> {
    try {
      return await this.getUserInfo(accessToken);
    } catch (error) {
      this.logger.warn(`Không thể lấy thông tin người dùng: ${error.message}`);
      return null;
    }
  }

  /**
   * Làm mới access token với xử lý lỗi an toàn
   * @param appId ID của ứng dụng
   * @param refreshToken Refresh token
   * @returns Access token mới hoặc null nếu có lỗi
   */
  async refreshTokenSafely(
    appId: string,
    refreshToken: string,
  ): Promise<ZaloRefreshTokenResponse | null> {
    try {
      return await this.refreshAccessToken(appId, refreshToken);
    } catch (error) {
      this.logger.warn(`Không thể làm mới access token: ${error.message}`);
      return null;
    }
  }

  /**
   * Kiểm tra và làm mới token nếu cần thiết
   * @param appId ID của ứng dụng
   * @param accessToken Access token hiện tại
   * @param refreshToken Refresh token
   * @returns Token mới nếu đã làm mới, hoặc token cũ nếu vẫn hợp lệ
   */
  async ensureValidToken(
    appId: string,
    accessToken: string,
    refreshToken: string,
  ): Promise<{ accessToken: string; refreshToken?: string; isRefreshed: boolean }> {
    const isValid = await this.validateToken(accessToken);
    
    if (isValid) {
      return { accessToken, isRefreshed: false };
    }

    const newTokens = await this.refreshTokenSafely(appId, refreshToken);
    
    if (newTokens) {
      return {
        accessToken: newTokens.access_token,
        refreshToken: newTokens.refresh_token,
        isRefreshed: true,
      };
    }

    throw new AppException(
      ErrorCode.EXTERNAL_SERVICE_ERROR,
      'Không thể làm mới access token, vui lòng đăng nhập lại',
    );
  }
}
