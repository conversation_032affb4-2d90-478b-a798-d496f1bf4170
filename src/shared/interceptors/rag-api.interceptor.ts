import { Injectable, NestInterceptor, ExecutionContext, <PERSON>H<PERSON><PERSON>, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';

/**
 * Interceptor để tự động thêm X-API-Key header cho các request đến RAG API
 */
@Injectable()
export class RagApiInterceptor implements NestInterceptor {
  private readonly logger = new Logger(RagApiInterceptor.name);
  private readonly apiKey: string;
  private readonly baseUrl: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.apiKey = this.configService.get<string>('RAG_API', '');
    this.baseUrl = this.configService.get<string>('FAST_API_URL', 'http://localhost:8000/');
    
    if (!this.apiKey) {
      this.logger.warn('RAG API key is not configured');
    }
    
    if (!this.baseUrl) {
      this.logger.warn('Fast API URL is not configured');
    }
    
    // Thêm interceptor cho tất cả các request đến RAG API
    this.setupInterceptor();
  }

  /**
   * Thiết lập interceptor cho HttpService
   */
  private setupInterceptor(): void {
    this.httpService.axiosRef.interceptors.request.use(
      (config) => {
        // Chỉ thêm header cho các request đến RAG API
        if (config.url && config.url.startsWith(this.baseUrl)) {
          this.logger.debug(`Adding X-API-Key header to request: ${config.url}`);
          
          // Thêm X-API-Key header
          config.headers = config.headers || {};
          config.headers['X-API-Key'] = this.apiKey;
        }
        
        return config;
      },
      (error) => {
        this.logger.error(`Request interceptor error: ${error.message}`);
        return Promise.reject(error);
      },
    );
  }

  /**
   * Phương thức bắt buộc của NestInterceptor
   * Không làm gì trong trường hợp này vì chúng ta đã thiết lập interceptor cho HttpService
   */
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle();
  }
}
