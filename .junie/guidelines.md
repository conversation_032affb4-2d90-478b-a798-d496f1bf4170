# Hướng Dẫn Auto Code cho Agent trong Dự Án NestJS

Tài liệu này cung cấp các quy tắc và hướng dẫn để **agent** tự động tạo code cho dự án **NestJS** sử dụng **Layered Architecture**, **Domain-Driven Design (DDD)**, và **Repository Pattern**. Agent phải tuân thủ các quy tắc này để đảm bảo code nhất quán, d<PERSON> b<PERSON>o trì, và đúng cấu trúc.

## 1. Nhiệm Vụ của Agent
<PERSON><PERSON> nhận yêu cầu tạo **module** hoặc **tính năng**, agent phải:
1. **Xác định** module và các thành phần cần tạo (entity, repository, service, controller, DTO, exception).
2. **Tạo cấu trúc thư mục** theo quy tắc.
3. **Viết code** cho từng thành phần theo mẫu và quy tắc.
4. **Thêm JSDoc** ngắn gọn để mô tả.
5. **<PERSON><PERSON> lý lỗi** và validation đúng cách.
6. **Tái sử dụng** các thành phần từ `@common` và `@shared`.
7. **Comment Code** các commet đều phải là tiếng việt
7. Nếu yêu cầu không rõ, **hỏi lại** trước khi tạo code.

## 2. Cấu Trúc Thư Mục
Mỗi module là một **Bounded Context** với cấu trúc:

```
/[module]
  /admin
    /controllers    # Controller cho admin
    /services       # Service cho admin
    /dto            # DTO cho admin
  /user
    /controllers    # Controller cho user
    /services       # Service cho user
    /dto            # DTO cho user
  /entities         # Entity của module
  /repositories     # Repository của module
  /interfaces       # Interface và type
  /constants        # Hằng số và enum
  /exceptions       # Exception của module
```

### Thư Mục Chung
- **`@common/`**:
    - `/exceptions`: `AppException`, `ErrorCode`.
    - `/response`: `ApiResponseDto`.
    - `/swagger`: Cấu hình Swagger.
    - `/helpers`: Hàm hỗ trợ (ví dụ: `generateS3Key`).
    - `/interfaces`: Interface chung.
    - `/dto`: `QueryDto`, `SortDirection`.
- **`@shared/`**:
    - `/services`: Service chung (S3Service, OpenAiService).
    - `/utils`: Hàm tiện ích.

**Hành động của Agent**:
- Tạo thư mục theo cấu trúc trên cho module mới.
- Sử dụng các thành phần từ `@common` và `@shared` khi cần, đặc biệt là `generateS3Key` từ `@common/helpers`.

## 3. Quy Tắc Viết Code

### 3.1. Controller
- **Chức năng**: Xử lý routing, gọi service, trả response.
- **Quy tắc**:
    - Sử dụng `@ApiTags`, `@ApiOperation`, `@ApiResponse` để tài liệu hóa.
    - Chuẩn hóa response với `ApiResponseDto`.
    - Không chứa logic nghiệp vụ.
    - Bảo vệ API bằng guard (`JwtUserGuard` cho user, `JwtEmployeeGuard` cho admin).
    - Sử dụng `@ApiBearerAuth('JWT-auth')` cho API cần xác thực.
    - Response phải sử dụng DTO, không được trả về object entity trực tiếp.
    - Chuẩn hóa endpoint cho tôi như sau: user/endpoint, admin/endpoint
- **Ví dụ**:
```typescript
@ApiTags('User')
@Controller('user/resource')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class ResourceUserController {
  constructor(private readonly service: ResourceService) {}

  @Get()
  @ApiOperation({ summary: 'Get paginated resources' })
  async getResources(@Query() query: QueryResourceDto) {
    const result = await this.service.getResources(query);
    return ApiResponseDto.paginated(result);
  }
}
```

### 3.2. Service
- **Chức năng**: Chứa logic nghiệp vụ, gọi repository.
- **Quy tắc**:
    - Không truy vấn database trực tiếp.
    - Không được trả về Object Entities mà phải map sang các DTO tương ứng
    - Sử dụng transaction khi thao tác nhiều bảng.
    - Xử lý lỗi bằng `AppException` và mã lỗi từ `/exceptions`.
    - Ghi log cho các thao tác quan trọng.
    - Khi tạo key S3, sử dụng `generateS3Key` từ `@common/helpers`.
    - Không sửa trực tiếp entity, chỉ truyền dữ liệu qua DTO và repository.
    - Chuyển đổi dữ liệu từ entity sang DTO trước khi trả về cho controller.

- **Ví dụ**:
```typescript
@Injectable()
export class ResourceService {
  constructor(
    private readonly repository: ResourceRepository,
    private readonly s3Service: S3Service,
    private readonly logger: Logger,
  ) {}

  async getResources(query: QueryResourceDto) {
    try {
      return await this.repository.findPaginated(query);
    } catch (error) {
      this.logger.error(`Failed to get resources: ${error.message}`);
      throw new AppException(RESOURCE_ERROR_CODES.FETCH_FAILED, error.message);
    }
  }
}
```

### 3.3. Repository
- **Chức năng**: Truy vấn database, sử dụng `QueryBuilder`.
- **Quy tắc**:
    - Kế thừa `Repository<Entity>` của TypeORM.
    - Sử dụng `QueryBuilder` cho mọi truy vấn, không dùng `find()`, `findOne()`.
    - Tạo phương thức tái sử dụng (ví dụ: `createBaseQuery`).
    - Trả về `PaginatedResult<Entity>` cho query phân trang.
    - Chỉ chọn các trường được liệt kê trong DTO, không dùng `SELECT *`.
    - Sử dụng `@Transactional` cho Update và Delete.
    - Không sửa trực tiếp entity, chỉ thực hiện thao tác qua QueryBuilder.

- **Ví dụ**:
```typescript
@Injectable()
export class ResourceRepository extends Repository<Resource> {
  private createBaseQuery(): SelectQueryBuilder<Resource> {
    return this.createQueryBuilder('resource');
  }

  async findPaginated(query: QueryResourceDto): Promise<PaginatedResult<Resource>> {
    const fields = ['id', 'name', 'createdAt'];
    const qb = this.createBaseQuery()
      .select(fields.map(field => `resource.${field}`))
      .skip((query.page - 1) * query.limit)
      .take(query.limit)
      .orderBy(`resource.${query.sortBy}`, query.sortDirection);
    const [items, total] = await qb.getManyAndCount();
    return { items, total };
  }
}
```

### 3.4. Entity
- **Chức năng**: Định nghĩa schema database.
- **Quy tắc**:
    - Sử dụng decorator TypeORM (`@Entity`, `@Column`, `@PrimaryGeneratedColumn`).
    - Định nghĩa relationship (`@OneToMany`, `@ManyToOne`).
    - Thêm JSDoc cho mỗi trường.
    - Entities chỉ được sử dụng để đọc dữ liệu, không được sửa trực tiếp.
- **Ví dụ**:
```typescript
@Entity()
export class Resource {
  /** Unique identifier */
  @PrimaryGeneratedColumn()
  id: number;

  /** Resource name */
  @Column()
  name: string;
}
```

### 3.5. DTO
- **Chức năng**: Validate và transform dữ liệu đầu vào.
- **Quy tắc**:
    - Sử dụng `class-validator` và `class-transformer`.
    - Tách `CreateDto`, `UpdateDto`, `QueryDto`.
    - Extend `QueryDto` từ `@common/dto` cho DTO phân trang.
    - Sử dụng `@ApiProperty` để tài liệu hóa.
    - Tạo [Entity]ResponseDto để định dạng dữ liệu response, ánh xạ từ entity.

- **Ví dụ**:
```typescript
export enum ResourceSortBy {
  NAME = 'name',
  CREATED_AT = 'createdAt',
}

/**
 * DTO for querying resources
 */
export class QueryResourceDto extends QueryDto {
  @ApiProperty({ enum: ResourceSortBy, default: ResourceSortBy.CREATED_AT })
  @IsEnum(ResourceSortBy)
  @IsOptional()
  sortBy: ResourceSortBy = ResourceSortBy.CREATED_AT;
}
```

### 3.6. Exception
- **Chức năng**: Định nghĩa mã lỗi và xử lý lỗi.
- **Quy tắc**:
    - Tạo file: `/[module]/exceptions/[module].exception.ts`.
    - Định nghĩa `ErrorCode` trong dải mã lỗi của module (ví dụ: Agent: 10100-10199).
    - Đặt tên: `[MODULE_NAME]_[ACTION]_[ERROR_TYPE]`.
    - Export trong `index.ts`.
    - Thêm mã lỗi ENTITY_RESPONSE_FORBIDDEN cho trường hợp trả về entity trực tiếp.
    - message cho mã lỗi đều phải là tiếng việt
- **Ví dụ**:
```typescript
// src/modules/resource/exceptions/resource.exception.ts
import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

export const RESOURCE_ERROR_CODES = {
  FETCH_FAILED: new ErrorCode(
    10301,
    'Failed to fetch resources',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  NOT_FOUND: new ErrorCode(
    10302,
    'Resource not found',
    HttpStatus.NOT_FOUND,
  ),
  INVALID_DTO: new ErrorCode(
    10303,
    'Invalid DTO fields',
    HttpStatus.BAD_REQUEST,
  ),
  INVALID_S3_KEY: new ErrorCode(
    10304,
    'Invalid S3 key generation',
    HttpStatus.BAD_REQUEST,
  ),
};

// src/modules/resource/exceptions/index.ts
export * from './resource.exception';
```

## 4. Quy Tắc Xác Thực (Guard và JWT)
- **Quy tắc**:
    - Sử dụng `JwtUserGuard` cho user, `JwtEmployeeGuard` cho admin.
    - Thêm `@ApiBearerAuth('JWT-auth')` cho API cần xác thực.
    - Lấy thông tin từ JWT bằng decorator (`@CurrentUser('id')`, `@CurrentEmployee('id')`).
- **Ví dụ**:
```typescript
@Post()
@ApiBearerAuth('JWT-auth')
async create(
  @CurrentUser('id') userId: number,
  @Body() dto: CreateResourceDto,
) {
  return this.service.create(userId, dto);
}
```

## 5. Quy Tắc Phân Trang
- **Quy tắc**:
    - Sử dụng `QueryDto` từ `@common/dto` làm base.
    - Tạo enum `[Module]SortBy` nếu cần sắp xếp đặc thù.
    - Repository trả về `PaginatedResult<Entity>`.
    - Controller trả về `ApiResponseDto.paginated()`.
- **Ví dụ**:
```typescript
// Repository
async findPaginated(query: QueryResourceDto): Promise<PaginatedResult<Resource>> {
  // Logic phân trang
}

// Controller
@Get()
async getResources(@Query() query: QueryResourceDto) {
  const result = await this.service.getResources(query);
  return ApiResponseDto.paginated(result);
}
```

## 6. Quy Tắc Truy Vấn Dữ Liệu
- **Quy tắc**:
    - **Bắt buộc sử dụng `QueryBuilder`** cho mọi truy vấn (Select, Update, Delete).
    - **Không sử dụng `SELECT *`**: Chỉ lấy các trường được định nghĩa trong DTO hoặc yêu cầu cụ thể.
    - **Update và Delete**: Sử dụng decorator `@Transactional` từ `typeorm-transactional` để đảm bảo tính toàn vẹn dữ liệu.
    - Nếu DTO không khớp với entity (ví dụ: thiếu trường), ném `AppException` với mã lỗi phù hợp.
- **Ví dụ Select với QueryBuilder**:
```typescript
async findById(id: number, fields: string[]): Promise<Resource | null> {
  return this.createQueryBuilder('resource')
    .select(fields.map(field => `resource.${field}`))
    .where('resource.id = :id', { id })
    .getOne();
}
```

- **Ví dụ Update với @Transactional**:
```typescript
import { Transactional } from 'typeorm-transactional';

@Transactional()
async update(id: number, updateDto: UpdateResourceDto): Promise<void> {
  const fields = Object.keys(updateDto);
  const qb = this.createQueryBuilder()
    .update(Resource)
    .set(updateDto)
    .where('id = :id', { id });
  const result = await qb.execute();
  if (result.affected === 0) {
    throw new AppException(RESOURCE_ERROR_CODES.NOT_FOUND, `Resource with ID ${id} not found`);
  }
}
```

- **Ví dụ Delete với @Transactional**:
```typescript
import { Transactional } from 'typeorm-transactional';

@Transactional()
async delete(id: number): Promise<void> {
  const qb = this.createQueryBuilder()
    .delete()
    .from(Resource)
    .where('id = :id', { id });
  const result = await qb.execute();
  if (result.affected === 0) {
    throw new AppException(RESOURCE_ERROR_CODES.NOT_FOUND, `Resource with ID ${id} not found`);
  }
}
```

## 7. Quy Tắc Tạo Key S3
- **Chức năng**: Tạo key S3 để lưu trữ file trên AWS S3.
- **Quy tắc**:
    - Sử dụng hàm `generateS3Key` từ `@common/helpers`.
    - Cấu trúc key:
        - `baseFolder`: `employeeId.toString()` cho admin hoặc `userId.toString()` cho user.
        - `categoryFolder`: Sử dụng giá trị từ `CategoryFolderEnum` (ví dụ: `CategoryFolderEnum.AGENT`).
    - Validate `employeeId` hoặc `userId` trước khi tạo key:
        - Nếu không hợp lệ (null, undefined, hoặc không phải số), ném `AppException` với mã lỗi `INVALID_S3_KEY`.
    - Định nghĩa `CategoryFolderEnum` trong `/[module]/constants` nếu cần giá trị cụ thể.
- **Ví dụ**:
```typescript
import { generateS3Key } from '@common/helpers';
import { CategoryFolderEnum } from '../constants';
import { AppException } from '@common/exceptions';
import { RESOURCE_ERROR_CODES } from '../exceptions';

async createResource(userId: number, dto: CreateResourceDto) {
  if (!userId || isNaN(userId)) {
    throw new AppException(RESOURCE_ERROR_CODES.INVALID_S3_KEY, 'Invalid user ID for S3 key generation');
  }
  const iconKey = generateS3Key({
    baseFolder: userId.toString(),
    categoryFolder: CategoryFolderEnum.AGENT,
  });
  // Logic lưu file lên S3 với iconKey
}
```

- **Định nghĩa CategoryFolderEnum**:
```typescript
// src/modules/resource/constants/category-folder.enum.ts
export enum CategoryFolderEnum {
  AGENT = 'agent',
  RESOURCE = 'resource',
}
```

## 8. Checklist cho Agent
Khi tạo module hoặc tính năng, agent phải kiểm tra:
- [ ] Tạo đúng cấu trúc thư mục cho module.
- [ ] Tạo entity với JSDoc và decorator TypeORM.
- [ ] Tạo repository với `QueryBuilder` và phương thức tái sử dụng.
- [ ] Sử dụng `@Transactional` cho Update và Delete.
- [ ] Chỉ chọn các trường từ DTO trong truy vấn Select, không dùng `SELECT *`.
- [ ] Tạo DTO (`CreateDto`, `UpdateDto`, `QueryDto`) với validation và `@ApiProperty`.
- [ ] Tạo service với logic nghiệp vụ, xử lý lỗi bằng `AppException`.
- [ ] Tạo controller với guard, `@ApiBearerAuth('JWT-auth')`, và `ApiResponseDto`.
- [ ] Tạo file exception với mã lỗi trong dải được cấp, bao gồm `INVALID_S3_KEY`.
- [ ] Thêm JSDoc ngắn gọn cho mỗi file.
- [ ] Sử dụng thành phần từ `@common` và `@shared`.
- [ ] Đảm bảo code không chứa logic truy vấn trong service.
- [ ] Sử dụng `generateS3Key` với `baseFolder` và `categoryFolder` đúng quy tắc khi lưu trữ S3.

## 9. Xử Lý Khi Yêu Cầu Không Rõ
- Nếu yêu cầu thiếu thông tin (ví dụ: tên module, chức năng cụ thể), agent phải:
    - Trả lời: "Vui lòng cung cấp tên module và mô tả chức năng cụ thể để tôi tạo code."
    - Không tự giả định hoặc tạo code không đúng.

## 10. Xử Lý Tình Huống Đặc Biệt
- **Lỗi không mong muốn**: Nếu gặp lỗi không nằm trong danh sách mã lỗi đã định nghĩa, agent phải:
    - Ghi log chi tiết lỗi.
    - Ném `AppException` với mã lỗi chung (ví dụ: `GENERAL_ERROR`) và thông báo lỗi cụ thể.
- **Yêu cầu không rõ ràng**: Nếu yêu cầu thiếu thông tin, agent phải:
    - Trả lời: "Vui lòng cung cấp thêm thông tin về [thiếu sót cụ thể, ví dụ: tên module, chức năng]."
    - Không tự giả định hoặc tạo code không đúng.
- **DTO không khớp với Entity**: Nếu các trường trong DTO không tồn tại trong entity, agent phải:
    - Ném `AppException` với mã lỗi `INVALID_DTO` và thông báo chi tiết.
- **Truy vấn không hợp lệ**: Nếu `QueryBuilder` trả về lỗi cú pháp hoặc không tìm thấy bản ghi, agent phải:
    - Ghi log lỗi và ném `AppException` với mã lỗi phù hợp (ví dụ: `NOT_FOUND` hoặc `QUERY_FAILED`).
- **Lỗi tạo key S3**: Nếu `employeeId` hoặc `userId` không hợp lệ, agent phải:
    - Ném `AppException` với mã lỗi `INVALID_S3_KEY` và thông báo chi tiết.

**Ví dụ**:
- Nếu DTO chứa trường không tồn tại:
```typescript
if (!entityFields.includes(dtoField)) {
  throw new AppException(RESOURCE_ERROR_CODES.INVALID_DTO, `Field ${dtoField} does not exist in Resource entity`);
}
```
- Nếu userId không hợp lệ khi tạo key S3:
```typescript
if (!userId || isNaN(userId)) {
  throw new AppException(RESOURCE_ERROR_CODES.INVALID_S3_KEY, 'Invalid user ID for S3 key generation');
}
```

## 11. Ví Dụ Chi Tiết: Tạo Module "Resource"
Dưới đây là quy trình tạo module "Resource" từ đầu đến cuối, tích hợp các quy tắc truy vấn và tạo key S3:

### Bước 1: Tạo Cấu Trúc Thư Mục
Tạo thư mục `/resource` với các subfolders:
- `/admin/controllers`, `/admin/services`, `/admin/dto`
- `/user/controllers`, `/user/services`, `/user/dto`
- `/entities`, `/repositories`, `/interfaces`, `/constants`, `/exceptions`

### Bước 2: Tạo Constants
Tạo file `/resource/constants/category-folder.enum.ts`:
```typescript
/**
 * Enum for S3 category folders
 */
export enum CategoryFolderEnum {
  AGENT = 'agent',
  RESOURCE = 'resource',
}
```

### Bước 3: Tạo Entity
Tạo file `/resource/entities/resource.entity.ts`:
```typescript
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

/**
 * Resource entity
 */
@Entity()
export class Resource {
  /** Unique identifier */
  @PrimaryGeneratedColumn()
  id: number;

  /** Resource name */
  @Column()
  name: string;

  /** S3 key for resource file */
  @Column({ nullable: true })
  s3Key: string;

  /** Created at timestamp */
  @Column()
  createdAt: Date;
}
```

### Bước 4: Tạo Exception
Tạo file `/resource/exceptions/resource.exception.ts`:
```typescript
import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

export const RESOURCE_ERROR_CODES = {
  FETCH_FAILED: new ErrorCode(
    10301,
    'Không thể lấy danh sách tài nguyên',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  NOT_FOUND: new ErrorCode(
    10302,
    'Không tìm thấy tài nguyên',
    HttpStatus.NOT_FOUND,
  ),
  INVALID_DTO: new ErrorCode(
    10303,
    'Trường dữ liệu không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
  INVALID_S3_KEY: new ErrorCode(
    10304,
    'Tạo khóa S3 không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
};
```

### Bước 5: Tạo Repository
Tạo file `/resource/repositories/resource.repository.ts`:
```typescript
import { Injectable, Logger } from '@nestjs/common';
import { Repository, SelectQueryBuilder, DataSource } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { Resource } from '../entities/resource.entity';
import { QueryResourceDto } from '../user/dto/query-resource.dto';
import { PaginatedResult } from '@common/response';

@Injectable()
export class ResourceRepository extends Repository<Resource> {
  private readonly logger = new Logger(ResourceRepository.name);

  constructor(private dataSource: DataSource) {
    super(Resource, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho resource
   */
  private createBaseQuery(): SelectQueryBuilder<Resource> {
    return this.createQueryBuilder('resource');
  }

  /**
   * Tìm tài nguyên theo ID
   */
  async findById(id: number): Promise<Resource | null> {
    this.logger.log(`Tìm tài nguyên với ID: ${id}`);

    const qb = this.createBaseQuery()
      .select(['resource.id', 'resource.name', 'resource.s3Key', 'resource.createdAt'])
      .where('resource.id = :id', { id });

    return qb.getOne();
  }

  /**
   * Lấy danh sách tài nguyên có phân trang
   */
  async findPaginated(query: QueryResourceDto): Promise<PaginatedResult<Resource>> {
    this.logger.log(`Lấy danh sách tài nguyên có phân trang: ${JSON.stringify(query)}`);

    const fields = ['id', 'name', 's3Key', 'createdAt'];
    const qb = this.createBaseQuery()
      .select(fields.map(field => `resource.${field}`));

    // Thêm điều kiện tìm kiếm nếu có
    if (query.search) {
      qb.andWhere('resource.name LIKE :search', { search: `%${query.search}%` });
    }

    // Phân trang
    qb.skip((query.page - 1) * query.limit)
      .take(query.limit)
      .orderBy(`resource.${query.sortBy}`, query.sortDirection);

    const [items, total] = await qb.getManyAndCount();

    return {
      items,
      total,
      page: query.page,
      limit: query.limit,
      totalPages: Math.ceil(total / query.limit)
    };
  }
}
```

### Bước 6: Tạo DTO
Tạo file `/resource/user/dto/query-resource.dto.ts`:
```typescript
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { QueryDto } from '@common/dto';

export enum ResourceSortBy {
  NAME = 'name',
  CREATED_AT = 'createdAt',
}

/**
 * DTO cho truy vấn tài nguyên
 */
export class QueryResourceDto extends QueryDto {
  @ApiPropertyOptional({
    enum: ResourceSortBy,
    default: ResourceSortBy.CREATED_AT,
    description: 'Trường sắp xếp'
  })
  @IsEnum(ResourceSortBy)
  @IsOptional()
  sortBy: ResourceSortBy = ResourceSortBy.CREATED_AT;
}
```

Tạo file `/resource/user/dto/resource-response.dto.ts`:
```typescript
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Resource } from '../../entities/resource.entity';

/**
 * DTO phản hồi cho tài nguyên
 */
export class ResourceResponseDto {
  @ApiProperty({
    description: 'ID tài nguyên',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Tên tài nguyên',
    example: 'Tài nguyên mẫu'
  })
  name: string;

  @ApiPropertyOptional({
    description: 'URL xem tài nguyên',
    example: 'https://cdn.example.com/resources/123.jpg'
  })
  url?: string;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: '2023-01-01T00:00:00Z'
  })
  createdAt: Date;

  /**
   * Chuyển đổi từ entity sang DTO
   */
  static fromEntity(entity: Resource, url?: string): ResourceResponseDto {
    const dto = new ResourceResponseDto();
    dto.id = entity.id;
    dto.name = entity.name;
    dto.url = url;
    dto.createdAt = entity.createdAt;
    return dto;
  }
}
```

### Bước 7: Tạo Service
Tạo file `/resource/user/services/resource.service.ts`:
```typescript
import { Injectable, Logger } from '@nestjs/common';
import { ResourceRepository } from '../../repositories/resource.repository';
import { QueryResourceDto } from '../dto/query-resource.dto';
import { ResourceResponseDto } from '../dto/resource-response.dto';
import { AppException } from '@common/exceptions';
import { RESOURCE_ERROR_CODES } from '../../exceptions/resource.exception';
import { S3Service } from '@shared/services';
import { PaginatedResult } from '@common/response';
import { TimeIntervalEnum } from '@shared/utils';

@Injectable()
export class ResourceService {
  private readonly logger = new Logger(ResourceService.name);

  constructor(
    private readonly repository: ResourceRepository,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Lấy danh sách tài nguyên có phân trang
   */
  async getResources(query: QueryResourceDto): Promise<PaginatedResult<ResourceResponseDto>> {
    try {
      const result = await this.repository.findPaginated(query);

      // Chuyển đổi từ entity sang DTO và thêm URL
      const items = await Promise.all(
        result.items.map(async (resource) => {
          let url = undefined;
          if (resource.s3Key) {
            url = await this.s3Service.getSignedUrl(resource.s3Key, TimeIntervalEnum.ONE_HOUR);
          }
          return ResourceResponseDto.fromEntity(resource, url);
        })
      );

      return {
        items,
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách tài nguyên: ${error.message}`);
      throw new AppException(RESOURCE_ERROR_CODES.FETCH_FAILED, error.message);
    }
  }

  /**
   * Lấy thông tin tài nguyên theo ID
   */
  async getResourceById(id: number): Promise<ResourceResponseDto> {
    try {
      const resource = await this.repository.findById(id);

      if (!resource) {
        throw new AppException(RESOURCE_ERROR_CODES.NOT_FOUND, `Không tìm thấy tài nguyên với ID ${id}`);
      }

      let url = undefined;
      if (resource.s3Key) {
        url = await this.s3Service.getSignedUrl(resource.s3Key, TimeIntervalEnum.ONE_HOUR);
      }

      return ResourceResponseDto.fromEntity(resource, url);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy thông tin tài nguyên: ${error.message}`);
      throw new AppException(RESOURCE_ERROR_CODES.FETCH_FAILED, error.message);
    }
  }
}
```

### Bước 8: Tạo Controller
Tạo file `/resource/user/controllers/resource.controller.ts`:
```typescript
import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiExtraModels } from '@nestjs/swagger';
import { ResourceService } from '../services/resource.service';
import { QueryResourceDto } from '../dto/query-resource.dto';
import { ResourceResponseDto } from '../dto/resource-response.dto';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { JwtUserGuard } from '@shared/guards';
import { CurrentUser } from '@shared/decorators';

@ApiTags('User Resources')
@ApiExtraModels(ApiResponseDto, PaginatedResult, ResourceResponseDto)
@Controller('user/resources')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class ResourceUserController {
  constructor(private readonly service: ResourceService) {}

  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tài nguyên có phân trang' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tài nguyên',
    schema: ApiResponseDto.getPaginatedSchema(ResourceResponseDto)
  })
  async getResources(@Query() query: QueryResourceDto) {
    const result = await this.service.getResources(query);
    return ApiResponseDto.paginated(result);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin tài nguyên theo ID' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin tài nguyên',
    schema: ApiResponseDto.getSchema(ResourceResponseDto)
  })
  async getResourceById(@Param('id') id: number) {
    const result = await this.service.getResourceById(id);
    return ApiResponseDto.success(result);
  }
}
```

### Bước 9: Tạo Module
Tạo file `/resource/resource.module.ts`:
```typescript
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Resource } from './entities/resource.entity';
import { ResourceRepository } from './repositories/resource.repository';
import { ResourceService } from './user/services/resource.service';
import { ResourceUserController } from './user/controllers/resource.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([Resource]),
  ],
  controllers: [ResourceUserController],
  providers: [ResourceRepository, ResourceService],
  exports: [ResourceRepository, ResourceService],
})
export class ResourceModule {}
```

### Bước 10: Kiểm Tra Checklist
- [x] Tạo đúng cấu trúc thư mục cho module.
- [x] Tạo entity với JSDoc và decorator TypeORM.
- [x] Tạo repository với `QueryBuilder` và phương thức tái sử dụng.
- [x] Sử dụng `@Transactional` cho Update và Delete.
- [x] Chỉ chọn các trường từ DTO trong truy vấn Select, không dùng `SELECT *`.
- [x] Tạo DTO (`QueryDto`, `ResponseDto`) với validation và `@ApiProperty`.
- [x] Tạo service với logic nghiệp vụ, xử lý lỗi bằng `AppException`.
- [x] Tạo controller với guard, `@ApiBearerAuth('JWT-auth')`, và `ApiResponseDto`.
- [x] Tạo file exception với mã lỗi trong dải được cấp.
- [x] Thêm JSDoc ngắn gọn cho mỗi file.
- [x] Sử dụng thành phần từ `@common` và `@shared`.
- [x] Đảm bảo code không chứa logic truy vấn trong service.

## Kết luận

Tài liệu này cung cấp hướng dẫn chi tiết về cách tạo code cho dự án NestJS sử dụng Layered Architecture, Domain-Driven Design, và Repository Pattern. Bằng cách tuân thủ các quy tắc và mẫu được cung cấp, agent có thể tạo ra code chất lượng cao, nhất quán và dễ bảo trì.

Các điểm quan trọng cần nhớ:

1. **Phân tách trách nhiệm**: Mỗi lớp (controller, service, repository) có trách nhiệm riêng biệt.
2. **Sử dụng QueryBuilder**: Luôn sử dụng QueryBuilder thay vì các phương thức tìm kiếm có sẵn.
3. **Xử lý lỗi**: Sử dụng AppException với mã lỗi cụ thể.
4. **Tài liệu hóa API**: Sử dụng các decorator Swagger để tài liệu hóa API.
5. **Chuyển đổi dữ liệu**: Luôn chuyển đổi entity sang DTO trước khi trả về.
6. **Tái sử dụng code**: Sử dụng các thành phần từ @common và @shared.
7. **Kiểm tra kỹ lưỡng**: Sử dụng checklist để đảm bảo không bỏ sót bất kỳ thành phần nào.

Bằng cách tuân thủ hướng dẫn này, agent có thể tạo ra code chất lượng cao, đáp ứng các yêu cầu của dự án và dễ dàng mở rộng trong tương lai.