/**
 * React Hooks cho Digital Product Form
 * Bao gồm form state management, validation và API calls
 */

import { useState, useCallback, useEffect, useMemo } from 'react';
import {
  DigitalProductFormState,
  DigitalProductValidationErrors,
  DigitalClassificationFormItem,
  CustomFieldFormItem,
  CreateDigitalProductResponse,
  FileUploadProgress,
} from './digital-product-interfaces';
import {
  createInitialFormState,
  validateDigitalProductForm,
  transformFormStateToDto,
  createNewClassification,
  isFormValid,
  generateSku,
  isValidImageFile,
  isValidFileSize,
} from './digital-product-utils';

// ==================== FORM STATE HOOK ====================

/**
 * Hook quản lý state của form tạo sản phẩm số
 */
export const useDigitalProductForm = () => {
  const [formState, setFormState] = useState<DigitalProductFormState>(createInitialFormState);
  const [errors, setErrors] = useState<DigitalProductValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDirty, setIsDirty] = useState(false);

  // Update form field
  const updateField = useCallback(<K extends keyof DigitalProductFormState>(
    field: K,
    value: DigitalProductFormState[K]
  ) => {
    setFormState(prev => ({ ...prev, [field]: value }));
    setIsDirty(true);
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [errors]);

  // Update nested field (for price, digital settings, etc.)
  const updateNestedField = useCallback((
    parentField: keyof DigitalProductFormState,
    childField: string,
    value: any
  ) => {
    setFormState(prev => ({
      ...prev,
      [parentField]: {
        ...prev[parentField as any],
        [childField]: value,
      },
    }));
    setIsDirty(true);
  }, []);

  // Validate form
  const validateForm = useCallback(() => {
    const validationErrors = validateDigitalProductForm(formState);
    setErrors(validationErrors);
    return Object.keys(validationErrors).length === 0;
  }, [formState]);

  // Reset form
  const resetForm = useCallback(() => {
    setFormState(createInitialFormState());
    setErrors({});
    setIsDirty(false);
    setIsSubmitting(false);
  }, []);

  // Check if form is valid
  const isValid = useMemo(() => isFormValid(formState), [formState]);

  return {
    formState,
    errors,
    isSubmitting,
    isDirty,
    isValid,
    updateField,
    updateNestedField,
    validateForm,
    resetForm,
    setIsSubmitting,
  };
};

// ==================== CLASSIFICATIONS MANAGEMENT HOOK ====================

/**
 * Hook quản lý classifications trong form
 */
export const useClassificationsManager = (
  classifications: DigitalClassificationFormItem[],
  updateField: (field: 'classifications', value: DigitalClassificationFormItem[]) => void
) => {
  // Add new classification
  const addClassification = useCallback(() => {
    const newClassification = createNewClassification();
    updateField('classifications', [...classifications, newClassification]);
  }, [classifications, updateField]);

  // Remove classification
  const removeClassification = useCallback((index: number) => {
    if (classifications.length > 1) {
      const newClassifications = classifications.filter((_, i) => i !== index);
      updateField('classifications', newClassifications);
    }
  }, [classifications, updateField]);

  // Update classification
  const updateClassification = useCallback((
    index: number,
    field: keyof DigitalClassificationFormItem,
    value: any
  ) => {
    const newClassifications = [...classifications];
    newClassifications[index] = {
      ...newClassifications[index],
      [field]: value,
    };
    
    // Auto-generate SKU if name changes
    if (field === 'name' && value.trim()) {
      newClassifications[index].sku = generateSku(value, index);
    }
    
    updateField('classifications', newClassifications);
  }, [classifications, updateField]);

  // Duplicate classification
  const duplicateClassification = useCallback((index: number) => {
    const original = classifications[index];
    const duplicate = {
      ...original,
      id: `temp_${Date.now()}_${Math.random()}`,
      name: `${original.name} (Copy)`,
      sku: generateSku(`${original.name} Copy`, classifications.length),
    };
    
    const newClassifications = [...classifications];
    newClassifications.splice(index + 1, 0, duplicate);
    updateField('classifications', newClassifications);
  }, [classifications, updateField]);

  return {
    addClassification,
    removeClassification,
    updateClassification,
    duplicateClassification,
  };
};

// ==================== FILE UPLOAD HOOK ====================

/**
 * Hook quản lý upload files
 */
export const useFileUpload = () => {
  const [uploadProgress, setUploadProgress] = useState<Record<string, FileUploadProgress>>({});

  // Add files to upload queue
  const addFiles = useCallback((files: File[], category: 'product' | 'classification', classificationId?: string) => {
    const validFiles = files.filter(file => {
      if (!isValidImageFile(file)) {
        alert(`File ${file.name} không phải là hình ảnh hợp lệ`);
        return false;
      }
      if (!isValidFileSize(file, 5)) {
        alert(`File ${file.name} quá lớn (tối đa 5MB)`);
        return false;
      }
      return true;
    });

    const newProgress: Record<string, FileUploadProgress> = {};
    validFiles.forEach(file => {
      const key = `${category}_${classificationId || 'main'}_${file.name}_${Date.now()}`;
      newProgress[key] = {
        file,
        progress: 0,
        status: 'pending',
      };
    });

    setUploadProgress(prev => ({ ...prev, ...newProgress }));
    return validFiles;
  }, []);

  // Update upload progress
  const updateProgress = useCallback((key: string, progress: number, status?: FileUploadProgress['status']) => {
    setUploadProgress(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        progress,
        status: status || prev[key]?.status || 'uploading',
      },
    }));
  }, []);

  // Remove file from upload queue
  const removeFile = useCallback((key: string) => {
    setUploadProgress(prev => {
      const newProgress = { ...prev };
      delete newProgress[key];
      return newProgress;
    });
  }, []);

  // Clear all upload progress
  const clearProgress = useCallback(() => {
    setUploadProgress({});
  }, []);

  return {
    uploadProgress,
    addFiles,
    updateProgress,
    removeFile,
    clearProgress,
  };
};

// ==================== API HOOK ====================

/**
 * Hook gọi API tạo sản phẩm số
 */
export const useCreateDigitalProduct = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [response, setResponse] = useState<CreateDigitalProductResponse | null>(null);

  const createProduct = useCallback(async (
    formState: DigitalProductFormState,
    apiEndpoint: string = '/api/v1/user/products',
    authToken?: string
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      // Transform form state to DTO
      const dto = transformFormStateToDto(formState);

      // Make API call
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(authToken && { 'Authorization': `Bearer ${authToken}` }),
        },
        body: JSON.stringify(dto),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Có lỗi xảy ra khi tạo sản phẩm');
      }

      const data: CreateDigitalProductResponse = await response.json();
      setResponse(data);
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const reset = useCallback(() => {
    setIsLoading(false);
    setError(null);
    setResponse(null);
  }, []);

  return {
    createProduct,
    isLoading,
    error,
    response,
    reset,
  };
};

// ==================== CUSTOM FIELDS HOOK ====================

/**
 * Hook quản lý custom fields
 */
export const useCustomFields = (
  customFields: CustomFieldFormItem[],
  updateField: (field: 'customFields', value: CustomFieldFormItem[]) => void
) => {
  // Add custom field
  const addCustomField = useCallback((fieldId: number, fieldName?: string) => {
    const newField: CustomFieldFormItem = {
      fieldId,
      fieldName,
      fieldValue: '',
      isRequired: false,
    };
    updateField('customFields', [...customFields, newField]);
  }, [customFields, updateField]);

  // Remove custom field
  const removeCustomField = useCallback((index: number) => {
    const newFields = customFields.filter((_, i) => i !== index);
    updateField('customFields', newFields);
  }, [customFields, updateField]);

  // Update custom field
  const updateCustomField = useCallback((
    index: number,
    field: keyof CustomFieldFormItem,
    value: any
  ) => {
    const newFields = [...customFields];
    newFields[index] = {
      ...newFields[index],
      [field]: value,
    };
    updateField('customFields', newFields);
  }, [customFields, updateField]);

  return {
    addCustomField,
    removeCustomField,
    updateCustomField,
  };
};

// ==================== FORM PERSISTENCE HOOK ====================

/**
 * Hook lưu form state vào localStorage
 */
export const useFormPersistence = (
  formState: DigitalProductFormState,
  isDirty: boolean,
  storageKey: string = 'digitalProductForm'
) => {
  // Save to localStorage
  const saveToStorage = useCallback(() => {
    if (isDirty) {
      localStorage.setItem(storageKey, JSON.stringify(formState));
    }
  }, [formState, isDirty, storageKey]);

  // Load from localStorage
  const loadFromStorage = useCallback((): DigitalProductFormState | null => {
    try {
      const saved = localStorage.getItem(storageKey);
      return saved ? JSON.parse(saved) : null;
    } catch {
      return null;
    }
  }, [storageKey]);

  // Clear storage
  const clearStorage = useCallback(() => {
    localStorage.removeItem(storageKey);
  }, [storageKey]);

  // Auto-save every 30 seconds
  useEffect(() => {
    const interval = setInterval(saveToStorage, 30000);
    return () => clearInterval(interval);
  }, [saveToStorage]);

  // Save on page unload
  useEffect(() => {
    const handleBeforeUnload = () => saveToStorage();
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [saveToStorage]);

  return {
    saveToStorage,
    loadFromStorage,
    clearStorage,
  };
};
